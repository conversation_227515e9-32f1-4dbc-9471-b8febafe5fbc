import { Button } from "primereact/button";
import React from "react";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";

const EmptyPage = () => {

	const sendMail = () => {
		let data = [
			{
				"email": "<EMAIL>",
				"cc": [
					"<EMAIL>",
					"<PERSON>@tvsmotor.com",
					"<EMAIL>"
				],
				"name": "<PERSON><PERSON><PERSON>har<PERSON> (OPNS/Nalagarh/TVSMotor)",
				"subject": "Reminder to Review Assigned Sustainability Data Collection Form(s)",
				"body": "<div> <p>Dear Ya<PERSON><PERSON>udhary (OPNS/Nalagarh/TVSMotor)</p>\n                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>\n                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>\n\n                                  <table border=\"1\" cellpadding=\"6\" cellspacing=\"0\" style=\"border-collapse: collapse;\">\n          <thead>\n            <tr><th>Form</th><th>Entity</th><th>Reporting Period</th><th>Reporter</th><th>Reviewer</th><th>Approver</th><th>Status</th></tr>\n          </thead>\n        \n          <tbody>\n            <tr><td>Details of water disposal by  the reporting entity</td><td>Nalagarh</td><td>Jun-2025</td><td>Ravindra Payal (OPNS/Nalagarh/TVSMotor)</td><td>Yashpal Chaudhary (OPNS/Nalagarh/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Water Consumption</td><td>Nalagarh</td><td>Jun-2025</td><td>Ravindra Payal (OPNS/Nalagarh/TVSMotor)</td><td>Yashpal Chaudhary (OPNS/Nalagarh/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Details on sources of water withdrawal by the reporting entity</td><td>Nalagarh</td><td>Jun-2025</td><td>Ravindra Payal (OPNS/Nalagarh/TVSMotor)</td><td>Yashpal Chaudhary (OPNS/Nalagarh/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Air Emissions (other than GHG emissions) from the operations</td><td>Nalagarh</td><td>May-2025</td><td>Ravindra Payal (OPNS/Nalagarh/TVSMotor)</td><td>Yashpal Chaudhary (OPNS/Nalagarh/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Air Emissions (other than GHG emissions) from the operations</td><td>Nalagarh</td><td>Jun-2025</td><td>Ravindra Payal (OPNS/Nalagarh/TVSMotor)</td><td>Yashpal Chaudhary (OPNS/Nalagarh/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr>\n          </tbody>\n        </table>\n                                  <p>Please log in to the <a href=https://tvsmotor.eisqr.com>EiSqr – ESG Platform</a> to complete the review of the submitted data and supporting documents before the submission deadline.</p>\n<p>In case of any queries, raise a ticket or alternatively, write to us on <a href=\"mailto:<EMAIL>\" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>\n                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>\n                                </div>"
			},
			{
				"email": "<EMAIL>",
				"cc": [
					"<EMAIL>",
					"<EMAIL>"
				],
				"name": "K Prema(OPNS/Hosur/TVSMotor)",
				"subject": "Reminder to Review Assigned Sustainability Data Collection Form(s)",
				"body": "<div> <p>Dear K Prema(OPNS/Hosur/TVSMotor)</p>\n                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>\n                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>\n\n                                  <table border=\"1\" cellpadding=\"6\" cellspacing=\"0\" style=\"border-collapse: collapse;\">\n          <thead>\n            <tr><th>Form</th><th>Entity</th><th>Reporting Period</th><th>Reporter</th><th>Reviewer</th><th>Approver</th><th>Status</th></tr>\n          </thead>\n        \n          <tbody>\n            <tr><td>Details of procured electricity, steam, heating, and cooling services by the reporting entity</td><td>Mysore</td><td>Apr-2025</td><td>G Uthayakumar (OPNS/Hosur/TVSMotor)</td><td>K Prema(OPNS/Hosur/TVSMotor)</td><td></td><td>Pending  for data review</td></tr><tr><td>Details of procured electricity, steam, heating, and cooling services by the reporting entity</td><td>Mysore</td><td>May-2025</td><td>G Uthayakumar (OPNS/Hosur/TVSMotor)</td><td>K Prema(OPNS/Hosur/TVSMotor)</td><td></td><td>Pending  for data review</td></tr><tr><td>Details of procured electricity, steam, heating, and cooling services by the reporting entity</td><td>Mysore</td><td>Jun-2025</td><td>G Uthayakumar (OPNS/Hosur/TVSMotor)</td><td>K Prema(OPNS/Hosur/TVSMotor)</td><td></td><td>Pending  for data review</td></tr>\n          </tbody>\n        </table>\n                                  <p>Please log in to the <a href=https://tvsmotor.eisqr.com>EiSqr – ESG Platform</a> to complete the review of the submitted data and supporting documents before the submission deadline.</p>\n<p>In case of any queries, raise a ticket or alternatively, write to us on <a href=\"mailto:<EMAIL>\" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>\n                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>\n                                </div>"
			},
			{
				"email": "<EMAIL>",
				"cc": [
					"<EMAIL>",
					"<EMAIL>"
				],
				"name": "S Dayalan (Facility /Corp/TVSMotor)",
				"subject": "Reminder to Review Assigned Sustainability Data Collection Form(s)",
				"body": "<div> <p>Dear S Dayalan (Facility /Corp/TVSMotor)</p>\n                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>\n                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>\n\n                                  <table border=\"1\" cellpadding=\"6\" cellspacing=\"0\" style=\"border-collapse: collapse;\">\n          <thead>\n            <tr><th>Form</th><th>Entity</th><th>Reporting Period</th><th>Reporter</th><th>Reviewer</th><th>Approver</th><th>Status</th></tr>\n          </thead>\n        \n          <tbody>\n            <tr><td>Details of procured electricity, steam, heating, and cooling services by the reporting entity</td><td>Chaitanya (Chennai)</td><td>May-2025</td><td>S Dayalan (Facility /Corp/TVSMotor)</td><td>S Dayalan (Facility /Corp/TVSMotor)</td><td></td><td>Pending  for data review</td></tr>\n          </tbody>\n        </table>\n                                  <p>Please log in to the <a href=https://tvsmotor.eisqr.com>EiSqr – ESG Platform</a> to complete the review of the submitted data and supporting documents before the submission deadline.</p>\n<p>In case of any queries, raise a ticket or alternatively, write to us on <a href=\"mailto:<EMAIL>\" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>\n                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>\n                                </div>"
			},
			{
				"email": "<EMAIL>",
				"cc": [
					"<EMAIL>",
					"<EMAIL>",
					"<EMAIL>"
				],
				"name": "S Lohith (EM/Bangalore/TVSMotor)",
				"subject": "Reminder to Review Assigned Sustainability Data Collection Form(s)",
				"body": "<div> <p>Dear S Lohith (EM/Bangalore/TVSMotor)</p>\n                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>\n                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>\n\n                                  <table border=\"1\" cellpadding=\"6\" cellspacing=\"0\" style=\"border-collapse: collapse;\">\n          <thead>\n            <tr><th>Form</th><th>Entity</th><th>Reporting Period</th><th>Reporter</th><th>Reviewer</th><th>Approver</th><th>Status</th></tr>\n          </thead>\n        \n          <tbody>\n            <tr><td>Details on sources of water withdrawal by the reporting entity</td><td>Electronic City</td><td>Apr-2025</td><td>S Lohith (EM/Bangalore/TVSMotor)</td><td>S Lohith (EM/Bangalore/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Details on sources of water withdrawal by the reporting entity</td><td>Electronic City</td><td>May-2025</td><td>S Lohith (EM/Bangalore/TVSMotor)</td><td>S Lohith (EM/Bangalore/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr>\n          </tbody>\n        </table>\n                                  <p>Please log in to the <a href=https://tvsmotor.eisqr.com>EiSqr – ESG Platform</a> to complete the review of the submitted data and supporting documents before the submission deadline.</p>\n<p>In case of any queries, raise a ticket or alternatively, write to us on <a href=\"mailto:<EMAIL>\" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>\n                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>\n                                </div>"
			},
			{
				"email": "<EMAIL>",
				"cc": [
					"<EMAIL>",
					"<EMAIL>",
					"<EMAIL>"
				],
				"name": "Gobinath Kandasamy Rajavel(Sustainability/Hosur/TVSMotor)",
				"subject": "Reminder to Review Assigned Sustainability Data Collection Form(s)",
				"body": "<div> <p>Dear Gobinath Kandasamy Rajavel(Sustainability/Hosur/TVSMotor)</p>\n                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>\n                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>\n\n                                  <table border=\"1\" cellpadding=\"6\" cellspacing=\"0\" style=\"border-collapse: collapse;\">\n          <thead>\n            <tr><th>Form</th><th>Entity</th><th>Reporting Period</th><th>Reporter</th><th>Reviewer</th><th>Approver</th><th>Status</th></tr>\n          </thead>\n        \n          <tbody>\n            <tr><td>Air Emissions (other than GHG emissions) from the operations</td><td>Hosur</td><td>Apr-2025</td><td>Ayesha Mariyam(HRD/Hosur/TVSMotor)</td><td>Gobinath Kandasamy Rajavel(Sustainability/Hosur/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Air Emissions (other than GHG emissions) from the operations</td><td>Hosur</td><td>May-2025</td><td>Ayesha Mariyam(HRD/Hosur/TVSMotor)</td><td>Gobinath Kandasamy Rajavel(Sustainability/Hosur/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Air Emissions (other than GHG emissions) from the operations</td><td>Hosur</td><td>Jun-2025</td><td>Ayesha Mariyam(HRD/Hosur/TVSMotor)</td><td>Gobinath Kandasamy Rajavel(Sustainability/Hosur/TVSMotor)</td><td>M Cyril Packianathan (Sustainability/Hosur/TVSMotor)</td><td>Pending  for data review</td></tr>\n          </tbody>\n        </table>\n                                  <p>Please log in to the <a href=https://tvsmotor.eisqr.com>EiSqr – ESG Platform</a> to complete the review of the submitted data and supporting documents before the submission deadline.</p>\n<p>In case of any queries, raise a ticket or alternatively, write to us on <a href=\"mailto:<EMAIL>\" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>\n                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>\n                                </div>"
			},
			{
				"email": "<EMAIL>",
				"cc": [
					"<EMAIL>",
					"<EMAIL>",
					"<EMAIL>"
				],
				"name": "D Saravana Kumar (Sustainability/Hosur/TVSMotor)",
				"subject": "Reminder to Review Assigned Sustainability Data Collection Form(s)",
				"body": "<div> <p>Dear D Saravana Kumar (Sustainability/Hosur/TVSMotor)</p>\n                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>\n                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>\n\n                                  <table border=\"1\" cellpadding=\"6\" cellspacing=\"0\" style=\"border-collapse: collapse;\">\n          <thead>\n            <tr><th>Form</th><th>Entity</th><th>Reporting Period</th><th>Reporter</th><th>Reviewer</th><th>Approver</th><th>Status</th></tr>\n          </thead>\n        \n          <tbody>\n            <tr><td>Work-related ill health</td><td>Mysore</td><td>Apr-2025</td><td>BS Prakash (ERM/Mysore/TVSMotor)</td><td>D Saravana Kumar (Sustainability/Hosur/TVSMotor)</td><td>TN Sriramkumar (ERM /Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Work-related ill health</td><td>Mysore</td><td>May-2025</td><td>BS Prakash (ERM/Mysore/TVSMotor)</td><td>D Saravana Kumar (Sustainability/Hosur/TVSMotor)</td><td>TN Sriramkumar (ERM /Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Work-related ill health</td><td>Mysore</td><td>Jun-2025</td><td>BS Prakash (ERM/Mysore/TVSMotor)</td><td>D Saravana Kumar (Sustainability/Hosur/TVSMotor)</td><td>TN Sriramkumar (ERM /Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Employees / workers having suffered high consequence work- related injury / ill-health / fatalities,who have been are rehabilitated.</td><td>Mysore</td><td>Apr-2025</td><td>BS Prakash (ERM/Mysore/TVSMotor)</td><td>D Saravana Kumar (Sustainability/Hosur/TVSMotor)</td><td>TN Sriramkumar (ERM /Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Employees / workers having suffered high consequence work- related injury / ill-health / fatalities,who have been are rehabilitated.</td><td>Mysore</td><td>May-2025</td><td>BS Prakash (ERM/Mysore/TVSMotor)</td><td>D Saravana Kumar (Sustainability/Hosur/TVSMotor)</td><td>TN Sriramkumar (ERM /Hosur/TVSMotor)</td><td>Pending  for data review</td></tr><tr><td>Employees / workers having suffered high consequence work- related injury / ill-health / fatalities,who have been are rehabilitated.</td><td>Mysore</td><td>Jun-2025</td><td>BS Prakash (ERM/Mysore/TVSMotor)</td><td>D Saravana Kumar (Sustainability/Hosur/TVSMotor)</td><td>TN Sriramkumar (ERM /Hosur/TVSMotor)</td><td>Pending  for data review</td></tr>\n          </tbody>\n        </table>\n                                  <p>Please log in to the <a href=https://tvsmotor.eisqr.com>EiSqr – ESG Platform</a> to complete the review of the submitted data and supporting documents before the submission deadline.</p>\n<p>In case of any queries, raise a ticket or alternatively, write to us on <a href=\"mailto:<EMAIL>\" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>\n                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>\n                                </div>"
			}
		]
		for (const item of []) {
			APIServices.post(API.SendMail, { to: item.email, subject: item.subject, body: item.body, cc: item.cc })
		}
	}

	return (
		<div className="grid">
			<div className="col-12">
				<div className="card">
					<h5> Empty Page </h5> <p> Use this page to start from scratch and place your custom content. </p>
					<Button label='Upload' onClick={() => { sendMail() }} />
				</div>
			</div>
		</div>
	);
};

const comparisonFn = function (prevProps, nextProps) {
	return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(EmptyPage, comparisonFn);
