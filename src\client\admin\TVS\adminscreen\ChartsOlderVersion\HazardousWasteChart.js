import React from "react";
import {
  Composed<PERSON><PERSON>,
  <PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import { Button } from "primereact/button";
import { useState, useRef } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

const data = [
  {
    year: "2019",
    preparedReuse: 20,
    recycled: 30,
    otherRecovery: 10,
    incineration: 15,
    transferLandfill: 25,
    otherDisposal: 10,
    incinerationEnergy: 40,
    total: 150,
  },
  {
    year: "2020",
    preparedReuse: 25,
    recycled: 20,
    otherRecovery: 15,
    incineration: 20,
    transferLandfill: 30,
    otherDisposal: 10,
    incinerationEnergy: 70,
    total: 190,
  },
  {
    year: "2021",
    preparedReuse: 30,
    recycled: 25,
    otherRecovery: 10,
    incineration: 15,
    transferLandfill: 20,
    otherDisposal: 15,
    incinerationEnergy: 30,
    total: 145,
  },
  {
    year: "2022",
    preparedReuse: 35,
    recycled: 20,
    otherRecovery: 15,
    incineration: 10,
    transferLandfill: 25,
    otherDisposal: 10,
    incinerationEnergy: 60,
    total: 175,
  },
  {
    year: "2023",
    preparedReuse: 40,
    recycled: 30,
    otherRecovery: 20,
    incineration: 10,
    transferLandfill: 20,
    otherDisposal: 15,
    incinerationEnergy: 20,
    total: 155,
  },
];

const renderCustomLegend = (props) => {
  const { payload } = props;
  return (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, 15em)",
        gap: 10,
        color: "#4A4A4A",
        position: "relative",
        top: -200,
        right: -800,
      }}
    >
      {payload.map((entry, index) => (
        <div key={`item-${index}`} style={{ color: "black", fontSize: "14px" }}>
          <span
            style={{
              color: entry.color,
              marginRight: 4,
              fontSize: "20px",
            }}
          >
            ■
          </span>
          {entry.value}
        </div>
      ))}
    </div>
  );
};

const HazardousWasteChart = () => {
  const [activeMode, setActiveMode] = useState(true);
  const dt = useRef(null);

  return (
    <div>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            fontFamily: "Lato",
            fontSize: "16px",
            fontWeight: 700,
            lineHeight: "19.2px",
            textAlign: "left",
            margin: "18px 10px 18px 10px",
          }}
        >
          Hazardous waste by disposal method{" "}
        </div>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <input
            type="month"
            style={{
              padding: "3px",
              borderRadius: "8px",
              width: "15rem",
              border: "1px solid grey",
              height: "30px",
              fontFamily: "lato",
            }}
          />
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19 " />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
        </div>
      </div>
      {activeMode && (
        <div style={{ overflow: "hidden" }}>
          <ResponsiveContainer height={500} width={700}>
            <ComposedChart data={data}>
              <CartesianGrid stroke="#f5f5f5" />
              <XAxis dataKey="year" />
              <YAxis />
              <Tooltip />

              {/* Stacked Bars */}
              <Bar
                dataKey="preparedReuse"
                stackId="a"
                fill="#00634F"
                name="Prepared for reuse"
                barSize={60}
              />
              <Bar
                dataKey="recycled"
                stackId="a"
                fill="#73C3B3"
                name="Recycled"
              />
              <Bar
                dataKey="otherRecovery"
                stackId="a"
                fill="#B0E0D7"
                name="Other recovery treatment"
              />
              <Bar
                dataKey="incinerationEnergy"
                stackId="a"
                fill="#EE5724"
                name="Incineration (With Energy)"
              />
              <Bar
                dataKey="incineration"
                stackId="a"
                fill="#FF9878"
                name="Incineration"
              />
              <Bar
                dataKey="transferLandfill"
                stackId="a"
                fill="#F9DF7F"
                name="Transfer to landfill"
              />
              <Bar
                dataKey="otherDisposal"
                stackId="a"
                fill="#F9DF7F"
                name="Other disposal treatments"
              />

              {/* Line for Total */}
              <Line
                dataKey="total"
                stroke="#667085"
                strokeWidth={2}
                name="Total"
              />

              <Legend content={renderCustomLegend} />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      )}
      {!activeMode && (
        <div>
          <DataTable ref={dt} value={data} tableClassName="font-lato">
            <Column
              header="Year"
              style={{ minWidth: "8%" }}
              field="year"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Prepared Re-Use"
              style={{ minWidth: "8%" }}
              field="preparedReuse"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Recycled"
              style={{ minWidth: "8%" }}
              field="recycled"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Other Recovery"
              style={{ minWidth: "8%" }}
              field="otherRecovery"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Incineration Energy"
              style={{ minWidth: "8%" }}
              field="incinerationEnergy"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Incineration"
              style={{ minWidth: "8%" }}
              field="incineration"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Transfer Land Fill"
              style={{ minWidth: "8%" }}
              field="transferLandfill"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Other Disposal"
              style={{ minWidth: "8%" }}
              field="otherDisposal"
              emptyMessage="No Assignment(s)"
            />
            <Column
              header="Total"
              style={{ minWidth: "8%" }}
              field="total"
              emptyMessage="No Assignment(s)"
            />
          </DataTable>
        </div>
      )}
    </div>
  );
};

export default HazardousWasteChart;
