import React, { useState, useEffect, useMemo } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Dropdown } from "primereact/dropdown";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const MSIActionStatusChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filterOptions = [
    { label: 'All Categories', value: 'all' },
    { label: 'Opportunity for Improvement', value: 'opportunity' },
    { label: 'Regulatory Major', value: 'regulatory_major' },
    { label: 'Regulatory Minor', value: 'regulatory_minor' },
    { label: 'Minor Non-compliance', value: 'minor_non_compliance' }
  ];

  // Process supply data to extract action status information
  useEffect(() => {
    const fetchActionStatusData = async () => {
      setLoading(true);
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];

        let pendingActionCount = 0;
        let completedActionCount = 0;

        // Use the same logic as Dashboard.js for counting actions
        allAssignments.forEach(item => {
          if (item?.auditorAssignmentSubmission &&
              (item?.auditorAssignmentSubmission.type === 1 || item?.auditorAssignmentSubmission.type === 2) &&
              Array.isArray(item.supplierActions) && item.supplierActions.length > 0) {

            // Filter actions by selected category
            const filteredActions = item.supplierActions.filter(action => {
              if (selectedFilter === 'all') {
                // Include only the specific categories as requested:
                // - Opportunity for Improvement (categoryOfFinding = 2)
                // - Regulatory Major Non-compliance (categoryOfFinding = 3, nonComplianceType = 1)
                // - Regulatory Minor Non-compliance (categoryOfFinding = 3, nonComplianceType = 2)
                // - Minor Non-compliance (categoryOfFinding = 3, nonComplianceType = 3 or undefined)
                return action.categoryOfFinding === 2 || // Opportunity for Improvement
                       (action.categoryOfFinding === 3 && action.nonComplianceType === 1) || // Regulatory Major
                       (action.categoryOfFinding === 3 && action.nonComplianceType === 2) || // Regulatory Minor
                       (action.categoryOfFinding === 3 && action.nonComplianceType === 3) || // Minor Non-compliance
                       (action.categoryOfFinding === 3 && !action.nonComplianceType); // Default to minor if type not specified
              } else if (selectedFilter === 'opportunity') {
                return action.categoryOfFinding === 2;
              } else if (selectedFilter === 'regulatory_major') {
                return action.categoryOfFinding === 3 && action.nonComplianceType === 1;
              } else if (selectedFilter === 'regulatory_minor') {
                return action.categoryOfFinding === 3 && action.nonComplianceType === 2;
              } else if (selectedFilter === 'minor_non_compliance') {
                return action.categoryOfFinding === 3 && (action.nonComplianceType === 3 || !action.nonComplianceType);
              }
              return false;
            });

            // Count individual pending actions (same logic as Dashboard.js)
            const pendingActions = filteredActions.filter(action =>
              action.status !== 'completed' && action.status !== 'submitted'
            );
            pendingActionCount += pendingActions.length;

            // Count individual completed actions (same logic as Dashboard.js)
            const completedActions = filteredActions.filter(action =>
              parseInt(action.type) === 3
            );
            completedActionCount += completedActions.length;
          }
        });

        const processedData = [
          { status: "Pending Actions", count: pendingActionCount, color: "#F59E0B" },
          { status: "Completed Actions", count: completedActionCount, color: "#22C55E" }
        ];

        console.log('MSIActionStatusChart - Final processed data:', processedData);
        console.log('MSIActionStatusChart - Action counts:', {
          pendingActionCount,
          completedActionCount,
          selectedFilter
        });
        setChartData(processedData);
      } catch (error) {
        console.error('Error fetching action status data:', error);
        // Fallback to empty data on error to maintain consistency
        setChartData([
          { status: "Actions Released", count: 0, color: "#3B82F6" },
          { status: "Actions Closed", count: 0, color: "#22C55E" }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchActionStatusData();
  }, [selectedFilter]);

  // Highcharts configuration
  const chartOptions = useMemo(() => ({
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Arial, sans-serif'
      }
    },
    title: {
      text: 'Action Status Overview',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333'
      }
    },
    xAxis: {
      categories: chartData.map(item => item.status),
      labels: {
        style: {
          fontSize: '12px',
          color: '#666'
        },
        formatter: function() {
          // Word wrap long labels
          const words = this.value.split(' ');
          if (words.length > 2) {
            const mid = Math.ceil(words.length / 2);
            return words.slice(0, mid).join(' ') + '<br/>' + words.slice(mid).join(' ');
          }
          return this.value;
        },
        useHTML: true
      },
      gridLineWidth: 0,
      lineColor: '#e0e0e0'
    },
    yAxis: {
      title: {
        text: 'Number of Actions',
        style: {
          fontSize: '12px',
          color: '#666'
        }
      },
      labels: {
        style: {
          fontSize: '11px',
          color: '#666'
        }
      },
      gridLineColor: '#f0f0f0',
      min: 0
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ccc',
      borderRadius: 8,
      shadow: true,
      style: {
        fontSize: '12px'
      },
      formatter: function() {
        return `<b>${this.x}</b><br/>Count: <b>${this.y}</b>`;
      }
    },
    plotOptions: {
      column: {
        maxPointWidth: 80,
        borderWidth: 0,
        borderRadius: 4,
        dataLabels: {
          enabled: true,
          style: {
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#333'
          }
        }
      }
    },
    legend: {
      enabled: false
    },
    series: [{
      name: 'Actions',
      data: chartData.map(item => ({
        y: item.count,
        color: item.color
      })),
      colorByPoint: true
    }],
    credits: {
      enabled: false
    },
    responsive: {
      rules: [{
        condition: {
          maxWidth: 500
        },
        chartOptions: {
          plotOptions: {
            column: {
              maxPointWidth: 60
            }
          },
          xAxis: {
            labels: {
              style: {
                fontSize: '11px'
              }
            }
          }
        }
      }]
    }
  }), [chartData]);

  // Calculate completion rate
  const completionRate = chartData.length > 0 ?
    ((chartData.find(item => item.status === "Completed Actions")?.count || 0) /
     ((chartData.find(item => item.status === "Pending Actions")?.count || 0) +
      (chartData.find(item => item.status === "Completed Actions")?.count || 0)) * 100).toFixed(1) : 0;

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #007bff", paddingBottom: "5px" }}>
          Action Status Overview
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Dropdown
            value={selectedFilter}
            options={filterOptions}
            onChange={(e) => setSelectedFilter(e.value)}
            placeholder="Filter by Category"
            style={{ minWidth: '200px' }}
          />
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {loading ? (
        <div style={{
          height: '400px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: '16px',
          color: '#666'
        }}>
          Loading action status data...
        </div>
      ) : activeMode ? (
        <div style={{ height: '400px', width: '100%' }}>
          <HighchartsReact
            highcharts={Highcharts}
            options={chartOptions}
            containerProps={{ style: { height: '100%', width: '100%' } }}
          />
        </div>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="status" header="Status" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Action Status Summary</h4>
        <div style={{ display: "flex", justifyContent: "space-between", flexWrap: "wrap" }}>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Total Pending</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#F59E0B" }}>
              {chartData.find(item => item.status === "Pending Actions")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Total Completed</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#22C55E" }}>
              {chartData.find(item => item.status === "Completed Actions")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Completion Rate</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#8B5CF6" }}>
              {completionRate}%
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "120px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Filter Applied</p>
            <p style={{ margin: "0", fontSize: "14px", fontWeight: "bold", color: "#666" }}>
              {filterOptions.find(opt => opt.value === selectedFilter)?.label || 'All Categories'}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MSIActionStatusChart;
