import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const data = [
  {
    year: "2020",
    men_jg1: 40,
    men_jg2: 60,
    men_jg3: 50,
    men_jg4: 30,
    men_jg5: 70,
    men_jg6: 40,
    women_jg1: 20,
    women_jg2: 40,
    women_jg3: 30,
    women_jg4: 25,
    women_jg5: 50,
    women_jg6: 30,
  },
  {
    year: "2021",
    men_jg1: 45,
    men_jg2: 65,
    men_jg3: 55,
    men_jg4: 35,
    men_jg5: 75,
    men_jg6: 45,
    women_jg1: 25,
    women_jg2: 45,
    women_jg3: 35,
    women_jg4: 30,
    women_jg5: 55,
    women_jg6: 35,
  },
  {
    year: "2022",
    men_jg1: 50,
    men_jg2: 70,
    men_jg3: 60,
    men_jg4: 40,
    men_jg5: 80,
    men_jg6: 50,
    women_jg1: 30,
    women_jg2: 50,
    women_jg3: 40,
    women_jg4: 35,
    women_jg5: 60,
    women_jg6: 40,
  },
  {
    year: "2023",
    men_jg1: 55,
    men_jg2: 75,
    men_jg3: 65,
    men_jg4: 45,
    men_jg5: 85,
    men_jg6: 55,
    women_jg1: 35,
    women_jg2: 55,
    women_jg3: 45,
    women_jg4: 40,
    women_jg5: 65,
    women_jg6: 45,
  },
];

const DiversityByJobGrade = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderChart();
  }, []);

  const renderChart = () => {
    const width = 700;
    const height = 500;
    const margin = { top: 50, right: 200, bottom: 150, left: 60 };

    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const keys = [
      "men_jg1",
      "men_jg2",
      "men_jg3",
      "men_jg4",
      "men_jg5",
      "men_jg6",
      "women_jg1",
      "women_jg2",
      "women_jg3",
      "women_jg4",
      "women_jg5",
      "women_jg6",
    ];

    const stackedData = d3.stack().keys(keys)(data);

    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.year))
      .range([0, chartWidth])
      .padding(0.4);

    const y = d3
      .scaleLinear()
      .domain([0, d3.max(stackedData[stackedData.length - 1], (d) => d[1])])
      .nice()
      .range([chartHeight, 0]);

    const color = d3.scaleOrdinal().domain(keys).range([
      "#cfe2f3", // Light blue
      "#fce2b3", // Light yellow
      "#d9e3f5", // Soft lavender
      "#f8d7da", // Soft red
      "#c6d9f1", // Soft blue
      "#fbe4d5", // Soft peach
    ]);

    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(x));

    g.append("g").call(d3.axisLeft(y));

    g.append("text")
      .attr("x", chartWidth / 2)
      .attr("y", chartHeight + 40)
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text("Year");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -chartHeight / 2)
      .attr("y", -40)
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text("Number of Employees");

    g.selectAll("g.stack")
      .data(stackedData)
      .enter()
      .append("g")
      .attr("fill", (d) => color(d.key))
      .selectAll("rect")
      .data((d) => d)
      .enter()
      .append("rect")
      .attr("x", (d) => x(d.data.year))
      .attr("y", (d) => y(d[1]))
      .attr("height", (d) => y(d[0]) - y(d[1]))
      .attr("width", x.bandwidth()) // Adjust the width for each year (stacked bars)
      .append("title")
      .text((d) => `${d.key}: ${d[1] - d[0]}`);

    // Adjusting legend to lower position
    const legend = svg
      .append("g")
      .attr(
        "transform",
        `translate(${width - margin.right + 20},${margin.top})`
      ); // Position legends to the right of the graph

    color.domain().forEach((key, index) => {
      legend
        .append("rect")
        .attr("x", 0)
        .attr("y", index * 25) // Adjust spacing between legend items
        .attr("width", 15)
        .attr("height", 15)
        .attr("fill", color(key));

      legend
        .append("text")
        .attr("x", 20)
        .attr("y", index * 25 + 12) // Align text with the corresponding rectangle
        .style("font-size", "14px")
        .style("font-family", "Arial, sans-serif")
        .style("fill", "#333")
        .text(key.replace("_", " ").replace("jg", "Job Grade"));
    });
  };

  return (
    <>
      <h3 style={{ fontSize: "18px" }}>Diversity of Employees by Job Grades</h3>
      <div>
        Number and percentage of men and women employees in different job grades
        (6 job grades shown by the stacked bars).
      </div>
      <div
        ref={chartRef}
        style={{ display: "flex", justifyContent: "center", marginTop: "20px" }}
      />
    </>
  );
};

export default DiversityByJobGrade;
