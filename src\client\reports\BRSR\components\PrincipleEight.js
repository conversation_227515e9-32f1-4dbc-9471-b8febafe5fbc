import React from "react";

const PrincipleEight = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          PRINCIPLE 8- BUSINESSES SHOULD PROMOTE INCLUSIVE GROWTH AND EQUITABLE
          DEVELOPMENT
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          1. Details of Social Impact Assessments (SIA) of projects undertaken
          by the entity based on applicable laws, in the current financial year.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Name and brief details of project</th>
              <th>SIA Notification No.</th>
              <th>Date of notification</th>
              <th>Whether conducted by independent external agency (Yes/No)</th>
              <th>Results communicated in public domain (Yes/No)</th>
              <th>Relevant Web link</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Provide information on project(s) for which ongoing Rehabilitation
          and Resettlement (R&amp;R) is being undertaken by your entity
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>S. No.</th>
              <th>Name of Project for which R&amp;R is ongoing</th>
              <th>State</th>
              <th>District</th>
              <th>No. of Project Affected Families (PAFs)</th>
              <th>% of PAFs covered by R&amp;R</th>
              <th>Amounts paid to PAFs in the FY (In INR)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Describe the mechanisms to receive and redress grievances of the
          community.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. Percentage of input material (inputs to total inputs by value)
          sourced from suppliers:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "40%" }}>&nbsp;</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Directly sourced from MSMEs/ small producers
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Sourced directly from within the district and neighboring
                districts*
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <p
          style={{ fontSize: "0.9em", fontStyle: "italic", marginTop: "0.5em" }}
        >
          *- Procured within India
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{ fontWeight: "normal", color: "black", marginBottom: "1rem" }}
        >
          5. Job creation in smaller towns – Disclose wages paid to persons
          employed (including employees or workers employed on a permanent or
          non-permanent / on contract basis) in the following locations, as % of
          total wage cost
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "40%" }}>Location</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>Rural</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Semi-urban</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Urban</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Metropolitan</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          1. Provide details of actions taken to mitigate any negative social
          impacts identified in the Social
        </p>
        <p
          style={{
            textAlign: "center",
            fontWeight: "bold",
            color: "black",
            marginBottom: "2rem",
          }}
        >
          Impact Assessments (Reference: Question 1 of Essential indicators
          above):
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Details of negative social impact identified.</th>
              <th>Corrective action taken</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          2. Provide the following information on CSR projects undertaken by
          your entity in designated aspirational districts as identified by
          government bodies
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>S No.</th>
              <th>State</th>
              <th>Aspirational district</th>
              <th>Amount spent (in Rs.)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. a) Do you have a preferential procurement policy where you give
          preference to purchase from suppliers comprising marginalized
          /vulnerable groups? (Yes/No)
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "black",
            marginBottom: "1rem",
            marginLeft: "1rem",
          }}
        >
          (b) From which marginalized /vulnerable groups do you procure?
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "black",
            marginBottom: "1rem",
            marginLeft: "1rem",
          }}
        >
          (c) What percentage of total procurement (by value) does it
          constitute?
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          4. Details of the benefits derived and shared from the intellectual
          properties owned or acquired by your entity (in the current financial
          year), based on traditional knowledge:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>S No.</th>
              <th>Intellectual Property based on traditional knowledge</th>
              <th>Owned/ Acquired (Yes/No)</th>
              <th>Benefit shared (Yes / No)</th>
              <th>Basis of calculating benefit share</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          5. Details of corrective actions taken or underway, based on any
          adverse order in intellectual property related disputes wherein usage
          of traditional knowledge is involved.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Name of authority</th>
              <th>Brief of the Case</th>
              <th>Corrective action taken</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          6. Details of beneficiaries of CSR projects
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>S. No.</th>
              <th>CSR Projects (in FY 2023-24)</th>
              <th>No. of persons benefitted from CSR Projects</th>
              <th>
                % Of beneficiaries from vulnerable and marginalized groups
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>2</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>3</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>4</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>5</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>6</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>7</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>8</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>9</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PrincipleEight;
