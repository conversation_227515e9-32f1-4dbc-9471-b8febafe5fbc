import React, { useEffect, useState, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  CartesianGrid,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import CriticalNonCompliances from "./NonComplianceComponent";
import NonComplianceDetailsDialog from "./NonComplianceDetailsDialog";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const SubGraph4Demo = ({ supplyData }) => {
  const [chartData, setChartData] = useState([]);
  const [nonComplianceCount, setNonComplianceCount] = useState(0);
  const [nonComplianceData, setNonComplianceData] = useState([]);
  const [dialogVisible, setDialogVisible] = useState(false);

  useEffect(() => {
    const fetchSocialNonComplianceCount = async () => {
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];

        // Group by vendorCode
        const groupedByVendor = allAssignments.reduce((acc, item) => {
          if (!item.vendorCode) return acc;
          if (!acc[item.vendorCode]) acc[item.vendorCode] = [];
          acc[item.vendorCode].push(item);
          return acc;
        }, {});

        let totalSocialNonCompliances = 0;
        const allSocialNonCompliances = [];

        Object.values(groupedByVendor).forEach(assignments => {
          const latest = assignments.sort((a, b) =>
            new Date(b.created_on) - new Date(a.created_on)
          )[0];

          const responseStr = latest?.auditorAssignmentSubmission?.response;
          if (!responseStr) return;

          let sectionMap = [];

          try {
            const auditorResponse = JSON.parse(responseStr);
            sectionMap = auditorResponse.flatMap(section =>
              (section.assessmentSubSection1s || []).map(sub => ({
                ...sub,
                order: section.order,
                esg: section.order === 1 ? 1 : section.order >= 5 ? 3 : 2
              }))
            );
          } catch (e) {
            console.error("Invalid response JSON for auditorAssignmentSubmission", e);
            return;
          }

          const getESG = (subsection2Id) => {
            return sectionMap.find(s => s.id === subsection2Id)?.esg || null;
          };

          const actions = (latest.supplierActions || []).map(action => ({
            ...action,
            esg: getESG(action.assessmentSubSection2Id),
            vendorName: latest?.vendor?.supplierName || 'Unknown Supplier',
            vendorLocation: latest?.vendor?.supplierLocation || latest?.location || 'Unknown Location',
            msiId: latest?.msiId,
            auditStartDate: latest?.auditStartDate,
            auditEndDate: latest?.auditEndDate
          }));

          const socialNonConformances = actions.filter(
            a => a.categoryOfFinding === 3 && a.esg === 2
          );

          totalSocialNonCompliances += socialNonConformances.length;
          allSocialNonCompliances.push(...socialNonConformances);
        });

        setNonComplianceCount(totalSocialNonCompliances);
        setNonComplianceData(allSocialNonCompliances);
      } catch (error) {
        console.error("Error fetching social non-compliances:", error);
        setNonComplianceCount(0);
      }
    };

    fetchSocialNonComplianceCount();
  }, [supplyData]);

  // Memoized chart data
  const memoizedChartData = useMemo(() => {
    if (supplyData.length === 0) return [];

    const totalSuppliers = supplyData.length;

    const totalHealthSafety = supplyData.reduce(
      (sum, item) => sum + (parseFloat(item.health_safety) || 0),
      0
    );

    const totalSocialStewardship = supplyData.reduce(
      (sum, item) => sum + (parseFloat(item.social_stewardship_framework) || 0),
      0
    );

    const totalSustainability = supplyData.reduce(
      (sum, item) => sum + (parseFloat(item.supplier_sustainability_ambassadorship_framework) || 0),
      0
    );

    const avgHealthSafety = (totalHealthSafety / totalSuppliers).toFixed(1);
    const avgSocialStewardship = (totalSocialStewardship / totalSuppliers).toFixed(1);
    const avgSustainability = (totalSustainability / totalSuppliers).toFixed(1);

    return [
      {
        category: "Occupational Health & Safety",
        maxScore: 20 - avgHealthSafety,
        avgScore: avgHealthSafety,
      },
      {
        category: "Supplier Social Stewardship Framework",
        maxScore: 10 - avgSocialStewardship,
        avgScore: avgSocialStewardship,
      },
      {
        category: "Supplier Sustainability Ambassadorship Framework",
        maxScore: 20 - avgSustainability,
        avgScore: avgSustainability,
      },
    ];
  }, [supplyData]);

  useEffect(() => {
    setChartData(memoizedChartData);
  }, [memoizedChartData]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{ backgroundColor: "#fff", border: "1px solid #ccc", borderRadius: "8px", padding: "10px", fontSize: "14px" }}>
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map(entry => (
            <p key={entry.name} style={{ margin: 0 }}>{`${entry.name}: ${entry.name === "Maximum" ? 20 : entry.value}`}</p>
          ))}
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }) => (
    <ul style={{ display: "flex", listStyleType: "none", justifyContent: "center", padding: 0, marginTop: "10px" }}>
      {payload.map((entry, index) => (
        <li key={`item-${index}`} style={{ marginRight: "5px" }}>
          <span style={{ backgroundColor: entry.color, width: "10px", height: "10px", borderRadius: "50%", display: "inline-block", marginRight: 4 }}></span>
          <span style={{ fontSize: "14px" }}>{entry.value}</span>
        </li>
      ))}
    </ul>
  );

  const CustomizedTick = ({ x, y, payload }) => {
    const wrapText = (text, width = 18) => {
      const words = text.split(" ");
      const lines = [];
      let line = "";

      words.forEach(word => {
        if ((line + " " + word).length > width) {
          lines.push(line);
          line = word;
        } else {
          line += (line ? " " : "") + word;
        }
      });
      lines.push(line);

      return lines.map((line, i) => <tspan key={i} x="0" dy={i === 0 ? 0 : 10}>{line}</tspan>);
    };

    return (
      <g transform={`translate(${x},${y})`}>
        <text x={0} y={0} textAnchor="middle" fontSize={11} fill="#666" dominantBaseline="hanging">
          {wrapText(payload.value)}
        </text>
      </g>
    );
  };

  return (
    <div className="container mt-4">
      <h5 className="mb-3 text-center text-dark">Social Section Performance</h5>

      <ResponsiveContainer width="100%" height={400}>
        <BarChart barSize={50} data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="category" tick={<CustomizedTick />} interval={0} />
          <YAxis domain={[0, 20]} />
          <Tooltip content={CustomTooltip} />
          <Legend content={CustomLegend} />
          <Bar dataKey="avgScore" stackId="a" fill="#FC6E51" name="Achieved">
            <LabelList dataKey="avgScore" position="insideBottom" style={{ fontSize: "12px", fill: "white" }} />
          </Bar>
          <Bar dataKey="maxScore" stackId="a" fill="#FEB2A8" name="Maximum" />
        </BarChart>
      </ResponsiveContainer>

      <div className="col-12 flex justify-content-center">
        <CriticalNonCompliances
          count={nonComplianceCount}
          onClick={() => setDialogVisible(true)}
        />
      </div>

      {/* Non-Compliance Details Dialog */}
      <NonComplianceDetailsDialog
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        nonComplianceData={nonComplianceData}
        title="Social Non-Compliance Details"
        esgType="Social"
      />
    </div>
  );
};

export default SubGraph4Demo;
