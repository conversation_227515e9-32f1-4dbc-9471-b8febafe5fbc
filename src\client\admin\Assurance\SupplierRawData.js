import React, { useEffect, useRef, useState } from 'react'
import APIServices from '../../../service/APIService'
import { API } from '../../../constants/api_url'
import { red } from '@mui/material/colors';
import { MultiSelect } from 'primereact/multiselect';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { useSelector } from 'react-redux';
import { filterDataByTierAndLocationByLevel, getFiscalYearsFromStartDate } from '../../../components/BGHF/helper';
import {
  Tab,
  Tabs,
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import { Dropdown } from 'primereact/dropdown';
import { Badge } from 'primereact/badge';
import XlsxPopulate from 'xlsx-populate';
import { DateTime } from 'luxon';
import { saveAs } from 'file-saver';
import Swal from 'sweetalert2';

export const SupplierRawData = ({ admin }) => {
  const [attributeColumn, setAttributeTableColumn] = useState([])
  const [load, setLoad] = useState(true)
  const [currentTabTitle, setCurrentTabTitle] = useState('')
  const [filteredSrf, setFilteredSrf] = useState([])
  const [supplierSubmission, setSupplierSubmission] = useState({ current: [], backup: [] })
  const [attribute, setAttribute] = useState([{ "title": "ATTRIBUTE 1", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Fuel Name", "header": "Fuel Name" }, { "name": "SDP78_1Total Consumption", "header": "Total Consumption" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_1Data Source", "header": "Data Source" }, { "name": "SDP78_2Type of Biomass Fuel", "header": "Type of Biomass Fuel" }, { "name": "SDP78_2Quantity of Fuel Consumed", "header": "Quantity of Fuel Consumed" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_2Type of Biofuel", "header": "Type of Biofuel" }, { "name": "SDP78_2Quantity of Fuel Consumption", "header": "Quantity of Fuel Consumption" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_2Type of Refrigerant", "header": "Type of Refrigerant" }, { "name": "SDP78_2Quantity of Refrigerant Consumed", "header": "Quantity of Refrigerant Consumed" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Electricity Source", "header": "Electricity Source" }, { "name": "SDP78_1Electricity Consumption", "header": "Electricity Consumption" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_6Name of the Third-party", "header": "Name of the Third-party" }, { "name": "SDP78_6Emission Factor of the Power Procured from Third Party (tCO2e/kWh)", "header": "Emission Factor of the Power Procured from Third Party (tCO2e/kWh)" }] }, { "title": "ATTRIBUTE 2", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Source of Water", "header": "Source of Water" }, { "name": "SDP78_1Quantity of Water Withdrawn", "header": "Quantity of Water Withdrawn" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_6Water Consumption Category", "header": "Water Consumption Category" }, { "name": "SDP78_6Quantity of Water", "header": "Quantity of Water" }, { "name": "SDP78_6Unit", "header": "Unit" }, { "name": "SDP78_1Water Discharge Level", "header": "Water Discharge Level" }, { "name": "SDP78_1Quantity of Water Discharged", "header": "Quantity of Water Discharged" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_1Remarks", "header": "Remarks" }, { "name": "SDP78_1Water Recycled and Reused", "header": "Water Recycled and Reused" }, { "name": "SDP78_1Quantity", "header": "Quantity" }, { "name": "SDP78_1Unit", "header": "Unit" }] }, { "title": "ATTRIBUTE 4", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Type of Waste Generated", "header": "Type of Waste Generated" }, { "name": "SDP78_1 Quantity of Waste Generated", "header": " Quantity of Waste Generated" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_2Waste Category", "header": "Waste Category" }, { "name": "SDP78_2Type of Waste", "header": "Type of Waste" }, { "name": "SDP78_2Quantity of Waste Generated", "header": "Quantity of Waste Generated" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Type of Waste", "header": "Type of Waste" }, { "name": "SDP78_1Quantity of Waste Recovered", "header": "Quantity of Waste Recovered" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_2Category of Waste", "header": "Category of Waste" }, { "name": "SDP78_2Type of Waste Recovered", "header": "Type of Waste Recovered" }, { "name": "SDP78_2Quantity of Waste Recovered", "header": "Quantity of Waste Recovered" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Type of Waste", "header": "Type of Waste" }, { "name": "SDP78_1Quantity of Waste Incinerated", "header": "Quantity of Waste Incinerated" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_2Waste Category", "header": "Waste Category" }, { "name": "SDP78_2Type of Waste Incinerated", "header": "Type of Waste Incinerated" }, { "name": "SDP78_2Quantity of Waste Incinerated", "header": "Quantity of Waste Incinerated" }, { "name": "SDP78_2Unit", "header": "Unit" }] }, { "title": "ATTRIBUTE 5", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Details of safety related incidents ", "header": "Details of safety related incidents " }, { "name": "SDP78_1Reported Output", "header": "Reported Output" }, { "name": "SDP78_1Unit", "header": "Unit" }] }, { "title": "ATTRIBUTE 6", data: [] }, { "title": "ATTRIBUTE 7", data: [] }, { "title": "ATTRIBUTE 8", data: [] }, { "title": "ATTRIBUTE 9", data: [] }])

  useEffect(() => {
    renderSupplierResponse()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Set initial data for the first tab when data is loaded
  useEffect(() => {
    if (!load && supplierSubmission.current.length > 0 && attribute.length > 0) {
      const firstAttribute = attribute[0];
      setAttributeTableColumn(firstAttribute.data);
      setCurrentTabTitle(firstAttribute.title);
      setFilteredSrf(
        supplierSubmission.current
          .filter(x => x.title === firstAttribute.title)
          ?.flatMap(item => item.data.map(x => ({
            ...x,
            vendorCode: item.vendorCode,
            supplierName: item.supplierName,
            reportingFrom: item.reportingFrom,
            reportingTo: item.reportingTo
          })))
      );
    }
  }, [load, supplierSubmission.current, attribute])
  const renderSupplierResponse = async () => {
    setLoad(true)
    try {
      const promise3 = await APIServices.get(API.ValueChainSubmission_UP(admin.id) +
        `?filter=${encodeURIComponent(JSON.stringify({ where: { srfId: 78 }, fields: { id: true, vendorId: true,vendorCode: true, response: true, reporting_period: true, type: true }, include: [{ relation: 'vendor', scope: { fields: { supplierName: true, code: true } } }] }))}`)
      const groupedData = groupSubmissionIntoAttributes(promise3.data)

      setSupplierSubmission({ current: groupedData, backup: groupedData })


    } catch {

    } finally {
      setLoad(false)
    }


  }
  const groupSubmissionIntoAttributes = (submissions) => {
    const combinedData = submissions.flatMap(submission => submission?.response?.map(x => ({ ...x,vendorCode:submission.vendorCode, vendorId: submission.vendorId, supplierName: submission?.vendor?.supplierName, reporting_period: submission.reporting_period })) || []);

    const result = [];
    let currentAttribute = null;
console.log(combinedData)
    combinedData.forEach((item) => {
      if (item.type === "paragraph" && item.label.includes("ATTRIBUTE")) {
        if (currentAttribute) {
          result.push(currentAttribute);
        }

        currentAttribute = {
          title: item.label?.split(':')[0],
          vendorId: item.vendorId,
          vendorCode:item.vendorCode,
          supplierName: item.supplierName,
          reportingFrom: DateTime.fromFormat(item?.reporting_period[0] || null, 'MM-yyyy',).toFormat('LLL-yyyy'),
          reportingTo: DateTime.fromFormat(item?.reporting_period.slice(-1)[0] || null, 'MM-yyyy',).toFormat('LLL-yyyy'),
          data: []
        };
      } else if (currentAttribute) {
        const result = parseData(item)
        if (Array.isArray(result) && result.length) {
          currentAttribute.data.push(...result.filter(x => Object.keys(x).length));
        }

      }
    });

    if (currentAttribute) {
      result.push(currentAttribute);
    }

    return result;
  };
  const parseData = (obj) => {
    if (!obj || !['table', 'tableadd'].includes(obj.type) || !obj.data) return [];

    return obj.data.map(row => {
      let extractedRow = {};

      if (obj.type === 'table') {
        obj.value.forEach(key => {
          const cell = row[key];
          const uniqueKey = (obj?.name || '') + key
          if (cell) {
            if (cell.type === 4) {
              // Type 4: Extract label from values based on value match
              const selectedValue = cell.data?.values?.find(v => v.value === cell.data.value);
              extractedRow[uniqueKey] = selectedValue ? selectedValue.label : '';
            } else if (cell.type === 5) {
              // Type 5: Take label from data
              extractedRow[uniqueKey] = cell.data?.label || '';
            } else if (cell.type === 3) {
              // Type 3: Take value directly
              extractedRow[uniqueKey] = cell.data?.value || 0;
            } else if (cell.type === 1) {
              // Type 1: Take value directly
              extractedRow[uniqueKey] = cell.data?.value || '';
            } else {
              extractedRow[uniqueKey] = '';
            }
          }
        });
      }

      if (obj.type === 'tableadd') {
        for (const [key, value] of Object.entries(row)) {
          const uniqueKey = (obj?.name || '') + key
          if (value?.data && value?.data.value != null) {
            if (value.type === 4) {
              // Match value to the corresponding label
              const match = value.data?.values?.find(v => v.value === value.data.value);
              extractedRow[uniqueKey] = match ? match.label : null;
            } else {
              extractedRow[uniqueKey] = value.data.value;
            }
          }
        }
      }

      return extractedRow;
    });
  };
  // Function to export current tab data to Excel
  const exportToExcel = async () => {
    if (!filteredSrf || filteredSrf.length === 0) {
      // Use SweetAlert for better user experience
      Swal.fire({
        icon: 'warning',
        title: 'No Data',
        text: 'No data to export for the current tab',
        timer: 2000
      });
      return;
    }



    // Show loading dialog while export is in progress
    const loadingDialog = Swal.fire({
      title: 'Exporting Data',
      html: 'Please wait while the data is being exported...',
      allowOutsideClick: false,
      allowEscapeKey: false,
      allowEnterKey: false,
      showConfirmButton: false,
      willOpen: () => {
        Swal.showLoading();
      }
    });

    try {
      // Create a new workbook
      const workbook = await XlsxPopulate.fromBlankAsync();
      const sheet = workbook.sheet(0);

      // Set sheet name to the current tab title
      // Handle sheet name carefully to avoid Excel errors
      let sheetName = currentTabTitle || 'Supplier Data';

      // Excel has strict rules for sheet names:
      // 1. Cannot contain these characters: \ / * ? [ ] :
      // 2. Cannot exceed 31 characters
      // 3. Cannot be blank

      // Replace invalid characters with underscores
      sheetName = sheetName.replace(/[\\\/\*\?\[\]\:]/g, '_');

      // Ensure name is not empty
      if (!sheetName.trim()) {
        sheetName = 'Supplier_Data';
      }

      // Limit to 31 characters
      sheetName = sheetName.substring(0, 31);

      // Try to set the sheet name
      try {
        sheet.name(sheetName);
      } catch (e) {
        // If still fails, use a simple fallback name
        console.warn(`Failed to set sheet name "${sheetName}". Using fallback name.`);
        sheet.name('Supplier_Data');
      }

      // Add headers
      const headers = ['Vendor Code', 'Supplier', 'Reporting From', 'Reporting To', ...attributeColumn.map(col => col.header)];
      headers.forEach((header, colIndex) => {
        sheet.cell(1, colIndex + 1).value(header).style({
          bold: true,
          fill: 'D3D3D3',
          horizontalAlignment: 'center'
        });
      });

      // Add data rows
      filteredSrf.forEach((row, rowIndex) => {
        // Add vendor code, supplier and reporting period data
        sheet.cell(rowIndex + 2, 1).value(row.vendorCode || '');
        sheet.cell(rowIndex + 2, 2).value(row.supplierName || '');
        sheet.cell(rowIndex + 2, 3).value(row.reportingFrom || '');
        sheet.cell(rowIndex + 2, 4).value(row.reportingTo || '');

        // Add attribute data
        attributeColumn.forEach((col, colIndex) => {
          sheet.cell(rowIndex + 2, colIndex + 5).value(row[col.name] !== undefined ? row[col.name] : '');
        });
      });

      // Auto-size columns
      for (let i = 1; i <= headers.length; i++) {
        const columnWidth = Math.max(
          headers[i - 1].length,
          ...filteredSrf.map(row => {
            const value = i <= 4
              ? (i === 1 ? row.vendorCode : i === 2 ? row.supplierName : i === 3 ? row.reportingFrom : row.reportingTo)
              : row[attributeColumn[i - 5].name];
            return value !== undefined && value !== null ? String(value).length : 0;
          })
        );
        sheet.column(i).width(columnWidth + 2);
      }

      // Generate blob and save file
      const blob = await workbook.outputAsync();
      saveAs(new Blob([blob]), `${currentTabTitle || 'Supplier_Data'}_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.xlsx`);

      // Close the loading dialog
      Swal.close();
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      // Close the loading dialog first
      Swal.close();
      // Then show error message
      Swal.fire({
        icon: 'error',
        title: 'Export Failed',
        text: 'Failed to export data. Please try again.'
      });
    }
  };

  // Function to export all tabs to Excel without confirmation dialog
  const exportAllToExcel = async () => {
    // Show loading dialog while export is in progress
    const loadingDialog = Swal.fire({
      title: 'Exporting All Data',
      html: 'Please wait while all data is being exported...',
      allowOutsideClick: false,
      allowEscapeKey: false,
      allowEnterKey: false,
      showConfirmButton: false,
      willOpen: () => {
        Swal.showLoading();
      }
    });

    try {
      // First, collect all the data we need to export
      const sheetsToExport = [];

      // Process each attribute/tab
      for (const attr of attribute) {
        // Skip if no data for this attribute
        const tabData = supplierSubmission.current
          .filter(x => x.title === attr.title)
          ?.flatMap(item => item.data.map(x => ({
            ...x,
            vendorCode:item.vendorCode,
            supplierName: item.supplierName,
            reportingFrom: item.reportingFrom,
            reportingTo: item.reportingTo
          })));

        if (!tabData || tabData.length === 0) continue;

        // Create a safe sheet name
        let sheetName = attr.title || `Sheet_${sheetsToExport.length + 1}`;

        // Replace invalid characters with underscores
        sheetName = sheetName.replace(/[\\\/\*\?\[\]\:\&\+\-\=\@\#\$\%\^\(\)\!]/g, '_');

        // Ensure name is not empty and doesn't start with a number
        if (!sheetName.trim() || /^\d/.test(sheetName)) {
          sheetName = `Sheet_${sheetsToExport.length + 1}`;
        }

        // Limit to 31 characters
        sheetName = sheetName.substring(0, 31);

        // Store the data for this sheet
        sheetsToExport.push({
          name: sheetName,
          data: tabData,
          headers: ['Vendor Code', 'Supplier', 'Reporting From', 'Reporting To', ...attr.data.map(col => col.header)],
          columns: attr.data
        });
      }

      // If no data to export, show a message and return
      if (sheetsToExport.length === 0) {
        Swal.fire({
          icon: 'warning',
          title: 'No Data',
          text: 'No data to export',
          timer: 2000
        });
        return;
      }

      // Create a new workbook
      const workbook = await XlsxPopulate.fromBlankAsync();

      // Process the first sheet (use the existing Sheet1)
      const firstSheet = workbook.sheet(0);
      firstSheet.name(sheetsToExport[0].name);

      // Add headers to first sheet
      sheetsToExport[0].headers.forEach((header, colIndex) => {
        firstSheet.cell(1, colIndex + 1).value(header).style({
          bold: true,
          fill: 'D3D3D3',
          horizontalAlignment: 'center'
        });
      });

      // Add data rows to first sheet
      sheetsToExport[0].data.forEach((row, rowIndex) => {
        // Add vendor code, supplier and reporting period data
        firstSheet.cell(rowIndex + 2, 1).value(row.vendorCode || '');
        firstSheet.cell(rowIndex + 2, 2).value(row.supplierName || '');
        firstSheet.cell(rowIndex + 2, 3).value(row.reportingFrom || '');
        firstSheet.cell(rowIndex + 2, 4).value(row.reportingTo || '');

        // Add attribute data
        sheetsToExport[0].columns.forEach((col, colIndex) => {
          firstSheet.cell(rowIndex + 2, colIndex + 5).value(row[col.name] !== undefined ? row[col.name] : '');
        });
      });

      // Auto-size columns for first sheet
      for (let i = 1; i <= sheetsToExport[0].headers.length; i++) {
        const columnWidth = Math.max(
          sheetsToExport[0].headers[i - 1].length,
          ...sheetsToExport[0].data.map(row => {
            const value = i <= 4
              ? (i === 1 ? row.vendorCode : i === 2 ? row.supplierName : i === 3 ? row.reportingFrom : row.reportingTo)
              : row[sheetsToExport[0].columns[i - 5]?.name];
            return value !== undefined && value !== null ? String(value).length : 0;
          })
        );
        firstSheet.column(i).width(columnWidth + 2);
      }

      // Process remaining sheets one by one
      for (let i = 1; i < sheetsToExport.length; i++) {
        try {
          // Create a unique sheet name to avoid conflicts
          const uniqueName = `Sheet${i + 1}`;
          const newSheet = workbook.addSheet(uniqueName);

          // Try to rename the sheet with our desired name
          try {
            newSheet.name(sheetsToExport[i].name);
          } catch (e) {
            console.warn(`Could not set sheet name to "${sheetsToExport[i].name}". Using "${uniqueName}" instead.`);
          }

          // Add headers
          sheetsToExport[i].headers.forEach((header, colIndex) => {
            newSheet.cell(1, colIndex + 1).value(header).style({
              bold: true,
              fill: 'D3D3D3',
              horizontalAlignment: 'center'
            });
          });

          // Add data rows
          sheetsToExport[i].data.forEach((row, rowIndex) => {
            // Add vendor code, supplier and reporting period data
            newSheet.cell(rowIndex + 2, 1).value(row.vendorCode || '');
            newSheet.cell(rowIndex + 2, 2).value(row.supplierName || '');
            newSheet.cell(rowIndex + 2, 3).value(row.reportingFrom || '');
            newSheet.cell(rowIndex + 2, 4).value(row.reportingTo || '');

            // Add attribute data
            sheetsToExport[i].columns.forEach((col, colIndex) => {
              newSheet.cell(rowIndex + 2, colIndex + 5).value(row[col.name] !== undefined ? row[col.name] : '');
            });
          });

          // Auto-size columns
          for (let j = 1; j <= sheetsToExport[i].headers.length; j++) {
            const columnWidth = Math.max(
              sheetsToExport[i].headers[j - 1].length,
              ...sheetsToExport[i].data.map(row => {
                const value = j <= 4
                  ? (j === 1 ? row.vendorCode : j === 2 ? row.supplierName : j === 3 ? row.reportingFrom : row.reportingTo)
                  : row[sheetsToExport[i].columns[j - 5]?.name];
                return value !== undefined && value !== null ? String(value).length : 0;
              })
            );
            newSheet.column(j).width(columnWidth + 2);
          }
        } catch (sheetError) {
          console.error(`Error creating sheet ${i + 1}:`, sheetError);
          // Continue with other sheets
        }
      }

      // Generate blob and save file with all sheets
      const blob = await workbook.outputAsync();
      saveAs(new Blob([blob]), `Supplier_Data_All_Attributes_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.xlsx`);

      // Close the loading dialog
      Swal.close();
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      // Close the loading dialog first
      Swal.close();
      // Then show error message
      Swal.fire({
        icon: 'error',
        title: 'Export Failed',
        text: 'Failed to export data. Please try again.'
      });
    }
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h4>Supplier Data</h4>
        <Button
          disabled={load}
          onClick={exportAllToExcel}
          label="Export All Tabs"
          icon="pi pi-download"
          className="p-button-primary mr-3"
        />

      </div>

      {!load && <TabsComponent data={attribute} onChange={(e) => {
        setAttributeTableColumn(e.data);
        setCurrentTabTitle(e.title);
        setFilteredSrf(supplierSubmission.current.filter(x => x.title === e.title)?.flatMap(item => item.data.map(x => ({ ...x,vendorCode: item.vendorCode, supplierName: item.supplierName, reportingFrom: item.reportingFrom, reportingTo: item.reportingTo }))))
      }} tabLabelKey={'title'} />}

      <div className="d-flex justify-content-between align-items-center mb-3">
        <h4>{currentTabTitle || 'Supplier Data'}</h4>
        <Button
          disabled={filteredSrf.length === 0 || load}
          onClick={exportToExcel}
          label={"Export " + currentTabTitle}
          icon="pi pi-download"
          className="p-button-primary mr-3"
        />

      </div>

      <DataTable
        value={filteredSrf}
        scrollable
        loading={load}
        className="custom-datatable"
        emptyMessage="No data available"
        filterDisplay="menu"
      >
        
        <Column
          field="vendorCode"
          header="Vendor Code"
          style={{ minWidth: '150px' }}
          sortable
          filter
          filterPlaceholder="Search Code"
          filterMenuStyle={{ width: '14rem' }}
          filterElement={(options) => (
            <MultiSelect
              value={options.value}
              options={[...new Set(filteredSrf.map(item => item.vendorCode))].filter(Boolean)}
              onChange={(e) => options.filterCallback(e.value)}
              placeholder="Select Vendor Code"
              className="p-column-filter"
              maxSelectedLabels={1}
              style={{ width: '100%' }}
              panelClassName="hidefilter"
            />
          )}
          showFilterMatchModes={false}
          filterMatchMode="in"
        />
        <Column
          field="supplierName"
          header="Supplier"
          style={{ minWidth: '150px' }}
          sortable
          filter
          filterPlaceholder="Search suppliers"
          filterMenuStyle={{ width: '14rem' }}
          filterElement={(options) => (
            <MultiSelect
              value={options.value}
              options={[...new Set(filteredSrf.map(item => item.supplierName))].filter(Boolean)}
              onChange={(e) => options.filterCallback(e.value)}
              placeholder="Select suppliers"
              className="p-column-filter"
              maxSelectedLabels={1}
              style={{ width: '100%' }}
              panelClassName="hidefilter"
            />
          )}
          showFilterMatchModes={false}
          filterMatchMode="in"
        />
        <Column
          field="reportingFrom"
          header="Reporting From"
          style={{ minWidth: '150px' }}
          sortable
          filter
          filterPlaceholder="Search periods"
          filterMenuStyle={{ width: '14rem' }}
          filterElement={(options) => (
            <MultiSelect
              value={options.value}
              options={[...new Set(filteredSrf.map(item => item.reportingFrom))].filter(Boolean)}
              onChange={(e) => options.filterCallback(e.value)}
              placeholder="Select periods"
              className="p-column-filter"
              maxSelectedLabels={1}
              style={{ width: '100%' }}
              panelClassName="hidefilter"
            />
          )}
          showFilterMatchModes={false}
          filterMatchMode="in"
        />
        <Column
          field="reportingTo"
          header="Reporting To"
          style={{ minWidth: '150px' }}
          sortable
          filter
          filterPlaceholder="Search periods"
          filterMenuStyle={{ width: '14rem' }}
          filterElement={(options) => (
            <MultiSelect
              value={options.value}
              options={[...new Set(filteredSrf.map(item => item.reportingTo))].filter(Boolean)}
              onChange={(e) => options.filterCallback(e.value)}
              placeholder="Select periods"
              className="p-column-filter"
              maxSelectedLabels={1}
              style={{ width: '100%' }}
              panelClassName="hidefilter"
            />
          )}
          showFilterMatchModes={false}
          filterMatchMode="in"
        />

        {attributeColumn.map((i, index) => (
          <Column
            key={index}
            field={i.name}
            header={i.header}
            sortable
          // filter
          // filterPlaceholder={`Search ${i.header}`}
          // filterMenuStyle={{ width: '14rem' }}
          // filterElement={(options) => {
          //     // Get unique values for this column
          //     const uniqueValues = [...new Set(filteredSrf
          //         .map(item => item[i.name])
          //         .filter(value => value !== undefined && value !== null && value !== '')
          //     )];

          //     return (
          //         <MultiSelect
          //             value={options.value}
          //             options={uniqueValues}
          //             onChange={(e) => options.filterCallback(e.value)}
          //             placeholder={`Select ${i.header}`}
          //             className="p-column-filter"
          //             maxSelectedLabels={1}
          //             style={{ width: '100%' }}
          //             panelClassName="hidefilter"
          //         />
          //     );
          // }}
          // showFilterMatchModes={false}
          // filterMatchMode="in"
          />
        ))}
      </DataTable>
    </div>
  )
}
const TabsComponent = ({ data = [], tabLabelKey = 'label', contentKey = 'content', onChange = () => { }, tabStyle = {}, contentStyle = {} }) => {
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    onChange(data[activeTab])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, data])

  return (
    <div>
      {/* Tab Headers */}
      <div style={{
        display: 'flex',
        flexWrap: 'wrap', // Allow tabs to wrap to next line
        borderBottom: '2px solid #ccc',
        marginBottom: '10px',
        gap: '5px' // Add space between tabs
      }}>
        {data.map((item, index) => (
          <div
            key={index}
            onClick={() => { setActiveTab(index); }}
            style={{
              padding: '10px 20px',
              cursor: 'pointer',
              borderBottom: activeTab === index ? '2px solid #007bff' : '2px solid transparent',
              fontWeight: activeTab === index ? 'bold' : 'normal',
              color: activeTab === index ? '#007bff' : '#000',
              borderRadius: '5px',
              backgroundColor: '#f9f9f9',
              ...tabStyle
            }}
          >
            {item[tabLabelKey]}
          </div>
        ))}
      </div>

      {/* Tab Content */}
      <div style={{ padding: '10px', ...contentStyle }}>
        {data[activeTab] && data[activeTab][contentKey]}
      </div>
    </div>
  );
};