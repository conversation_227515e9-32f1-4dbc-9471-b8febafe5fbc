.document-browser {
    padding: 20px;
    margin: 0 auto;
}

.document-browser .p-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.document-browser .p-card-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.5rem;
}

.document-browser .p-breadcrumb {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem 1rem;
}

.document-browser .p-breadcrumb .p-breadcrumb-list {
    margin: 0;
}

.document-browser .p-breadcrumb .p-menuitem-link {
    color: #007bff;
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.document-browser .p-breadcrumb .p-menuitem-link:hover {
    background-color: #e3f2fd;
    color: #0056b3;
}

.document-browser .p-datatable {
    border-radius: 6px;
    overflow: hidden;
}

.document-browser .p-datatable .p-datatable-header {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.document-browser .p-datatable .p-datatable-tbody > tr {
    transition: background-color 0.2s;
}

.document-browser .p-datatable .p-datatable-tbody > tr:hover {
    background-color: #f8f9fa;
}

.document-browser .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.document-browser .p-button {
    border-radius: 6px;
    transition: all 0.2s;
}

.document-browser .p-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.document-browser .pi-folder {
    font-size: 1.2rem;
}

.document-browser .pi-file {
    font-size: 1.1rem;
}

.document-browser .flex.align-items-center {
    cursor: pointer;
}

.document-browser .flex.align-items-center:hover {
    color: #007bff;
}

/* Loading spinner container */
.document-browser .flex.justify-content-center {
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

/* Empty state styling */
.document-browser .p-datatable .p-datatable-emptymessage {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .document-browser {
        padding: 10px;
    }
    
    .document-browser .p-datatable .p-datatable-tbody > tr > td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
    
    .document-browser .p-card-title {
        font-size: 1.25rem;
    }
}

/* Custom tooltip styling */
.document-browser .p-tooltip .p-tooltip-text {
    background: #2c3e50;
    color: white;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* Action buttons styling */
.document-browser .p-button-text {
    color: #007bff;
}

.document-browser .p-button-text:hover {
    background-color: #e3f2fd;
    color: #0056b3;
}

/* File type specific styling */
.document-browser .pi-file[data-file-type="parquet"] {
    color: #28a745;
}

.document-browser .pi-file[data-file-type="csv"] {
    color: #17a2b8;
}

.document-browser .pi-file[data-file-type="json"] {
    color: #ffc107;
}
