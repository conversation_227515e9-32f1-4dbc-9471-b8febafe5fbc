// Reliable storage implementation for Redux Persist
// Handles quota exceeded errors gracefully

class ReliableStorage {
  constructor() {
    this.isLocalStorageAvailable = this.checkLocalStorageAvailability();
    this.memoryStorage = new Map();
    this.usingMemoryFallback = false;
  }

  checkLocalStorageAvailability() {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  async getItem(key) {
    if (this.usingMemoryFallback || !this.isLocalStorageAvailable) {
      return this.memoryStorage.get(key) || null;
    }

    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('localStorage.getItem failed, switching to memory storage:', error);
      this.usingMemoryFallback = true;
      return this.memoryStorage.get(key) || null;
    }
  }

  async setItem(key, value) {
    // Always store in memory as backup
    this.memoryStorage.set(key, value);

    if (this.usingMemoryFallback || !this.isLocalStorageAvailable) {
      return;
    }

    try {
      localStorage.setItem(key, value);
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        console.warn('localStorage quota exceeded, switching to memory-only storage');
        this.usingMemoryFallback = true;
        
        // Try to clear some space
        this.clearNonEssentialData();
        
        // Try one more time
        try {
          localStorage.setItem(key, value);
          this.usingMemoryFallback = false; // Success, can use localStorage again
        } catch (retryError) {
          console.warn('Still cannot use localStorage after cleanup, staying in memory mode');
        }
      } else {
        console.warn('localStorage.setItem failed:', error);
        this.usingMemoryFallback = true;
      }
    }
  }

  async removeItem(key) {
    this.memoryStorage.delete(key);

    if (this.usingMemoryFallback || !this.isLocalStorageAvailable) {
      return;
    }

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('localStorage.removeItem failed:', error);
    }
  }

  async clear() {
    this.memoryStorage.clear();

    if (this.usingMemoryFallback || !this.isLocalStorageAvailable) {
      return;
    }

    try {
      localStorage.clear();
    } catch (error) {
      console.warn('localStorage.clear failed:', error);
    }
  }

  clearNonEssentialData() {
    const essentialKeys = ['persist:root'];
    const keysToRemove = [];

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && !essentialKeys.includes(key)) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn(`Failed to remove ${key}:`, error);
        }
      });

      console.log(`Cleared ${keysToRemove.length} non-essential localStorage items`);
    } catch (error) {
      console.warn('Failed to clear non-essential data:', error);
    }
  }

  // Get storage status for debugging
  getStatus() {
    return {
      isLocalStorageAvailable: this.isLocalStorageAvailable,
      usingMemoryFallback: this.usingMemoryFallback,
      memoryItemCount: this.memoryStorage.size,
      localStorageItemCount: this.isLocalStorageAvailable ? localStorage.length : 0
    };
  }
}

// Create and export a singleton instance
export const reliableStorage = new ReliableStorage();

// Export a factory function for Redux Persist
export const createReliableStorage = () => reliableStorage;
