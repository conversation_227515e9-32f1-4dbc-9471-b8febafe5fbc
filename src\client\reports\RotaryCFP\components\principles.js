export const principles = [
  {
    title: "EXECUTIVE SUMMARY",
    id: "executive-summary",
    children: [
      {
        title: "Table 1 1: Rotary Group of Companies",
        targetId: "section-executive-summary",
      },
    ],
  },
  {
    title: "DEFINITIONS AND ABBREVIATIONS",
    id: "definitions-abbreviation",
    children: [
      {
        title: "Table 2: Definitions & Abbreviations",
        targetId: "section-definitions-abbreviation",
      },
    ],
  },
  {
    title: "ORGANISATION PROFILE",
    id: "organization-profile",
    children: [
      {
        title: "3.1	DESCRIPTION OF ORGANISATION",
        targetId: "section-organization-profile",
      },
    ],
  },
  {
    title: "GHG INVENTORY DESIGN AND METHODOLOGY",
    id: "ghg-inventory-design-and-methodology",
    children: [
      {
        title: "METHODOLOGY",
        targetId: "section-ghg-inventory-design-and-methodology",
      },
      {
        title: "ORGANISATIONAL BOUNDARIES",
        targetId:
          "section-ghg-inventory-design-and-methodology-organisational-boundaries",
      },
      {
        title: "OPERATIONAL BOUNDARIES",
        targetId:
          "section-ghg-inventory-design-and-methodology-operational-boundaries",
      },
      {
        title: "GHG INVENTORY",
        targetId: "section-ghg-inventory-design-and-methodology-ghg-inventory",
      },
      {
        title: "EMISSION FACTORS",
        targetId:
          "section-ghg-inventory-design-and-methodology-emission-factors",
      },
    ],
  },
  {
    title: "REDUCTION TARGETS AND IMPROVEMENT MEASURES",
    id: "reduction-targets-and-improvement-measures",
    children: [],
  },
];
