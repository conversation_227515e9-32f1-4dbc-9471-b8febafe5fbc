import React, { useState, useEffect } from "react";
import { MultiSelect } from "primereact/multiselect";
import { Calendar } from "primereact/calendar";
import "./FilterStyles.css";

const FilterComponent = ({ data, type, setFilteredData, tabIndex }) => {



  const [filters, setFilters] = useState({
    zone: [],
    city: [],
    branch_code: [],
    area_office: [],
    start_date_of_calibration: null,
    end_date_of_calibration: null,
  });
  useEffect(() => {
    applyFilters();
  }, [filters]);

  const handleChange = (key, value) => {
    setFilters((prev) => {
      let newFilters = { ...prev, [key]: value };

      // Reset dependent fields when a higher-level filter changes
      if (key === "zone") {
        newFilters.city = [];
        newFilters.branch_code = [];
        newFilters.area_office = [];
      }
      if (key === "city") {
        newFilters.branch_code = [];
        newFilters.area_office = [];
      }
      if (key === "branch_code") {
        newFilters.area_office = [];
      }

      return newFilters;
    });
  };

  const applyFilters = () => {
    let filtered = data;

    if (filters.zone.length > 0) {
      filtered = filtered.filter((item) => filters.zone.includes(item.zone));
    }
    if (filters.city.length > 0) {
      filtered = filtered.filter((item) => filters.city.includes(item.city));
    }
    if (filters.branch_code.length > 0) {
      filtered = filtered.filter((item) => {
        const itemBranchCode = String(item.branch_code || "").trim();
        const itemDealerName = String(item.dealer_name || "").trim();

        return filters.branch_code.some(selectedBranchCode => {
          const selectedCode = selectedBranchCode.split("-").pop().trim();
          const selectedName = selectedBranchCode.split("-")[0].trim();

          return itemBranchCode
            ? itemBranchCode === selectedCode
            : itemDealerName === selectedName;
        });
      });
    }
    // Handle area_office filtering
    if (filters.area_office.length > 0) {
      filtered = filtered.filter((item) => {
        const itemAreaOffice = item.area_office
          ? item.area_office.trim()
          : "";

        return filters.area_office.some(selectedAreaOffice => {
          // Extract everything except the last part (assuming last part is branch code)
          const extractedAreaOffice = selectedAreaOffice
            .split(" - ")
            .slice(0, -1)
            .join(" - ")
            .trim();

          return itemAreaOffice === extractedAreaOffice;
        });
      });
    }

    const startDate = filters.start_date_of_calibration
      ? new Date(filters.start_date_of_calibration).setHours(0, 0, 0, 0)
      : null;
    const endDate = filters.end_date_of_calibration
      ? new Date(filters.end_date_of_calibration).setHours(23, 59, 59, 999)
      : null;

    if (startDate || endDate) {
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.date_of_calibration).setHours(
          0,
          0,
          0,
          0
        );
        return (
          (!startDate || itemDate >= startDate) &&
          (!endDate || itemDate <= endDate)
        );
      });
    }

    setFilteredData(filtered);
  };

  const getAreaOfficeOptions = () => {
    let filteredData = data;

    // Apply filters based on selected values
    if (filters.zone.length > 0) {
      filteredData = filteredData.filter((item) => filters.zone.includes(item.zone));
    }
    if (filters.city.length > 0) {
      filteredData = filteredData.filter((item) => filters.city.includes(item.city));
    }

    // Apply branch_code filter
    if (filters.branch_code.length > 0) {
      filteredData = filteredData.filter((item) => {
        const itemBranchCode = String(item.branch_code || "").trim();
        const itemDealerName = String(item.dealer_name || "").trim();

        return filters.branch_code.some(selectedBranchCode => {
          const selectedCode = selectedBranchCode.split("-").pop().trim();
          const selectedName = selectedBranchCode.split("-")[0].trim();

          return itemBranchCode
            ? itemBranchCode === selectedCode
            : itemDealerName === selectedName;
        });
      });
    }

    let uniqueAreaOffices = new Set(
      filteredData
        .map(
          (item) =>
            `${item.area_office ? item.area_office : ""} - ${
              item.branch_code ? item.branch_code : ""
            }`
        )
        .filter((value) => value.trim() !== "-") // Avoid empty values
    );

    return Array.from(uniqueAreaOffices).map((value) => ({
      label: value,
      value: value,
    }));
  };

  // Get unique options dynamically based on selected filters
  const getUniqueOptions = (key) => {
    let filteredData = data;

    // Apply filters based on hierarchy
    if (key !== 'zone' && filters.zone.length > 0) {
      filteredData = filteredData.filter(item => filters.zone.includes(item.zone));
    }

    if (key !== 'city' && filters.city.length > 0) {
      filteredData = filteredData.filter(item => filters.city.includes(item.city));
    }

    if (key === 'area_office' && filters.branch_code.length > 0) {
      filteredData = filteredData.filter(item => {
        const itemBranchCode = String(item.branch_code || '').trim();
        const itemDealerName = String(item.dealer_name || '').trim();

        return filters.branch_code.some(selectedBranchCode => {
          const selectedCode = selectedBranchCode.split('-').pop().trim();
          const selectedName = selectedBranchCode.split('-')[0].trim();

          return itemBranchCode
            ? itemBranchCode === selectedCode
            : itemDealerName === selectedName;
        });
      });
    }

    let uniqueValues = new Set(
      filteredData
        .map((item) => item[key])
        .filter(
          (value) => value !== undefined && value !== null && value !== ""
        )
    );

    return Array.from(uniqueValues).map((value) => ({
      label: String(value),
      value: String(value),
    }));
  };

  const getBranchCodeOptions = () => {
    let filteredData = data;

    // Apply filters only if specific values are selected
    if (filters.zone.length > 0) {
      filteredData = filteredData.filter((item) => filters.zone.includes(item.zone));
    }
    if (filters.city.length > 0) {
      filteredData = filteredData.filter((item) => filters.city.includes(item.city));
    }

    let uniqueBranchCodes = new Set(
      filteredData
        .map(
          (item) => {
            // For APS tab, use dealer_name as APS name
            const nameField = item.dealer_name ? item.dealer_name : "";
            const codeField = item.branch_code ? item.branch_code : "";
            return `${nameField} - ${codeField}`;
          }
        )
        .filter((value) => value.trim() !== " - ") // Avoid empty values
    );

    return Array.from(uniqueBranchCodes).map((value) => ({
      label: value,
      value: value,
    }));
  };

  return (
    <div style={{ display: "flex", justifyContent: "space-evenly" }}>
    <div style={{ display: "flex", flexDirection: "column" }}>
        <label>Zone</label>
        <MultiSelect
          value={filters.zone}
          options={getUniqueOptions("zone")}
          onChange={(e) => handleChange("zone", e.value)}
          placeholder="Select Zone"
          className="w-100 filter-dropdown"
          filter
          display="chip"
          panelClassName="hidefilter"
          maxSelectedLabels={1}
          virtualScrollerOptions={{ itemSize: 38 }}
        />
      </div>

      <div style={{ display: "flex", flexDirection: "column" }}>
        <label>City</label>
        <MultiSelect
          value={filters.city}
          options={getUniqueOptions("city")}
          onChange={(e) => handleChange("city", e.value)}
          placeholder="Select City"
          className="w-100 filter-dropdown"
          filter
          display="chip"
          panelClassName="hidefilter"
          maxSelectedLabels={1}
          virtualScrollerOptions={{ itemSize: 38 }}
        />
      </div>

   
      {tabIndex !== 2 && (
        <div style={{ display: "flex", flexDirection: "column" }}>
          <label>{tabIndex === 1 ? "APS Name and Code" : "Dealer Name and Code"}</label>
          <MultiSelect
            value={filters.branch_code}
            options={getBranchCodeOptions()}
            onChange={(e) => handleChange("branch_code", e.value)}
            placeholder={tabIndex === 1 ? "Select APS Code" : "Select Dealer Code"}
            className="w-100 filter-dropdown"
            filter
            disabled={false}
            display="chip"
            panelClassName="hidefilter"
            maxSelectedLabels={1}
            virtualScrollerOptions={{ itemSize: 38 }}
          />
        </div>
      )}

    
      <div style={{ display: "flex", flexDirection: "column" }}>
        <label>{tabIndex === 2 ? "Area Office and Code" : "Area Office"}</label>
        <MultiSelect
          value={filters.area_office}
          options={getAreaOfficeOptions()}
          onChange={(e) => handleChange("area_office", e.value)}
          placeholder="Select Area Office"
          className="w-100 filter-dropdown"
          filter
          disabled={false}
          display="chip"
          panelClassName="hidefilter"
          maxSelectedLabels={1}
          virtualScrollerOptions={{ itemSize: 38 }}
        />
      </div>

      {/* Date filters */}
      <div style={{ display: "flex", flexDirection: "column" }}>
        <label>From</label>
        <Calendar
          value={filters.start_date_of_calibration}
          onChange={(e) => handleChange("start_date_of_calibration", e.value)}
          placeholder="Start Date"
          showButtonBar
          showIcon
        />
      </div>

      <div style={{ display: "flex", flexDirection: "column" }}>
        <label>To</label>
        <Calendar
          value={filters.end_date_of_calibration}
          onChange={(e) => handleChange("end_date_of_calibration", e.value)}
          placeholder="End Date"
          showButtonBar
          showIcon
          minDate={filters.start_date_of_calibration}
          disabled={!filters.start_date_of_calibration}
        />
      </div>
    </div>
  );
};

export default FilterComponent;
