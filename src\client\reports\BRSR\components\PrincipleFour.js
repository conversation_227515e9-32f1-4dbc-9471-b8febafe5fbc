import React from "react";

const PrincipleFour = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          PRINCIPLE 4- BUSINESSES SHOULD RESPECT THE INTERESTS OF AND BE
          RESPONSIVE TO ALL ITS STAKEHOLDERS
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Describe the processes for identifying key stakeholder groups of
          the entity.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. List stakeholder groups identified as key for your entity and the
          frequency of engagement with 24 each stakeholder group.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ textAlign: "center" }}>Stakeholder Group</th>
              <th style={{ textAlign: "center" }}>
                Whether identified as Vulnerable &amp; Marginalized Group
                (Yes/No)
              </th>
              <th style={{ textAlign: "center" }}>
                Channels of communication (Email, SMS, Newspaper, Pamphlets,
                Advertisement, Community Meetings, Notice Board, Website),
                Others
              </th>
              <th style={{ textAlign: "center" }}>
                Frequency of engagement (Annually/ Half yearly/ Quarterly /
                others – please specify)
              </th>
              <th style={{ textAlign: "center" }}>
                Purpose and scope of engagement including key topics and
                concerns raised during such engagement
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "10rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Provide the processes for consultation between stakeholders and the
          Board on economic, environmental, and social topics or if consultation
          is delegated, how is feedback from such consultations provided to the
          Board?
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Whether stakeholder consultation is used to support the
          identification and management of environmental, and social topics (Yes
          / No). If so, provide details of instances as to how the inputs
          received from stakeholders on these topics were incorporated into the
          policies and activities of the entity.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Provide details of instances of engagement with, and actions taken
          to, address the concerns of vulnerable/ marginalized stakeholder
          groups.
        </p>
      </div>
    </div>
  );
};

export default PrincipleFour;
