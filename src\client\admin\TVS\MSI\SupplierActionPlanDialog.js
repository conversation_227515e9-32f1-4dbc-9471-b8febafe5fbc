import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Calendar } from 'primereact/calendar';
import ReactQuill from 'react-quill';
import { Button } from 'primereact/button';
import { format } from 'date-fns';
import { Tag } from 'primereact/tag';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import moment from 'moment';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import { useSelector } from 'react-redux';

export default function SupplierActionPlanDialog({ visible, onHide, data, refresh }) {
    const [actionData, setActionData] = useState([]);

    const userList = useSelector((state) => state.userlist.userList);
    const categoryList = [
        { name: 'Forging & Machining', value: 1 },
        { name: 'Casting & Machining', value: 2 },
        { name: 'Pressing & Fabrication', value: 3 },
        { name: 'Proprietary Mechanical', value: 4 },
        { name: 'Proprietary Electrical', value: 5 },
        { name: 'Plastics, Rubber, Painting and Stickers', value: 6 },
        { name: 'EV/3W/2W', value: 7 },
        { name: 'BW', value: 8 },
        { name: 'Accessories', value: 9 },
        { name: 'IDM (Indirect Material)', value: 10 },
        { name: 'Import', value: 11 }
    ];
    const userLookup = userList.reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
    }, {});
    useEffect(() => {
        if (data?.actions?.length) {
            const initialized = data.actions.map(action => ({
                ...action,
                rootCause: action.rootCause || '',
                proposedCorrectiveAction: action.proposedCorrectiveAction || '',
                actionTargetDate: action.actionTargetDate ? new Date(action.actionTargetDate) : null,
            }));
            setActionData(initialized);
        }
    }, [data]);

    const isSaveEnabled = actionData.every(
        act =>
            act.rootCause?.trim() &&
            act.proposedCorrectiveAction?.trim() &&
            act.actionTargetDate
    );
    const isDraftEnabled = actionData.some(
        act =>
            act.rootCause?.trim() ||
            act.proposedCorrectiveAction?.trim() ||
            act.actionTargetDate
    );
    const handleChange = (index, field, value) => {
        const updated = [...actionData];
        updated[index][field] = value;
        setActionData(updated);
    };

    const handleSave = () => {
        if (isSaveEnabled) {
            console.log(actionData);
            APIServices.post(API.SubmitActionPlan, actionData).then(() => {
                refresh()
            }
            )

        }
    };
    const handleDraft = () => {
        if (isDraftEnabled) {
            console.log(actionData);
            APIServices.post(API.DraftActionPlan, actionData).then(() => {
                refresh()
            }
            )

        }
    };

    // Function to render comments for an action
    const renderActionComments = (action) => {
        // Check if action has been approved or rejected
        const isApproved = action.type === 1;
        const isRejected = (action.type === 12 || action.type === 102) && action.actionPlanRejectedBy;

        // Skip if action is just submitted or has no approval/rejection info
        if (action.type === 21 || (!isApproved && !isRejected)) {
            return null;
        }

        return (
            <div className="action-comments mt-3" style={{ borderTop: '1px solid #dee2e6', paddingTop: '1rem' }}>
                <div
                    className="comment-item"
                    style={{
                        padding: '1rem',
                        borderRadius: '4px',
                        backgroundColor: '#f8f9fa',
                        borderLeft: `3px solid ${isApproved ? '#6c757d' : '#6c757d'}`
                    }}
                >
                    <div className="comment-header d-flex justify-content-between align-items-center">
                        <div>
                            <strong>
                                {isApproved
                                    ? `Approved`
                                    : `Return`
                                }
                            </strong>
                            <span className="comment-date ml-2 text-muted">
                                {isApproved && action.actionPlanApprovedOn
                                    ? moment(action.actionPlanApprovedOn).format('DD-MMM-YYYY hh:mm A')
                                    : action.actionPlanRejectedOn
                                        ? moment(action.actionPlanRejectedOn).format('DD-MMM-YYYY hh:mm A')
                                        : ''
                                }
                            </span>
                        </div>
                        <Tag
                            value={isApproved ? 'Approved' : 'Return'}
                            severity={isApproved ? 'success' : 'warning'}
                        />
                    </div>
                    <div className="comment-content mt-2">
                        {isApproved ? action.actionPlanApproverComments || 'No comments provided' : action.actionPlanApproverComments || 'No comments provided'}
                    </div>
                </div>
            </div>
        );
    };
    const renderSupplierMetadata = () => {
        console.log(data)
        if (!data || !data.vendor) return null;

        const vendor = data.vendor;

        // Map supplier category value to name
        const getSupplierCategoryName = (categoryValue) => {
            const category = categoryList.find(item => item.value === categoryValue);
            return category ? category.name : categoryValue;
        };

        // Get calibration team members
        const getCalibrationTeamMembers = () => {
            const allAssessors = [
                ...(data.group1?.assessors || []),
                ...(data.group2?.assessors || []),
                ...(data.group3?.assessors || []),
                ...(data.group4?.assessors || [])
            ];

            const uniqueAssessors = [...new Set(allAssessors)];
            return uniqueAssessors.map(assessorId => {
                const user = userLookup[assessorId];
                return user ? user.information?.empname || '' : '';
            }).filter(x=>x).join(', ') || 'Not Assigned';
        };

        return (
            <Card className="mb-4 shadow-sm">
                <div className="row">
                   
                    <div className="col-md-12">
                        <h4 className="mb-3 clr-navy">MSI Assessment Information</h4>
                        <Divider />
                        <div className="row">
                            <div className="col-md-4">
                                <p><strong>Self-assessment Due Date:</strong> {data.assessmentEndDate ? moment(data.assessmentEndDate).format('DD MMM YYYY') : 'N/A'}</p>
                                <p><strong>Self-assessment Submitted Date:</strong> {data.supplierAssignmentSubmission?.submitted_on ? moment(data.supplierAssignmentSubmission.submitted_on).format('DD MMM YYYY') : 'Not Submitted'}</p>
                                <p><strong>MSI Self-Assessment Score:</strong> {data.supplierAssignmentSubmission?.supplierMSIScore || '-'}</p>
                            </div>
                            <div className="col-md-4">
                                <p><strong>Audit Start Date:</strong> {data.auditStartDate ? moment(data.auditStartDate).format('DD MMM YYYY') : 'N/A'}</p>
                                <p><strong>Audit End Date:</strong> {data.auditEndDate ? moment(data.auditEndDate).format('DD MMM YYYY') : 'N/A'}</p>
                                <p><strong>MSI Audit Score:</strong> {data.auditorAssignmentSubmission?.auditorMSIScore || '-'}</p>
                            </div>
                            <div className="col-md-4">
                                <p><strong>Calibration Team Members:</strong> {getCalibrationTeamMembers()}</p>
                                <p><strong>Assessment Status:</strong> {data.supplierAssignmentSubmission?.type === 1 ? 'Submitted' : data.supplierAssignmentSubmission?.type === 2 ? 'Approved' : 'Not Started'}</p>
                                <p><strong>Audit Status:</strong> {data.auditorAssignmentSubmission?.type === 1 ? 'Submitted' : data.auditorAssignmentSubmission?.type === 2 ? 'Approved' : 'Not Started'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        );
    };
    return (
        <Dialog header="Action Plan Submission" visible={visible} onHide={onHide} style={{ width: '80vw' }}>
            <div className="p-3">
                <div className="mb-3">
                    <strong>MSI ID:</strong> {data?.msiId} <br />
                    <strong>Due Date:</strong> {data?.dueDate ? format(new Date(data?.dueDate), 'dd-MMM-yyyy') : "NA"} <br />
                    {data?.submitted_on && <> <strong>{data.isApproved ? 'Approved on' : 'Submitted on'}:</strong> {data?.submitted_on} </>}
                </div>
                {renderSupplierMetadata()}
                <Accordion multiple activeIndex={[0]}>
                    {actionData.map((action, index) => (
                        <AccordionTab key={index} header={`#${action.actionId} - ${action.finding}`}>
                            <div className="col-12">
                                <label style={{ marginBottom: 10 }}><strong>Root Cause (Provide a detailed analysis to identify the underlying root cause of the finding):</strong></label>
                                {(action.type === 12 || action.type === 102) ? (
                                    <ReactQuill
                                        theme="snow"
                                        value={action.rootCause}
                                        onChange={(value) => handleChange(index, 'rootCause', value)}
                                    />
                                ) : (
                                    <div dangerouslySetInnerHTML={{ __html: action.rootCause }} />
                                )}
                            </div>

                            <div className="col-12">
                                <label style={{ marginBottom: 10 }}><strong>Proposed Corrective Action(s) (Propose specific corrective actions that directly address and mitigate the identified root cause):</strong></label>
                                {(action.type === 12 || action.type === 102) ? (
                                    <ReactQuill
                                        theme="snow"
                                        value={action.proposedCorrectiveAction}
                                        onChange={(value) => handleChange(index, 'proposedCorrectiveAction', value)}
                                    />
                                ) : (
                                    <div dangerouslySetInnerHTML={{ __html: action.proposedCorrectiveAction }} />
                                )}
                            </div>

                            <div className="col-12">
                                <label className='flex' style={{ marginBottom: 10 }}><strong>Target Completion Date:</strong></label>
                                {(action.type === 12 || action.type === 102) ? (
                                    <Calendar
                                        value={action.actionTargetDate}
                                        onChange={(e) => handleChange(index, 'actionTargetDate', e.value)}
                                        showIcon
                                        dateFormat="dd-MM-yy"
                                    />
                                ) : (
                                    <div>{action.actionTargetDate ? format(new Date(action.actionTargetDate), 'dd-LLL-yyyy') : 'N/A'}</div>
                                )}
                            </div>

                            {/* Display comments for this action if it has been approved or returned */}
                            {renderActionComments(action)}
                        </AccordionTab>
                    ))}
                </Accordion>
                <div>
                    {actionData.every((x) => x.type === 12 || x.type === 102) && isDraftEnabled && (
                        <div className="mt-4 text-right">
                            <Button label="Save as Draft" icon="pi pi-check" onClick={handleDraft} />
                        </div>
                    )}
                    {actionData.every((x) => x.type === 12 || x.type === 102) && isSaveEnabled && (
                        <div className="mt-4 text-right">
                            <Button label="Save & Submit Action Plan" icon="pi pi-check" onClick={handleSave} />
                        </div>
                    )}

                </div>

            </div>
        </Dialog>
    );
}
