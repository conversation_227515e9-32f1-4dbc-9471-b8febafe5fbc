import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const StackedHeatmaps = () => {
  const chartRef = useRef(null);

  // Example dataset with additional parameters
  const heatmapData = [
    { parameter: "Gender", category: "Entry-level", value: 50 },
    { parameter: "Gender", category: "Mid-level", value: 40 },
    { parameter: "Gender", category: "Senior Management", value: 30 },
    { parameter: "Gender", category: "Executive", value: 20 },
    { parameter: "Age", category: "Entry-level", value: 60 },
    { parameter: "Age", category: "Mid-level", value: 45 },
    { parameter: "Age", category: "Senior Management", value: 35 },
    { parameter: "Age", category: "Executive", value: 25 },
    { parameter: "Region", category: "Entry-level", value: 70 },
    { parameter: "Region", category: "Mid-level", value: 50 },
    { parameter: "Region", category: "Senior Management", value: 40 },
    { parameter: "Region", category: "Executive", value: 30 },
    // Add more rows for additional parameters and categories
  ];

  useEffect(() => {
    renderHeatmap();
  }, []);

  const renderHeatmap = () => {
    const width = 800;
    const height = 400;
    const margin = { top: 50, right: 50, bottom: 50, left: 100 };

    // Clear existing chart
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const parameters = [...new Set(heatmapData.map((d) => d.parameter))];
    const categories = [...new Set(heatmapData.map((d) => d.category))];

    const xScale = d3
      .scaleBand()
      .domain(categories)
      .range([margin.left, width - margin.right])
      .padding(0.1);

    const yScale = d3
      .scaleBand()
      .domain(parameters)
      .range([margin.top, height - margin.bottom])
      .padding(0.1);

    const colorScale = d3
      .scaleSequential(d3.interpolateCool)
      .domain([0, d3.max(heatmapData, (d) => d.value)]);

    // Draw heatmap cells
    svg
      .selectAll(".heatmap-rect")
      .data(heatmapData)
      .enter()
      .append("rect")
      .attr("x", (d) => xScale(d.category))
      .attr("y", (d) => yScale(d.parameter))
      .attr("width", xScale.bandwidth())
      .attr("height", yScale.bandwidth())
      .attr("fill", (d) => colorScale(d.value));

    // Add text labels inside cells
    svg
      .selectAll(".heatmap-text")
      .data(heatmapData)
      .enter()
      .append("text")
      .attr("x", (d) => xScale(d.category) + xScale.bandwidth() / 2)
      .attr("y", (d) => yScale(d.parameter) + yScale.bandwidth() / 2)
      .attr("text-anchor", "middle")
      .attr("dy", ".35em")
      .text((d) => d.value)
      .style("fill", "#000")
      .style("font-size", "10px");

    // Add x-axis
    svg
      .append("g")
      .attr("transform", `translate(0, ${height - margin.bottom})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .attr("text-anchor", "end")
      .style("font-size", "10px");

    // Add y-axis
    svg
      .append("g")
      .attr("transform", `translate(${margin.left}, 0)`)
      .call(d3.axisLeft(yScale))
      .selectAll("text")
      .style("font-size", "10px");
  };

  return (
    <>
      <div
        style={{
          fontSize: "14px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "5px",
        }}
      >
        Diversity Heatmap
      </div>
      diversity metrics across multiple dimensions, such as department, gender,
      and age group.
      <div style={{ display: "flex", justifyContent: "center" }}>
        <div ref={chartRef}></div>
      </div>
    </>
  );
};

export default StackedHeatmaps;
