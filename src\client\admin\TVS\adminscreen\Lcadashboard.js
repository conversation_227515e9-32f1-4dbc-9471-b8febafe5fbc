import React, { useState, useEffect, useRef } from "react";
import { Card } from "primereact/card";
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
} from "recharts";
import APIServices from "../../../../service/APIService";
import { API } from "../../../../constants/api_url";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Button } from "primereact/button";

const LCADashboard = () => {
  const [activeDetailView, setActiveDetailView] = useState(null); // 'parts', 'completion', 'suppliers', 'submission'
  const [globalFilter, setGlobalFilter] = useState("");
  const dt = useRef(null);

  const [partsTimeRange, setPartsTimeRange] = useState("Max");
  const [partsData, setPartsData] = useState([]);
  const [partRecords, setPartRecords] = useState([]);
  const [currentParts, setCurrentParts] = useState(0);
  const [partsVariance, setPartsVariance] = useState(0);
  const [partsTrend, setPartsTrend] = useState("");
  const get_lca_dashboard_data = () => {
    APIServices.get(API.LCA_dashboard + `?range=${partsTimeRange}`).then(
      (res) => {
        console.log(res.data);
        setPartsData(res.data.chartData);
        setPartRecords(res.data.records);
        setCurrentParts(res.data.currentValue);
        setPartsTrend(res.data.trendDirection);

        const targetParts = 1920;
        setPartsVariance(
          (((currentParts - targetParts) / targetParts) * 100).toFixed(1)
        );
      }
    );
  };

  useEffect(() => {
    get_lca_dashboard_data();
  }, [partsTimeRange]);

  const [completionTimeRange, setCompletionTimeRange] = useState("Max");
  const [completionData, setCompletionData] = useState([]);
  const [completionRecords, setCompletionRecords] = useState([]);
  const [currentCompletion, setCurrentCompletion] = useState(0);
  const [completionVariance, setCompletionVariance] = useState(0);
  const [completionTrend, setCompletionTrend] = useState("");
  const get_lca_completion_data = () => {
    APIServices.get(
      API.LCA_dashboard_completion + `?range=${completionTimeRange}`
    ).then((res) => {
      console.log(res.data);
      setCompletionData(res.data.chartData);
      setCompletionRecords(res.data.records);
      setCurrentCompletion(res.data.currentValue);
      setCompletionTrend(res.data.trendDirection);

      const targetCompletion = 57;
      setCompletionVariance((currentCompletion - targetCompletion).toFixed(1));
    });
  };

  useEffect(() => {
    get_lca_completion_data();
  }, [completionTimeRange]);

  const [suppliersTimeRange, setSuppliersTimeRange] = useState("Max");
  const [supplierData, setSupplierData] = useState([]);
  const [supplierRecords, setSupplierRecords] = useState([]);
  const [currentSuppliers, setCurrentSuppliers] = useState(0);
  const [suppliersVariance, setSuppliersVariance] = useState(0);
  const [suppliersTrend, setSuppliersTrend] = useState("");
  const get_lca_supplier_data = () => {
    APIServices.get(
      API.LCA_dashboard_supplier + `?range=${suppliersTimeRange}`
    ).then((res) => {
      console.log(res.data);
      setSupplierData(res.data.chartData);
      setSupplierRecords(res.data.records);
      setCurrentSuppliers(res.data.currentValue);
      setSuppliersTrend(res.data.trendDirection);

      const targetSuppliers = 500;
      setSuppliersVariance(
        (
          ((targetSuppliers - currentSuppliers) / targetSuppliers) *
          100
        ).toFixed(1)
      );
    });
  };

  useEffect(() => {
    get_lca_supplier_data();
  }, [suppliersTimeRange]);

  const [submissionTimeRange, setSubmissionTimeRange] = useState("Max");
  const [submissionData, setSubmissionData] = useState([]);
  const [submissionRecords, setSubmissionRecords] = useState([]);
  const [currentSubmission, setCurrentSubmission] = useState(0);
  const [submissionVariance, setSubmissionVariance] = useState(0);
  const [submissionTrend, setSubmissionTrend] = useState("");
  const get_lca_submission_data = () => {
    APIServices.get(
      API.LCA_dashboard_submission + `?range=${submissionTimeRange}`
    ).then((res) => {
      console.log(res.data);
      setSubmissionData(res.data.chartData);
      setSubmissionRecords(res.data.records);
      setCurrentSubmission(res.data.currentValue);
      setSubmissionVariance(res.data.average);
      setSubmissionTrend(res.data.trendDirection);
    });
  };

  useEffect(() => {
    get_lca_submission_data();
  }, [submissionTimeRange]);

  const getPartsData = () => partsData;

  const getCompletionData = () => completionData;

  const getSubmissionData = () => submissionData;

  const getSuppliersData = () => supplierData;

  //   const targetSubmission = 40.8;

  const handleShowDetails = (chartType) => {
    setActiveDetailView(activeDetailView === chartType ? null : chartType);
  };

  const getDetailedDataContent = () => {
    switch (activeDetailView) {
      case "parts":
        return {
          title: "Detailed Data - Absolute numbers of Identified parts",
          subtitle: "19 data points • 11 groups",
          stats: [
            { label: "Current", value: currentParts + " parts" },
            // {
            //   label: "Target",
            //   value: partsVariance + "%",
            //   color:
            //     partsTrend == "flat"
            //       ? "#3b82f6"
            //       : partsTrend == "down"
            //       ? "#ef4444"
            //       : "#10b981",
            // },
            { label: "Min", value: "2.3k" },
            { label: "Max", value: "2.8k" },
          ],
          tableHeaders: [
            "Supplier",
            "Vendor Code",
            "Number of Parts Supplied to TVSM and Part Numbers",
          ],
          tableData: partRecords,
        };
      case "completion":
        return {
          title:
            "Detailed Data - % Completion rate of LCA for the Identified parts",
          subtitle: "12 data points • 1Y period",
          stats: [
            { label: "Current", value: currentCompletion + "%" },
            // {
            //   label: "Target",
            //   value: completionVariance + "%",
            //   color:
            //     completionTrend == "flat"
            //       ? "#3b82f6"
            //       : completionTrend == "down"
            //       ? "#ef4444"
            //       : "#10b981",
            // },
            { label: "Min", value: "65" },
            { label: "Max", value: "79" },
          ],
          tableHeaders: [
            "Supplier",
            "Vendor Code",
            "Number of Parts Supplied to TVSM",
            "Part numbers",
            "Parts for which LCA is submitted",
          ],
          tableData: completionRecords,
        };
      case "suppliers":
        return {
          title: "Detailed Data - No. of suppliers requiring LCA submission",
          subtitle: "12 data points • 1Y period",
          stats: [
            { label: "Current", value: currentSuppliers + " suppliers" },
            // {
            //   label: "Target",
            //   value: suppliersVariance + "%",
            //   color:
            //     suppliersTrend == "flat"
            //       ? "#3b82f6"
            //       : suppliersTrend == "down"
            //       ? "#ef4444"
            //       : "#10b981",
            // },
            { label: "Min", value: "156" },
            { label: "Max", value: "180" },
          ],
          tableHeaders: [
            "Supplier",
            "Vendor Code",
            "Number of Parts Supplied to TVSM",
            "Part numbers",
          ],
          tableData: supplierRecords,
        };
      case "submission":
        return {
          title:
            "Detailed Data - % Completion rate of LCA Submission by supplier",
          subtitle: "12 data points • 1Y period",
          stats: [
            { label: "Current", value: currentSubmission + "%" },
            // {
            //   label: "Target",
            //   value: submissionVariance + "%",
            //   color:
            //     submissionTrend == "flat"
            //       ? "#3b82f6"
            //       : submissionTrend == "down"
            //       ? "#ef4444"
            //       : "#10b981",
            // },
            { label: "Min", value: "45" },
            { label: "Max", value: "65" },
          ],
          tableHeaders: [
            "Supplier",
            "Vendor Code",
            "Number of Parts Supplied to TVSM",
            "Part numbers",
            "Status %",
          ],
          tableData: submissionRecords,
        };
      default:
        return null;
    }
  };

  const LCAPartsChart = () => (
    <Card className="p-3" style={{ marginBottom: "20px" }}>
      <div className="flex align-items-center mb-3">
        <div
          className="flex align-items-center justify-content-center mr-3"
          style={{
            width: "40px",
            height: "40px",
            backgroundColor: "#6366f1",
            borderRadius: "8px",
          }}
        >
          <i className="pi pi-chart-bar text-white"></i>
        </div>
        <div>
          <h6 className="m-0 text-900 font-semibold">
            Absolute numbers of Identified parts
          </h6>
          <p className="m-0 text-600 text-sm">LCA Management</p>
        </div>
      </div>

      <div className="flex align-items-end justify-content-between mb-3">
        <div>
          <h2 className="m-0 text-900 font-bold">
            {currentParts.toLocaleString()}
            <span className="text-600 text-lg font-normal ml-1">parts</span>
          </h2>
        </div>
        <div className="flex align-items-center">
          <i
            className={`pi  mr-1 ${
              partsTrend === "down"
                ? "pi-arrow-down text-red-500"
                : partsTrend === "flat"
                ? "text-black-500"
                : "pi-arrow-up text-green-500"
            }`}
          ></i>
          <span
            className={`font-semibold text-sm ${
              partsTrend === "down"
                ? "text-red-500"
                : partsTrend === "flat"
                ? "text-yellow-500"
                : "text-green-500"
            }`}
          >
            &nbsp;
          </span>
        </div>
      </div>

      <div className="flex align-items-center gap-2 mb-3">
        {["3M", "6M", "1Y", "Max"].map((range) => (
          <button
            key={range}
            className={`p-button p-button-text p-button-sm ${
              partsTimeRange === range ? "p-button-outlined" : ""
            }`}
            onClick={() => setPartsTimeRange(range)}
            style={{
              padding: "4px 12px",
              fontSize: "12px",
              color: partsTimeRange === range ? "#6366f1" : "#6b7280",
            }}
          >
            {range}
          </button>
        ))}
      </div>

      <div style={{ height: "200px", width: "100%" }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={getPartsData()}>
            <defs>
              <linearGradient id="partsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#6366f1" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#6366f1" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="month"
              angle={-45}
              textAnchor="end"
              height={50}
              tick={{ fontSize: 12, fill: "#6b7280" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6b7280" }}
              tickFormatter={(value) => `${value.toFixed(1)}`}
              domain={["auto", "auto"]}
            />
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <Area
              type="monotone"
              dataKey="value"
              stroke={
                partsTrend === "down"
                  ? "#ef4444"
                  : partsTrend === "flat"
                  ? "#f59e0b"
                  : "#10b981"
              }
              strokeWidth={3}
              fill={`url(#submissionGradient-${partsTrend})`}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="flex justify-content-end mt-2">
        <button
          className="p-button p-button-text p-button-sm"
          onClick={() => handleShowDetails("parts")}
          style={{
            fontSize: "12px",
            color: activeDetailView === "parts" ? "#6366f1" : "#6b7280",
            padding: "4px 8px",
          }}
        >
          {activeDetailView === "parts" ? "Hide Details" : "Show Details"}
          <i
            className={`pi pi-chevron-${
              activeDetailView === "parts" ? "up" : "down"
            } ml-1`}
          ></i>
        </button>
      </div>
    </Card>
  );

  const LCACompletionChart = () => (
    <Card className="p-3" style={{ marginBottom: "20px" }}>
      <div className="flex align-items-center mb-3">
        <div
          className="flex align-items-center justify-content-center mr-3"
          style={{
            width: "40px",
            height: "40px",
            backgroundColor: "#6366f1",
            borderRadius: "8px",
          }}
        >
          <i className="pi pi-chart-line text-white"></i>
        </div>
        <div>
          <h6 className="m-0 text-900 font-semibold">
            % Completion rate of LCA for the Identified parts
          </h6>
          <p className="m-0 text-600 text-sm">LCA Management</p>
        </div>
      </div>

      <div className="flex align-items-end justify-content-between mb-3">
        <div>
          <h2 className="m-0 text-900 font-bold">
            {currentCompletion.toFixed(1)}
            <span className="text-600 text-lg font-normal ml-1">%</span>
          </h2>
        </div>
        <div className="flex align-items-center">
          <i
            className={`pi mr-1 ${
              completionTrend === "down"
                ? "pi-arrow-down text-red-500"
                : completionTrend === "flat"
                ? "text-black-500"
                : "pi-arrow-up text-green-500"
            }`}
          ></i>
          <span
            className={`font-semibold text-sm ${
              completionTrend === "down"
                ? "text-red-500"
                : completionTrend === "flat"
                ? "text-yellow-500"
                : "text-green-500"
            }`}
          >
            &nbsp;
          </span>
        </div>
      </div>

      <div className="flex align-items-center gap-2 mb-3">
        {["3M", "6M", "1Y", "Max"].map((range) => (
          <button
            key={range}
            className={`p-button p-button-text p-button-sm ${
              completionTimeRange === range ? "p-button-outlined" : ""
            }`}
            onClick={() => setCompletionTimeRange(range)}
            style={{
              padding: "4px 12px",
              fontSize: "12px",
              color: completionTimeRange === range ? "#6366f1" : "#6b7280",
            }}
          >
            {range}
          </button>
        ))}
      </div>

      <div style={{ height: "200px", width: "100%" }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={getCompletionData()}>
            <defs>
              <linearGradient
                id="completionGradient"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="month"
              angle={-45}
              textAnchor="end"
              height={50}
              tick={{ fontSize: 12, fill: "#6b7280" }}
            />
            <YAxis
              domain={[0, 100]}
              tickFormatter={(value) => `${value}%`}
              tick={{ fontSize: 12, fill: "#6b7280" }}
            />
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <Area
              type="monotone"
              dataKey="completionRate"
              stroke={
                completionTrend === "down"
                  ? "#ef4444"
                  : completionTrend === "flat"
                  ? "#f59e0b"
                  : "#10b981"
              }
              strokeWidth={3}
              fill={`url(#submissionGradient-${completionTrend})`}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="flex justify-content-end mt-2">
        <button
          className="p-button p-button-text p-button-sm"
          onClick={() => handleShowDetails("completion")}
          style={{
            fontSize: "12px",
            color: activeDetailView === "completion" ? "#6366f1" : "#6b7280",
            padding: "4px 8px",
          }}
        >
          {activeDetailView === "completion" ? "Hide Details" : "Show Details"}
          <i
            className={`pi pi-chevron-${
              activeDetailView === "completion" ? "up" : "down"
            } ml-1`}
          ></i>
        </button>
      </div>
    </Card>
  );

  const LCASuppliersChart = () => (
    <Card className="p-3" style={{ marginBottom: "20px" }}>
      <div className="flex align-items-center mb-3">
        <div
          className="flex align-items-center justify-content-center mr-3"
          style={{
            width: "40px",
            height: "40px",
            backgroundColor: "#6366f1",
            borderRadius: "8px",
          }}
        >
          <i className="pi pi-users text-white"></i>
        </div>
        <div>
          <h6 className="m-0 text-900 font-semibold">
            No. of suppliers requiring LCA submission
          </h6>
          <p className="m-0 text-600 text-sm">LCA Management</p>
        </div>
      </div>

      <div className="flex align-items-end justify-content-between mb-3">
        <div>
          <h2 className="m-0 text-900 font-bold">
            {currentSuppliers}
            <span className="text-600 text-lg font-normal ml-1">suppliers</span>
          </h2>
        </div>
        <div className="flex align-items-center">
          <i
            className={`pi  mr-1 ${
              suppliersTrend === "down"
                ? "pi-arrow-down text-red-500"
                : suppliersTrend === "flat"
                ? "text-black-500"
                : "pi-arrow-up text-green-500"
            }`}
          ></i>
          <span
            className={`font-semibold text-sm ${
              suppliersTrend === "down"
                ? "text-red-500"
                : suppliersTrend === "flat"
                ? "text-yellow-500"
                : "text-green-500"
            }`}
          >
            &nbsp;
          </span>
        </div>
      </div>

      <div className="flex align-items-center gap-2 mb-3">
        {["3M", "6M", "1Y", "Max"].map((range) => (
          <button
            key={range}
            className={`p-button p-button-text p-button-sm ${
              suppliersTimeRange === range ? "p-button-outlined" : ""
            }`}
            onClick={() => setSuppliersTimeRange(range)}
            style={{
              padding: "4px 12px",
              fontSize: "12px",
              color: suppliersTimeRange === range ? "#6366f1" : "#6b7280",
            }}
          >
            {range}
          </button>
        ))}
      </div>

      <div style={{ height: "200px", width: "100%" }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={getSuppliersData()}>
            <defs>
              <linearGradient
                id="suppliersGradient"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="month"
              angle={-45}
              textAnchor="end"
              height={50}
              tick={{ fontSize: 12, fill: "#6b7280" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6b7280" }}
              tickFormatter={(value) => `${value.toFixed(1)}`}
              domain={["auto", "auto"]}
            />
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <Area
              type="monotone"
              dataKey="suppliers"
              stroke={
                suppliersTrend === "down"
                  ? "#ef4444"
                  : suppliersTrend === "flat"
                  ? "#f59e0b"
                  : "#10b981"
              }
              strokeWidth={3}
              fill={`url(#submissionGradient-${suppliersTrend})`}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="flex justify-content-end mt-2">
        <button
          className="p-button p-button-text p-button-sm"
          onClick={() => handleShowDetails("suppliers")}
          style={{
            fontSize: "12px",
            color: activeDetailView === "suppliers" ? "#6366f1" : "#6b7280",
            padding: "4px 8px",
          }}
        >
          {activeDetailView === "suppliers" ? "Hide Details" : "Show Details"}
          <i
            className={`pi pi-chevron-${
              activeDetailView === "suppliers" ? "up" : "down"
            } ml-1`}
          ></i>
        </button>
      </div>
    </Card>
  );

  const LCASubmissionChart = () => (
    <Card className="p-3" style={{ marginBottom: "20px" }}>
      <div className="flex align-items-center mb-3">
        <div
          className="flex align-items-center justify-content-center mr-3"
          style={{
            width: "40px",
            height: "40px",
            backgroundColor: "#6366f1",
            borderRadius: "8px",
          }}
        >
          <i className="pi pi-check-circle text-white"></i>
        </div>
        <div>
          <h6 className="m-0 text-900 font-semibold">
            % Completion rate of LCA Submissions by supplier
          </h6>
          <p className="m-0 text-600 text-sm">LCA Management</p>
        </div>
      </div>

      <div className="flex align-items-end justify-content-between mb-3">
        <div>
          <h2 className="m-0 text-900 font-bold">
            {currentSubmission}
            <span className="text-600 text-lg font-normal ml-1">%</span>
          </h2>
        </div>
        <div className="flex align-items-center">
          <i
            className={`pi  mr-1 ${
              submissionTrend === "down"
                ? "pi-arrow-down text-red-500"
                : submissionTrend === "flat"
                ? "text-black-500"
                : "pi-arrow-up text-green-500"
            }`}
          ></i>
          <span
            className={`font-semibold text-sm ${
              submissionTrend === "down"
                ? "text-red-500"
                : submissionTrend === "flat"
                ? "text-yellow-500"
                : "text-green-500"
            }`}
          >
            &nbsp;
          </span>
        </div>
      </div>

      <div className="flex align-items-center gap-2 mb-3">
        {["3M", "6M", "1Y", "Max"].map((range) => (
          <button
            key={range}
            className={`p-button p-button-text p-button-sm ${
              submissionTimeRange === range ? "p-button-outlined" : ""
            }`}
            onClick={() => setSubmissionTimeRange(range)}
            style={{
              padding: "4px 12px",
              fontSize: "12px",
              color: submissionTimeRange === range ? "#6366f1" : "#6b7280",
            }}
          >
            {range}
          </button>
        ))}
      </div>

      <div style={{ height: "200px", width: "100%" }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={getSubmissionData()}>
            <defs>
              <linearGradient
                id="submissionGradient"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="month"
              angle={-45}
              textAnchor="end"
              height={50}
              tick={{ fontSize: 12, fill: "#6b7280" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6b7280" }}
              tickFormatter={(value) => `${value.toFixed(1)}`}
              domain={["auto", "auto"]}
            />
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <Area
              type="monotone"
              dataKey="submission"
              stroke={
                submissionTrend === "down"
                  ? "#ef4444"
                  : submissionTrend === "flat"
                  ? "#f59e0b"
                  : "#10b981"
              }
              strokeWidth={3}
              fill={`url(#submissionGradient-${submissionTrend})`}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="flex justify-content-end mt-2">
        <button
          className="p-button p-button-text p-button-sm"
          onClick={() => handleShowDetails("submission")}
          style={{
            fontSize: "12px",
            color: activeDetailView === "submission" ? "#6366f1" : "#6b7280",
            padding: "4px 8px",
          }}
        >
          {activeDetailView === "submission" ? "Hide Details" : "Show Details"}
          <i
            className={`pi pi-chevron-${
              activeDetailView === "submission" ? "up" : "down"
            } ml-1`}
          ></i>
        </button>
      </div>
    </Card>
  );

  return (
    <div className="lca-dashboard mt-4">
      <div
        className="grid"
        style={{ display: "flex", flexWrap: "nowrap", gap: "1rem" }}
      >
        <div style={{ flex: "1", minWidth: "250px" }}>
          <LCAPartsChart />
        </div>
        <div style={{ flex: "1", minWidth: "250px" }}>
          <LCACompletionChart />
        </div>
        <div style={{ flex: "1", minWidth: "250px" }}>
          <LCASuppliersChart />
        </div>
        <div style={{ flex: "1", minWidth: "250px" }}>
          <LCASubmissionChart />
        </div>
      </div>

      {/* Full-width detailed data section */}
      {activeDetailView && (
        <div
          className="mt-4 p-4"
          style={{
            backgroundColor: "#eff6ff",
            borderRadius: "12px",
            border: "1px solid #bfdbfe",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
          }}
        >
          {(() => {
            const detailData = getDetailedDataContent();
            return (
              <>
                <div className="flex justify-content-between align-items-center mb-4">
                  <div>
                    <h5
                      className="mt-0 mb-1 font-semibold"
                      style={{ color: "#1f2937", fontSize: "18px" }}
                    >
                      {detailData.title}
                    </h5>
                    <p className="m-0 text-sm" style={{ color: "#6b7280" }}>
                      {detailData.subtitle}
                    </p>
                  </div>
                  <button
                    className="p-button p-button-sm flex align-items-center"
                    style={{
                      backgroundColor: "#2563eb",
                      color: "white",
                      border: "none",
                      padding: "8px 16px",
                      borderRadius: "8px",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    <i className="pi pi-download mr-2"></i>
                    Export Data
                  </button>
                </div>

                <div
                  className="grid mb-4 p-4"
                  style={{
                    backgroundColor: "white",
                    borderRadius: "8px",
                    border: "1px solid #f3f4f6",
                  }}
                >
                  {detailData.stats.map((stat, index) => (
                    <div key={index} className="col-3 text-center">
                      <p
                        className="m-0 text-sm font-medium"
                        style={{ color: "#6b7280" }}
                      >
                        {stat.label}
                      </p>
                      <p
                        className="m-0 text-lg font-semibold"
                        style={{ color: stat.color || "#1f2937" }}
                      >
                        {stat.value}
                      </p>
                    </div>
                  ))}
                </div>

                <div
                  style={{
                    backgroundColor: "white",
                    borderRadius: "8px",
                    border: "1px solid #e2e8f0",
                    overflow: "hidden",
                    boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <table style={{ width: "100%", borderCollapse: "collapse" }}>
                    <thead>
                      <tr
                        style={{
                          backgroundColor: "#f8fafc",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        {detailData.tableHeaders.map((header, index) => (
                          <th
                            key={index}
                            style={{
                              padding: "12px 16px",
                              textAlign: "left",
                              fontSize: "14px",
                              fontWeight: "600",
                              color: "#374151",
                            }}
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {detailData.tableData.map((row, index) => (
                        <tr
                          key={index}
                          style={{
                            borderBottom:
                              index < detailData.tableData.length - 1
                                ? "1px solid #f3f4f6"
                                : "none",
                          }}
                        >
                          <td
                            style={{
                              padding: "16px",
                              fontSize: "14px",
                              fontWeight: "500",
                              color: "#1f2937",
                            }}
                          >
                            {row.supplier}
                          </td>
                          <td
                            style={{
                              padding: "16px",
                              fontSize: "14px",
                              fontWeight: "600",
                              color: "#1f2937",
                            }}
                          >
                            {row.code}
                          </td>
                          {activeDetailView === "suppliers" ? (
                            <>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#374151",
                                }}
                              >
                                {row.parts}
                              </td>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#3b82f6",
                                }}
                              >
                                {row.partNumbers}
                              </td>
                            </>
                          ) : activeDetailView === "submission" ? (
                            <>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#374151",
                                }}
                              >
                                {row.parts}
                              </td>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#3b82f6",
                                }}
                              >
                                {row.partNumbers}
                              </td>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#3b82f6",
                                }}
                              >
                                {row.status}
                              </td>
                            </>
                          ) : activeDetailView === "parts" ? (
                            <td
                              style={{
                                padding: "16px",
                                fontSize: "14px",
                                color: "#374151",
                              }}
                            >
                              {row.parts}
                            </td>
                          ) : (
                            <>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#374151",
                                }}
                              >
                                {row.parts}
                              </td>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#3b82f6",
                                }}
                              >
                                {row.partNumbers}
                              </td>
                              <td
                                style={{
                                  padding: "16px",
                                  fontSize: "14px",
                                  color: "#3b82f6",
                                }}
                              >
                                {row.submitted}
                              </td>
                            </>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table> */}

                  <DataTable
                    value={detailData.tableData}
                    paginator
                    rows={10}
                    className="p-datatable-striped"
                    responsiveLayout="scroll"
                    exportFilename="LCA_Submission_Export"
                    globalFilter={globalFilter}
                    header={
                      <div className="flex justify-content-between items-center">
                        <h5 className="m-0">{detailData.title}</h5>
                        <Button
                          icon="pi pi-download"
                          label="Export"
                          className="p-button-sm"
                          onClick={() => dt.current.exportCSV()}
                        />
                      </div>
                    }
                    ref={dt}
                  >
                    <Column
                      field="supplier"
                      header="Supplier"
                      filter
                      showFilterMatchModes={false}
                      filterPlaceholder="Search..."
                      sortable
                    />
                    <Column
                      field="code"
                      header="Vendor Code"
                      filter
                      showFilterMatchModes={false}
                      filterPlaceholder="Search..."
                      sortable
                    />

                    {(activeDetailView === "suppliers" ||
                      activeDetailView === "submission" ||
                      activeDetailView === "parts" ||
                      activeDetailView === "completion") && (
                      <Column
                        field="parts"
                        header="Number of Parts"
                        filterPlaceholder="Search..."
                        sortable
                      />
                    )}

                    {(activeDetailView === "suppliers" ||
                      activeDetailView === "submission" ||
                      activeDetailView === "completion") && (
                      <Column
                        field="partNumbers"
                        header="Part Numbers"
                        filter
                        showFilterMatchModes={false}
                        filterPlaceholder="Search..."
                        sortable
                      />
                    )}

                    {activeDetailView === "submission" && (
                      <Column field="status" header="Status %" sortable />
                    )}

                    {activeDetailView === "completion" && (
                      <Column
                        field="submitted"
                        header="Parts Submitted"
                        filter
                        showFilterMatchModes={false}
                        filterPlaceholder="Search..."
                        sortable
                      />
                    )}
                  </DataTable>
                </div>
              </>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default LCADashboard;
