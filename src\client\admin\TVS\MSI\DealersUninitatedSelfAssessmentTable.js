import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import moment from 'moment';

const DealersUninitatedSelfAssessmentTable = ({ data, dealerList, assessorList, globalFilter }) => {
    const [databk, setDatabk] = useState([]);
    const [datas, setDatas] = useState([]);
    const [search, setSearch] = useState('');
    const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);
    const [filteredDataCount, setFilteredDataCount] = useState(0);
    const [currentFilteredData, setCurrentFilteredData] = useState([]);
    const dataTableRef = useRef(null);

    // Add state for pagination
    const [rows, setRows] = useState(10);
    const [first, setFirst] = useState(0);

    // Add filter state management for DataTable
    const [tableFilters, setTableFilters] = useState({
        dealerName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        zone: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null }
    });

    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter;
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.dealerName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }

        // Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        setDatas(indexedData);
    };

    useEffect(() => {
        setDatabk(data);
        applyFilters(data);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    // Initialize filtered count when data changes
    useEffect(() => {
        // Set initial count when data is loaded or when search/date filters change
        // This ensures the count is always up to date
        setFilteredDataCount(datas.length);
        console.log('Data changed, setting filtered count to:', datas.length);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [datas.length]);

    useEffect(() => {
        const fetchSubmissions = async () => {
            try {
                const res = await APIServices.get(API.DealerSelfSubmission);
                setDealerSelfSubmissions(res?.data || []);
            } catch (error) {
                console.error('Error fetching DealerSelfSubmission:', error);
            }
        };

        fetchSubmissions();
    }, []);

    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]
       const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]
    const searchFn = (e) => {
        let val = e.target.value;
        setSearch(val);
        applyFilters(databk, val);
    };

    const calibrationIdBodyTemplate = (rowData) => {
        return (
            <span>
                {'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
            </span>
        );
    };

    const nameTemplate = (rowData) => {
        return <div>{rowData?.vendor?.dealerName || 'NA'}</div>;
    };

    const locationTemplate = (rowData) => {
        return <div>{rowData?.vendor?.dealerLocation || 'NA'}</div>;
    };

    const zoneTemplate = (rowData) => {
        return <div>{zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA'}</div>;
    };

    const categoryTemplate = (rowData) => {
        return <div>{dealerType.find(x => x.value === rowData?.vendor?.dealerCategory)?.name || 'NA'}</div>;
    };
    const getLastSubmissionMonth = (rowData) => {
        if (!Array.isArray(dealerSelfSubmissions)) return '';
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });
        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return '';
console.log(DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy'))
        return DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy').toFormat('LLLL yyyy');

    };
    const selfAssessmentMonthSort = (e) => {
        console.log(e.data)
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB) return 0;
                if (!monthA) return 1;
                if (!monthB) return -1;

                const dateA = DateTime.fromFormat(monthA, 'LLLL yyyy')
                const dateB = DateTime.fromFormat(monthB, 'LLLL yyyy')
                console.log(dateA.isValid, dateB.isValid);
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB) return 0;
                if (!monthA) return 1;
                if (!monthB) return -1;


                const dateA = DateTime.fromFormat(monthA, 'LLLL yyyy')
                const dateB = DateTime.fromFormat(monthB, 'LLLL yyyy')

                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    }
    const selfAssessmentMonthTemplate = (rowData) => {
        if (!Array.isArray(dealerSelfSubmissions)) return 'NA';

        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return 'NA';

        return moment(matched[0].reporting_period[0], 'MM-YYYY').format('MMMM YYYY');
    };

    const getSelfAssessmentScore = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length > 0) {
            try {
                const scoreObj = JSON.parse(matched[0].score || '{}');
                return scoreObj.overallScore ?? 'NA';
            } catch (e) {
                return 'NA';
            }
        }

        return 'NA';
    };

    const getRatingName = (score) => {
        if (!score || score === '-' || score === 'NA') return 'NA';
        score = parseFloat(score);
        if (score >= 85) return 'Platinum';
        if (score > 70) return 'Gold';
        if (score > 55) return 'Silver';
        return 'Not Met';
    };

    const sortIndexColumn = (e) => {
        const { data, order } = e;

        // Create a new array with the current data and add an index property
        const indexedData = data.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Sort based on the index
        if (order === 1) { // ascending
            return indexedData.sort((a, b) => a.tableIndex - b.tableIndex);
        } else { // descending
            return indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
        }
    };

    const RowFilterTemplate = (options, obj) => {
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(datas.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    const exportExcel = () => {
        if (!dealersWithoutSelfAssessments || dealersWithoutSelfAssessments.length === 0) {
            alert('No data to export.');
            return;
        }

        const exportData = dealersWithoutSelfAssessments.map((item) => ({
            'S.No': item.tableIndex || '',
            'Calibration ID': item.vendor?.code ? `MSI-${item.vendor.code}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Dealer Name': item.vendor?.dealerName || 'NA',
            'Location': item.vendor?.dealerLocation || 'NA',
            'Zone': zonalOfficeList.find(x => x.value === item.vendor?.dealerZone)?.name || 'NA',
            'Category': dealerType.find(x => x.value === item.vendor?.dealerCategory)?.name || 'NA',
            'Self-assessment Month': selfAssessmentMonthTemplate(item)
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Uninitiated Self Assessments');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Uninitiated_Self_Assessments_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };

    // Filter data to only include dealers without valid self-assessment scores
    const dealersWithoutSelfAssessments = datas.filter(dealer => {
        const selfAssessmentScore = getSelfAssessmentScore(dealer);
        return selfAssessmentScore === 'NA' || selfAssessmentScore === '-';
    });

    // Memoize the enhanced data to prevent unnecessary re-computations
    const enhancedData = useMemo(() => {
        return dealersWithoutSelfAssessments.map((x, index) => ({
            ...x,
            tableIndex: index + 1,
            latestSubmission: getLastSubmissionMonth(x),
            selfAssessmentGrade: getRatingName(getSelfAssessmentScore(x))
        }));
    }, [dealersWithoutSelfAssessments, dealerSelfSubmissions]);

    // Manual filter calculation function as a fallback
    const calculateFilteredCount = useCallback(() => {
        if (!enhancedData || enhancedData.length === 0) return 0;

        let filteredData = [...enhancedData];

        // Apply table filters
        Object.entries(tableFilters).forEach(([field, filter]) => {
            if (filter.value && filter.value.length > 0) {
                filteredData = filteredData.filter(item => {
                    const fieldValue = item[field];
                    if (fieldValue == null) return false;
                    return filter.value.includes(fieldValue);
                });
            }
        });

        // Apply global filter if present
        if (globalFilter && globalFilter.trim()) {
            const globalFilterLower = globalFilter.toLowerCase();
            filteredData = filteredData.filter(item => {
                return Object.values(item).some(value =>
                    value && value.toString().toLowerCase().includes(globalFilterLower)
                );
            });
        }

        return filteredData.length;
    }, [enhancedData, tableFilters, globalFilter]);

    // Initialize filtered count when enhanced data changes
    useEffect(() => {
        // Only update if no filters are currently applied
        const hasActiveFilters = Object.values(tableFilters).some(filter =>
            filter.value && filter.value.length > 0
        );

        if (!hasActiveFilters && !globalFilter) {
            setFilteredDataCount(enhancedData.length);
            console.log('Enhanced data changed, updating count to:', enhancedData.length);
        }
    }, [enhancedData.length, tableFilters, globalFilter]);

    // Backup mechanism to ensure count accuracy
    useEffect(() => {
        const updateCountFromCalculation = () => {
            const calculatedCount = calculateFilteredCount();

            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Manual calculation: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        };

        // Check periodically for any missed updates
        const interval = setInterval(updateCountFromCalculation, 3000);

        return () => clearInterval(interval);
    }, [calculateFilteredCount, filteredDataCount]);

    // Also trigger manual calculation when filters change
    useEffect(() => {
        const calculatedCount = calculateFilteredCount();
        console.log('🔢 Filters changed, calculated count:', calculatedCount);

        // Small delay to allow DataTable to process first
        const timeout = setTimeout(() => {
            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Filter change: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        }, 500);

        return () => clearTimeout(timeout);
    }, [tableFilters, globalFilter, calculateFilteredCount]);

    return (
        <>
            <div className="col-12 flex justify-content-between align-items-center mb-3" >
                <div className='col-5'>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText value={search} style={{ width: '100%' }} onChange={searchFn} placeholder="Search Code/Name" />
                    </span>
                </div>
            </div>
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4>Uninitiated MSI Self-assessments ({filteredDataCount})</h4>
                <button
                    className="btn btn-sm btn-success"
                    onClick={exportExcel}
                >
                    Download Excel
                </button>
            </div>

            <DataTable
                ref={dataTableRef}
                value={enhancedData}
                paginator
                rows={rows}
                first={first}
                 rowsPerPageOptions={[10, 25, 50, 100,150,200]}
                scrollable
                scrollHeight="500px"
                filters={tableFilters}
                filterDisplay="menu"
                onPage={(e) => {
                     setFirst(e.first);
                setRows(e.rows);
                    console.log('📄 Page changed:', e);
                    // Ensure count remains accurate when navigating pages
                    const currentCount = calculateFilteredCount();
                    if (currentCount !== filteredDataCount) {
                        console.log('🔄 Page change: Updating count to:', currentCount);
                        setFilteredDataCount(currentCount);
                    }
                }}
                onFilter={(e) => {
                    console.log('🔍 DataTable onFilter triggered');
                    console.log('Event object:', e);

                    // Create a copy of the filters object
                    const cleanedFilters = { ...e.filters };

                    if (cleanedFilters.hasOwnProperty('null')) {
                        delete cleanedFilters['null'];
                    }

                    setTableFilters(cleanedFilters);

                    // Update filtered data count based on the actual filtered results
                    // e.filteredValue contains ALL filtered records across all pages
                    let filteredCount;

                    if (e.filteredValue && Array.isArray(e.filteredValue)) {
                        filteredCount = e.filteredValue.length;
                        console.log('✅ Using e.filteredValue.length:', filteredCount);
                    } else {
                        // Fallback to manual calculation
                        filteredCount = calculateFilteredCount();
                        console.log('⚠️ Fallback to manual calculation:', filteredCount);
                    }

                    console.log('🔢 Filter applied:', cleanedFilters);
                    console.log('🔢 Final filtered count (total across all pages):', filteredCount);
                    console.log('🔢 Enhanced data length:', enhancedData.length);

                    // Update the count and filtered data immediately
                    setFilteredDataCount(filteredCount);
                    setCurrentFilteredData(e.filteredValue || enhancedData);
                }}
                globalFilter={globalFilter}
                className="mt-2 h-500"
            >
                <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={sortIndexColumn} />
                <Column sortable field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "msiId")
                    } />
                <Column sortable field="dealerName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "dealerName")
                    } />
                <Column sortable field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "location")
                    } />
                <Column sortable field="zone" header="Zone" body={zoneTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "zone")
                    } />
                <Column sortable field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "cat")
                    } />
                <Column sortable field="latestSubmission" header="Self-assessment Month" body={selfAssessmentMonthTemplate} sortFunction={selfAssessmentMonthSort} />
            </DataTable>
        </>
    );
};

export default DealersUninitatedSelfAssessmentTable;
