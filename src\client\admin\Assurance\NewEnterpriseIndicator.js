import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react'
import APIServices from '../../../service/APIService'
import { API } from '../../../constants/api_url'
import { red } from '@mui/material/colors';
import { MultiSelect } from 'primereact/multiselect';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { useSelector } from 'react-redux';
import { filterDataByTierAndLocationByLevel, getFiscalYearsFromStartDate } from '../../../components/BGHF/helper';
import {
    Tab,
    Tabs,
    Box,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
} from "@mui/material";
import { Dropdown } from 'primereact/dropdown';
import { Badge } from 'primereact/badge';
import Swal from 'sweetalert2';

export const NewEnterpriseIndicator = ({ admin, user }) => {
    const [yearOption, setYearOption] = useState([]);
    const [customMetricResponseBk, setCustomMetricResponseBk] = useState([]);
    const [customMetricResponse, setCustomMetricResponse] = useState([]);
    const [metricsData, setMetricsData] = useState([]);
    const [metricsDataBk, setMetricsDataBk] = useState([]);
    const [indidcatorlist, setIndicatorList] = useState([])
    const [indifilter, setIndiFilter] = useState({ year: 0, section: 0, indicator: 0, form: 0, framework: [], entity: [] })
    const [load, setLoad] = useState([]);
    const [exportProgress, setExportProgress] = useState({ show: false, progress: 0, message: '' });
    const [first, setFirst] = useState(0);
    const [rows, setRows] = useState(100); // Show 100 rows per page initially
    const [filters, setFilters] = useState({
        entity: { matchMode: 'in', value: null },
        indicatorType: { matchMode: 'in', value: null },
        status: { matchMode: 'in', value: null },
        reporting_period: { matchMode: 'in', value: null },
        title: { matchMode: 'in', value: null },
        emissionFactorName: { matchMode: 'in', value: null }
    });

    const [assFramework, setAssFramework] = useState([])
    const [rawsitelist, setRawsitelist] = useState([]);
    const [locList, setLocList] = useState({ country: [], city: [], location: [] })
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);
    const tableRef = useRef(null)
    useEffect(() => {
        renderData()
    }, [])
    const renderData = async () => {
        let yrOptions = getFiscalYearsFromStartDate(
            admin.information.startdate, fymonth
        );
        setLoad(true)
        setYearOption(yrOptions)
        try {

            if (yrOptions.length) {
                const promise3 = await APIServices.post(API.GetAssignedIndicator_UP(admin.id))
                setIndicatorList(promise3.data)
                let uriStringLoc = {
                    include: [
                        {
                            relation: "locationTwos",
                            scope: { include: [{ relation: "locationThrees" }] },
                        },
                    ],
                };
                const promise0 = APIServices.get(API.Report_Name_Twos)
                const promise1 = APIServices.post(
                    API.LocationByRoles(admin.id), { userId: user.id, roles: [2] }
                );
                const promise2 = APIServices.post(API.GetAssuranceIndicatorNew_UP(admin.id), { userId: user.id, indicatorId: [promise3.data?.[0]?.id || 0], year: { startMonth: yrOptions.slice(-1)?.[0]?.startMonth, endMonth: yrOptions.slice(-1)?.[0]?.endMonth } })


                Promise.all([promise0, promise1, promise2]).then((values) => {
                    console.log(values[2].data)
                    let allframework = values[0].data.filter((i) => { return admin.information.report.includes(i.id) })
                    setIndiFilter((prev) => ({ ...prev, indicator:promise3.data?.[0]?.id, year: yrOptions.slice(-1)[0].name, framework: allframework.map(x => x.title) }))
                    const shapedSite = values[1]?.data
                        .map((item) => {
                            if (item.locationTwos) {
                                item.locationTwos = item.locationTwos.filter(
                                    (locationTwo) =>
                                        locationTwo.locationThrees &&
                                        locationTwo.locationThrees?.length > 0
                                );
                            }
                            return item;
                        })
                        .filter((item) => item.locationTwos && item.locationTwos?.length > 0);
                    setRawsitelist(shapedSite)
                    setLocList({ country: [{ name: 'All Countries', id: 0 }, ...shapedSite.map(location => ({ name: location.name, id: location.id }))] });

                    setAssFramework(allframework)

                    // Get the data and pre-compute summary values
                    const rawData = values[2].data || [];

                    // Get the indicator unit for consistent display
                    const indicatorUnit = rawData.length > 0 ? rawData[0].indicatorUnit || 'tCo2e' : 'tCo2e';

                    // Calculate enterprise summary (total of all computed values)
                    const enterpriseTotal = rawData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

                    // Process the data to include pre-computed summaries
                    const processedData = rawData.map(item => {
                        // Calculate entity summary for this location
                        const locationData = rawData.filter(dataItem => dataItem.locationId === item.locationId);
                        const entityTotal = locationData.reduce((sum, dataItem) => sum + (parseFloat(dataItem.computedValue) || 0), 0);

                        return {
                            ...item,
                            // Pre-computed values to avoid recalculation on render
                            _entityTotal: entityTotal,
                            _enterpriseTotal: enterpriseTotal,
                            _indicatorUnit: indicatorUnit
                        };
                    });

                    setMetricsData(processedData)
                    setMetricsDataBk(processedData)
                }).then(() => {
                    setLoad(false)
                })

            }
        } catch {

        }
    }
    // Memoize the table rendering to prevent unnecessary re-renders
    const RenderTable = useMemo(() => {
        // Check if the selected indicator is derived (indicatorType === 2)
        const selectedIndicator = indidcatorlist.find(x => x.id === indifilter.indicator);
        const isDerivedIndicator = selectedIndicator?.indicatorType === 2;

        // Determine if we should use pagination or virtual scrolling based on data size
        const useVirtualScrolling = metricsData.length > 1000;
        const usePagination = metricsData.length > 5000;

        // Virtual scrolling configuration for large datasets
        const virtualScrollerOptions = useVirtualScrolling ? {
            lazy: false,
            itemSize: 46, // Height of each row in pixels
            delay: 100,
            showLoader: true,
            loading: load,
            numToleratedItems: 20,
            autoSize: true
        } : null;

        // Pagination configuration for very large datasets
        const paginatorTemplate = usePagination ? {
            layout: 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown',
            CurrentPageReport: (options) => {
                return (
                    <span style={{ color: 'var(--text-color)', userSelect: 'none', width: '120px', textAlign: 'center' }}>
                        {options.first} - {options.last} of {options.totalRecords}
                    </span>
                );
            }
        } : null;

        return (
            <DataTable
                    ref={tableRef}
                    loading={load}
                    value={metricsData}
                    scrollable
                    scrollHeight={usePagination ? "500px" : "600px"}
                    virtualScrollerOptions={virtualScrollerOptions}
                    paginator={usePagination}
                    first={usePagination ? first : undefined}
                    rows={usePagination ? rows : undefined}
                    totalRecords={usePagination ? metricsData.length : undefined}
                    onPage={usePagination ? (e) => {
                        setFirst(e.first);
                        setRows(e.rows);
                    } : undefined}
                    rowsPerPageOptions={usePagination ? [50, 100, 200, 500] : undefined}
                    paginatorTemplate={usePagination ? paginatorTemplate.layout : undefined}
                    currentPageReportTemplate={usePagination ? "Showing {first} to {last} of {totalRecords} entries" : undefined}
                    tableStyle={{ minWidth: '50rem' }}
                    filters={filters}
                    onFilter={(e) => {
                        setFilters(e.filters);
                    }}
                    filterDisplay="menu"
                    globalFilterFields={['entity', 'status', 'title', 'emissionFactorName']}
                    emptyMessage="No data found"
                    showGridlines
                    stripedRows
                >
                <Column
                    field="entity"
                    header={'Reporting Entity'}
                    showFilterMatchModes={false}
                    showFilterMenuOptions={false}
                    filterElement={(options) => RowFilterTemplate(options, "entity")}
                    filter
                    style={{ minWidth: '150px' }}
                    frozen
                />
                <Column
                    field="indicatorType"
                    header={'Indicator Type'}
                    showFilterMatchModes={false}
                    showFilterMenuOptions={false}
                    body={(rowData) => {
                        return rowData.indicatorType === 1 ? 'Standalone' :
                               rowData.indicatorType === 2 ? 'Derived' : "NA"
                    }}
                    filterElement={(options) => typeFilterTemplate(options)}
                    filter
                    style={{ minWidth: '120px' }}
                />
                {/* Conditionally render status column only for non-derived indicators */}
                {!isDerivedIndicator && (
                    <Column
                        field="status"
                        header={'Status'}
                        showFilterMatchModes={false}
                        showFilterMenuOptions={false}
                        filterElement={(options) => RowFilterTemplate(options, "status")}
                        filter
                        style={{ minWidth: '100px' }}
                    />
                )}
                <Column
                    field="reporting_period"
                    header={'Reporting Period'}
                    showFilterMatchModes={false}
                    showFilterMenuOptions={false}
                    filterElement={(options) => RowFilterTemplate(options, "reporting_period")}
                    filter
                    style={{ minWidth: '130px' }}
                />
                <Column
                    field="title"
                    header={'Contributing Data Points'}
                    showFilterMatchModes={false}
                    showFilterMenuOptions={false}
                    body={(rowData) => {
                        // For derived indicators, show "MT" + depIndicatorId + " " + depIndicatorTitle
                        if (isDerivedIndicator && rowData.depIndicatorId && rowData.depIndicatorTitle) {
                            return `MT${rowData.depIndicatorId} ${rowData.depIndicatorTitle}`;
                        }
                        // For standalone indicators, show the regular title
                        return rowData.title;
                    }}
                    filterElement={(options) => RowFilterTemplate(options, "title")}
                    filter
                    style={{ minWidth: '200px' }}
                />
                <Column
                    field="value"
                    header={'Total Quantity'}
                    style={{ minWidth: '120px' }}
                />
                <Column
                    field="unitOfMeasure"
                    header={'Unit of Measure'}
                    style={{ minWidth: '130px' }}
                />
                {/* Conditionally render emission-related columns only for non-derived indicators */}
                {!isDerivedIndicator && (
                    <>
                        <Column
                            field="emissionFactorName"
                            header={'Emission Factor (if applicable)'}
                            showFilterMatchModes={false}
                            showFilterMenuOptions={false}
                            filterElement={(options) => RowFilterTemplate(options, "emissionFactorName")}
                            filter
                            style={{ minWidth: '200px' }}
                        />
                        <Column
                            field="efkey"
                            header={'Emission Factor ID'}
                            style={{ minWidth: '150px' }}
                        />
                        <Column
                            field="emissionFactorValue"
                            header={'Emission Factor Value of Co2e in kgCo2e'}
                            style={{ minWidth: '200px' }}
                        />
                        <Column
                            field="emissionFactorCo2Value"
                            header={'Emission Factor Value of CO2 in kgCo2e'}
                            style={{ minWidth: '200px' }}
                        />
                        <Column
                            field="emissionFactorCh4Value"
                            header={'Emission Factor Value of Ch4 in kgCo2e'}
                            style={{ minWidth: '200px' }}
                        />
                        <Column
                            field="emissionFactorN2oValue"
                            header={'Emission Factor Value of N2o in kgCo2e'}
                            style={{ minWidth: '200px' }}
                        />
                        <Column
                            field="Net Calorific value (GJ/Tonne)"
                            header={'Net Calorific value (GJ/Tonne)'}
                            style={{ minWidth: '180px' }}
                        />
                        <Column
                            field="Density (kg/l)"
                            header={'Density (kg/l)'}
                            style={{ minWidth: '120px' }}
                        />
                    </>
                )}
                <Column
                    field="methodology"
                    header={'Formula'}
                    style={{ minWidth: '150px' }}
                />
                <Column
                    field="computedValue"
                    header={'Computed Value'}
                    body={(rowData) => {
                        return (rowData.computedValue !== 'NaN' && rowData.computedValue !== '' && rowData.computedValue !== '-')
                            ? (rowData.computedValue + ' ' + rowData._indicatorUnit) : '';
                    }}
                    style={{ minWidth: '140px' }}
                />
                {/* Conditionally render computed emission value columns only for non-derived indicators */}
                {!isDerivedIndicator && (
                    <>
                        <Column
                            field="computedCo2Value"
                            header={'Computed Value in tCo2'}
                            style={{ minWidth: '160px' }}
                        />
                        <Column
                            field="computedCh4Value"
                            header={'Computed Value in tCh4'}
                            style={{ minWidth: '160px' }}
                        />
                        <Column
                            field="computedN2oValue"
                            header={'Computed Value in tN2o'}
                            style={{ minWidth: '160px' }}
                        />
                    </>
                )}
                <Column
                    field="_entityTotal"
                    header={'Entity Summary'}
                    body={(rowData) => {
                        // Use pre-computed entity summary value
                        return rowData._entityTotal?.toFixed(2) + ' ' + rowData._indicatorUnit;
                    }}
                    style={{ minWidth: '140px' }}
                />
                <Column
                    field="_enterpriseTotal"
                    header={'Enterprise Summary'}
                    body={(rowData) => {
                        // Use pre-computed enterprise summary value
                        return rowData._enterpriseTotal?.toFixed(2) + ' ' + rowData._indicatorUnit;
                    }}
                    style={{ minWidth: '160px' }}
                />
            </DataTable>
        );
    }, [metricsData, load, first, rows, filters, indifilter.indicator, indidcatorlist]);

    const exportIndicatorReport = async () => {
        try {
            // Show progress indicator
            setExportProgress({ show: true, progress: 0, message: 'Preparing export...' });

            // Get the complete dataset for export
            let sourceData = metricsData; // Default to complete dataset

            // Check if any filters are active and manually apply them
            const hasActiveFilters = Object.values(filters).some(filter => filter.value && filter.value.length > 0);

            if (hasActiveFilters) {
                // Manually apply filters to get complete filtered dataset
                sourceData = metricsData.filter(item => {
                    // Check each filter
                    for (const [field, filterConfig] of Object.entries(filters)) {
                        if (filterConfig.value && filterConfig.value.length > 0) {
                            let fieldValue = item[field];

                            // Special handling for indicatorType
                            if (field === 'indicatorType') {
                                // filterConfig.value contains the numeric values (1, 2)
                                if (!filterConfig.value.includes(fieldValue)) {
                                    return false;
                                }
                            } else {
                                // For other fields, check if the field value is in the filter values
                                if (!filterConfig.value.includes(fieldValue)) {
                                    return false;
                                }
                            }
                        }
                    }
                    return true;
                });
                console.log('Applied filters manually. Filtered data:', sourceData.length, 'rows from', metricsData.length, 'total rows');
            } else {
                console.log('No active filters. Using complete dataset:', metricsData.length, 'rows');
            }

            console.log('Export Debug Info:', {
                totalMetricsData: metricsData.length,
                hasActiveFilters,
                sourceDataLength: sourceData.length,
                activeFilters: Object.entries(filters).filter(([, filter]) => filter.value && filter.value.length > 0)
            });

            if (!sourceData || sourceData.length === 0) {
                Swal.fire({
                    title: 'No Data Available',
                    text: 'No data available to export',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                setExportProgress({ show: false, progress: 0, message: '' });
                return;
            }

            // Prepare data for API call
            setExportProgress({ show: true, progress: 20, message: 'Preparing data for export...' });

            const exportData = {
                data: sourceData,
                iindicatorUnit: metricsData.length > 0 ? metricsData[0]._indicatorUnit || 'tCo2e' : 'tCo2e',
                fileName: (indidcatorlist.find(x => x.id === indifilter.indicator)?.title || 'Indicator') + '_Data.xlsx'
            };

            setExportProgress({ show: true, progress: 40, message: 'Sending request to server...' });

            // Call the API endpoint
            const response = await APIServices.post(API.IndicatorExcelConversion, exportData, {
                responseType: 'blob' // Important for file downloads
            });

            setExportProgress({ show: true, progress: 80, message: 'Processing server response...' });

            // Handle the blob response using the provided method
            setExportProgress({ show: true, progress: 95, message: 'Downloading file...' });

            const url = window.URL.createObjectURL(response.data);
            const a = document.createElement('a');
            a.href = url;
            a.download = exportData.fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            setExportProgress({ show: true, progress: 100, message: 'Export completed!' });

            // Show success message
            Swal.fire({
                title: 'Export Successful!',
                text: 'Your indicator report has been downloaded successfully.',
                icon: 'success',
                confirmButtonText: 'OK',
                timer: 3000
            });

            // Hide progress after a short delay
            setTimeout(() => {
                setExportProgress({ show: false, progress: 0, message: '' });
            }, 1000);

        } catch (error) {
            console.error('Export failed:', error);
            setExportProgress({ show: false, progress: 0, message: '' });

            // Show error message with SweetAlert
            Swal.fire({
                title: 'Export Failed',
                text: error.response?.data?.message || 'An error occurred while exporting the report. Please try again.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    }

    const updateDataByIndicatorFilter = async (obj, val) => {
        let loc = { ...indifilter, [obj]: val }
        const year = yearOption.find(x => x.name === loc.year)
        setLoad(true)
        const promise2 = await APIServices.post(API.GetAssuranceIndicatorNew_UP(admin.id), { userId: user.id, indicatorId: [loc.indicator], framework: loc.framework, year: { startMonth: year.startMonth, endMonth: year.endMonth } })

        // Get the data and pre-compute summary values
        const rawData = promise2?.data || [];

        // Get the indicator unit for consistent display
        const indicatorUnit = rawData.length > 0 ? rawData[0].indicatorUnit || 'tCo2e' : 'tCo2e';

        // Calculate enterprise summary (total of all computed values)
        const enterpriseTotal = rawData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

        // Process the data to include pre-computed summaries
        const processedData = rawData.map(item => {
            // Calculate entity summary for this location
            const locationData = rawData.filter(dataItem => dataItem.locationId === item.locationId);
            const entityTotal = locationData.reduce((sum, dataItem) => sum + (parseFloat(dataItem.computedValue) || 0), 0);

            return {
                ...item,
                // Pre-computed values to avoid recalculation on render
                _entityTotal: entityTotal,
                _enterpriseTotal: enterpriseTotal,
                _indicatorUnit: indicatorUnit
            };
        });

        setMetricsData(processedData);
        setIndiFilter(loc);
        setLoad(false);
    }

    // Custom filter templates
    const RowFilterTemplate = useCallback((options, obj) => {
        const filterOptions = Array.from(new Set(metricsData.map((i) => i[obj]).filter(x => x)));

        return (
            <MultiSelect
                value={options.value}
                options={filterOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
                showClear
                filter
            />
        );
    }, [metricsData]);

    const typeFilterTemplate = useCallback((options) => {
        const locationOptions = [
            { name: "Standalone", value: 1 },
            { name: "Derived", value: 2 }
        ];

        return (
            <MultiSelect
                value={options.value}
                options={locationOptions}
                optionValue='value'
                optionLabel='name'
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: '14rem' }}
                showClear
            />
        );
    }, []);

    return (
        <>
            <p>
                This screen displays indicators for the selected reporting year,
                where "Indicator" refers to computed value
                included in reports or dashboards. Use the filters to select the
                year, category, and indicator to view. Only indicators that have
                completed the approval process are listed here; unapproved data
                points are not visible. For the selected indicator(s), the table
                shows all contributing source data, reporting entities, and, where
                applicable, the associated emission factors. Clicking on a data
                point will open its specific submission screen, providing full
                workflow details, attached evidences, and additional information.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
                <Button
                    disabled={metricsData?.length === 0 || exportProgress.show}
                    onClick={() => {
                        exportIndicatorReport();
                    }}
                    label={exportProgress.show ? "Exporting..." : "Export Report"}
                    icon={exportProgress.show ? "pi pi-spin pi-spinner" : "pi pi-download"}
                    className="p-button-primary mr-3"
                />
            </div>

            <Box display="flex" gap={2} padding={2} alignItems="center">
                <FormControl sx={{ minWidth: 200 }}>
                    <label htmlFor="reporting-period-dropdown">
                        Reporting Year
                    </label>
                    <Dropdown
                        id="reporting-period-dropdown"
                        disabled={load}
                        value={indifilter.year}
                        options={[...yearOption]}
                        optionValue="name"
                        optionLabel="label"
                        onChange={(e) => updateDataByIndicatorFilter('year', e.value)}
                        placeholder="Select Reporting Year"
                    />
                </FormControl>
                <FormControl sx={{ minWidth: 200 }} >
                    <label htmlFor="category-dropdown">Framework</label>
                    <MultiSelect disabled={load} display="chip" style={{ width: 300 }} value={indifilter.framework} onChange={(e) => updateDataByIndicatorFilter('framework', e.value)} options={assFramework} optionLabel="title" optionValue="title"
                        filter={true} placeholder="Select" panelClassName={'hidefilter'} />
                </FormControl>

                <FormControl sx={{ minWidth: 200 }}>
                    <label htmlFor="datasource-dropdown">Select Indicator</label>
                    <Dropdown
                        id="indicator-types"
                        value={indifilter.indicator}
                        disabled={load}
                        filter
                        optionLabel="label"
                        optionValue="value"
                        options={indidcatorlist.map(x => ({ label: x.id + " : " + x.title, value: x.id }))}
                        onChange={(e) => updateDataByIndicatorFilter('indicator', e.value)}

                        placeholder="Select Type of Indicator"
                    />
                </FormControl>

            </Box>
            {RenderTable}
        </>
    )

}
