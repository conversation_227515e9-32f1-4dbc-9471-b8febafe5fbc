import React, { useState, useEffect } from 'react';
import { reliableStorage } from '../utils/reliableStorage';

const StorageDebugger = () => {
  const [storageStatus, setStorageStatus] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [storageSize, setStorageSize] = useState(0);

  useEffect(() => {
    const updateStatus = () => {
      try {
        const status = reliableStorage.getStatus();
        setStorageStatus(status);
        
        // Calculate localStorage size
        let size = 0;
        for (let key in localStorage) {
          if (localStorage.hasOwnProperty(key)) {
            size += localStorage[key].length + key.length;
          }
        }
        setStorageSize(size);
      } catch (error) {
        console.warn('Failed to get storage status:', error);
      }
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStorageWarningLevel = () => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const percentage = (storageSize / maxSize) * 100;
    
    if (percentage > 80) return 'danger';
    if (percentage > 60) return 'warning';
    return 'normal';
  };

  const handleClearStorage = async () => {
    if (window.confirm('Clear all localStorage data? This will reload the page.')) {
      try {
        await reliableStorage.clear();
        window.location.reload();
      } catch (error) {
        console.error('Failed to clear storage:', error);
      }
    }
  };

  if (!isVisible) {
    const warningLevel = getStorageWarningLevel();
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          padding: '5px 10px',
          backgroundColor: warningLevel === 'danger' ? '#dc3545' : 
                          warningLevel === 'warning' ? '#ffc107' : '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          zIndex: 9999,
          fontSize: '12px'
        }}
      >
        Storage: {formatBytes(storageSize)}
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      width: '300px',
      backgroundColor: 'white',
      border: '1px solid #ccc',
      borderRadius: '4px',
      padding: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 9999,
      fontSize: '12px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h4 style={{ margin: 0 }}>Storage Debug</h4>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      </div>

      <div>
        <strong>Storage Size:</strong> {formatBytes(storageSize)}
      </div>

      {storageStatus && (
        <div style={{ marginTop: '10px' }}>
          <div>
            <strong>Status:</strong>
          </div>
          <ul style={{ margin: '5px 0', paddingLeft: '20px', fontSize: '11px' }}>
            <li>localStorage Available: {storageStatus.isLocalStorageAvailable ? '✅' : '❌'}</li>
            <li>Using Memory Fallback: {storageStatus.usingMemoryFallback ? '⚠️ Yes' : '✅ No'}</li>
            <li>Memory Items: {storageStatus.memoryItemCount}</li>
            <li>localStorage Items: {storageStatus.localStorageItemCount}</li>
          </ul>
        </div>
      )}

      <div style={{ marginTop: '10px' }}>
        <button
          onClick={handleClearStorage}
          style={{
            padding: '5px 10px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
            marginRight: '5px',
            fontSize: '11px'
          }}
        >
          Clear Storage
        </button>
        
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '5px 10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px'
          }}
        >
          Reload
        </button>
      </div>
    </div>
  );
};

export default StorageDebugger;
