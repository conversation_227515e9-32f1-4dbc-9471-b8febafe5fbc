const ExecutiveSummary = ({locationData, selectedYear}) => {
  // Transform locationData to subsidiaries format
  const subsidiaries = locationData && locationData.length > 0
    ? locationData.flatMap(location =>
        location.locationTwos?.flatMap(locationTwo =>
          locationTwo.locationThrees?.map(locationThree => {
            // Extract name without parentheses content
            const fullName = locationThree.name;
            const nameWithoutParentheses = fullName.replace(/\s*\([^)]*\)\s*/g, '').trim();

            // Extract abbreviation from parentheses
            const abbreviationMatch = fullName.match(/\(([^)]+)\)$/);
            const abbreviation = abbreviationMatch ? abbreviationMatch[1] : '';

            return {
              locationId: locationThree.id,
              name: nameWithoutParentheses,
              abbreviation: abbreviation,
              location: locationTwo.name
            };
          }) || []
        ) || []
      ).map((item, index) => ({
        ...item,
        sn: index + 1
      }))
    : [];

  // Calculate dynamic year values
  const currentYear = selectedYear?.name || new Date().getFullYear();
  const previousYear = currentYear - 1;

  const footprintData = [
    {
      label: "Base Year",
      value: `FY ${currentYear} (1 January ${currentYear} to 31 December ${currentYear})`,
    },
    {
      label: "Approach to boundary identification",
      value: "Operation boundary",
    },
    { label: "Boundary of the footprint", value: "" },
    { label: "Gases Covered", value: "" },
    { label: "Standard Used", value: "The GHG Protocol Corporate Standard" },
  ];

  return (
    <div style={{ minHeight: "80vh", fontFamily: "Lato, sans-serif" }}>
      <div
        style={{
          maxWidth: "900px",
          margin: "auto",
        }}
      >
        <h3
          class="section-marker"
          id="section-executive-summary"
          style={{ fontWeight: "bold", marginBottom: "1rem" }}
        >
          1. EXECUTIVE SUMMARY
        </h3>

        <p class="section-marker" style={{ marginBottom: "1rem" }}>
          Rotary Group of Companies is calculating and reporting on its carbon
          footprint as per the GHG Protocol Corporate Accounting Reporting
          Standard within its operational boundaries, covering emissions from
          CO2, CH4, N2O and refrigerant gases. The carbon footprint calculations
          cover the operation of Rotary Group of Companies, as listed in the
          table below.
        </p>

        <p
          class="section-marker"
          style={{
            color: "blue",
            marginBottom: "1rem",
            textDecoration: "underline",
          }}
        >
          Table 1 1: Rotary Group of Companies
        </p>

 

        <table
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
            marginBottom: "1rem",
            border: "1px solid black",
          }}
        >
          <thead>
            <tr>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                S/N
              </th>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Business Unit / Subsidiary
              </th>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Abbreviation
              </th>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Location
              </th>
            </tr>
          </thead>
          <tbody>
            {subsidiaries.map((item) => (
              <tr key={item.sn}>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {item.sn}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {item.name}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {item.abbreviation}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {item.location}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <p class="section-marker" style={{ marginBottom: "1rem" }}>
          This Report covers the period of 1 January {currentYear} to 31 December {currentYear}.
          This Report for FY {currentYear} has been prepared with reference and in
          accordance with the Greenhouse Gas Protocol. The emitting activities
          covered in this Report include direct emissions resulting from Rotary
          Group of Companies owned or controlled equipment (Scope 1 emissions)
          and emissions from purchased electricity (Scope 2 emissions),
          respectively. Every effort has been made to ensure the accuracy of
          this Report.
        </p>

        <table
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
            marginBottom: "3rem",
            border: "1px solid black",
          }}
        >
          <tbody>
            {footprintData.map((row, idx) => (
              <tr key={idx}>
                <td
                  style={{
                    border: "1px solid black",
                    padding: "8px",
                    fontWeight: "bold",
                  }}
                >
                  {row.label}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {row.value || ""}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ExecutiveSummary;
