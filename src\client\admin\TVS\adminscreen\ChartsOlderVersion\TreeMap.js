import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

const dummyData = {
  name: "Scope 3",
  children: [
    { name: "Category 1", value: 50 },
    { name: "Category 11", value: 40 },
    { name: "Category 12", value: 60 },
    { name: "Category 13", value: 30 },
    { name: "Category 14", value: 80 },
  ],
};

const TreeMap = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    scope3: true,
  });
  const chartRef = useRef(null);

  useEffect(() => {
    renderTreemap();
  }, [visibleSeries]);

  const renderTreemap = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };

    // Clear the previous SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Create a treemap layout
    const treemap = d3
      .treemap()
      .size([
        width - margin.left - margin.right,
        height - margin.top - margin.bottom,
      ])
      .padding(1);

    // Hierarchical data structure for Scope 3
    const root = d3.hierarchy(dummyData).sum((d) => d.value);
    treemap(root);

    // Color scale for categories
    const colorScale = d3.scaleOrdinal(d3.schemeCategory10);

    // Create the treemap rectangles
    svg
      .selectAll("rect")
      .data(root.leaves())
      .enter()
      .append("rect")
      .attr("x", (d) => d.x0)
      .attr("y", (d) => d.y0)
      .attr("width", (d) => d.x1 - d.x0)
      .attr("height", (d) => d.y1 - d.y0)
      .attr("fill", (d, i) => colorScale(i))
      .attr("stroke", "#fff")
      .style("stroke-width", 1)
      .style("opacity", 0.7)
      .on("mouseover", function (event) {
        d3.select(this).style("opacity", 1).style("cursor", "pointer");
      })
      .on("mouseout", function () {
        d3.select(this).style("opacity", 0.7);
      });

    // Add labels to the treemap boxes
    svg
      .selectAll("text")
      .data(root.leaves())
      .enter()
      .append("text")
      .attr("x", (d) => (d.x0 + d.x1) / 2)
      .attr("y", (d) => (d.y0 + d.y1) / 2)
      .attr("dy", ".35em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#fff")
      .text((d) => `${d.data.name}: ${d.data.value}`);
  };

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "10px",
        }}
      >
        Scope 3 Emissions Treemap
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Checkbox for Scope 3 visibility */}
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <div style={{ display: "inline-block" }}>
          <Checkbox
            checked={visibleSeries["scope3"]}
            onChange={() => handleCheckboxChange("scope3")}
            style={{
              color: "#16a085",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 3</span>
        </div>
      </div>
    </div>
  );
};

export default TreeMap;
