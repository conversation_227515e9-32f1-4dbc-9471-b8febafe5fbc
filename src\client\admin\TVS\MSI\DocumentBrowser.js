import React, { useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { BreadCrumb } from 'primereact/breadcrumb';

import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Toast } from 'primereact/toast';
import { ProgressSpinner } from 'primereact/progressspinner';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import moment from 'moment';
import './DocumentBrowser.css';
import Axios from 'axios';

const DocumentBrowser = () => {
    const [documents, setDocuments] = useState([]);
    const [currentPath, setCurrentPath] = useState([]);
    const [currentItems, setCurrentItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [processing, setProcessing] = useState(false);
    const [breadcrumbItems, setBreadcrumbItems] = useState([]);
    const toast = React.useRef(null);

 

    useEffect(() => {
        loadDocuments();
    }, []);

    const loadDocuments = async () => {
        try {
            setLoading(true);

            // Uncomment when API is ready
            const response = await APIServices.get(API.S3_Bucket_SAP_List);
            setDocuments(response.data);
            processDocuments(response.data);
            setLoading(false);


        } catch (error) {
            console.error('Error loading documents:', error);
            toast.current?.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load documents'
            });
            setLoading(false);
        }
    };

    const processDocuments = (docs) => {
        const tree = {};

        docs.forEach(doc => {
            // Remove "Matrix/navigos/" prefix
            const cleanPath = doc.Key.replace(/^Matrix\/navigos\//, '');
            const pathParts = cleanPath.split('/');

            let current = tree;
            pathParts.forEach((part, index) => {
                if (!current[part]) {
                    current[part] = {
                        name: part,
                        type: index === pathParts.length - 1 ? 'file' : 'folder',
                        children: {},
                        lastModified: doc.LastModified,
                        fullKey: doc.Key,
                        path: pathParts.slice(0, index + 1).join('/')
                    };
                }
                current = current[part].children;
            });
        });

        setCurrentItems(Object.values(tree));
        updateBreadcrumb([]);
    };

    const navigateToFolder = (folderName) => {
        const newPath = [...currentPath, folderName];
        setCurrentPath(newPath);

        const tree = {};

        // Rebuild tree for current path
        documents.forEach(doc => {
            const cleanPath = doc.Key.replace(/^Matrix\/navigos\//, '');
            const pathParts = cleanPath.split('/');

            // Check if this document belongs to current path
            const matchesPath = newPath.every((pathPart, index) => pathParts[index] === pathPart);
            if (!matchesPath) return;

            // Build tree from current path onwards
            let treeCurrent = tree;
            pathParts.slice(newPath.length).forEach((part, index) => {
                if (!treeCurrent[part]) {
                    treeCurrent[part] = {
                        name: part,
                        type: index === pathParts.slice(newPath.length).length - 1 ? 'file' : 'folder',
                        children: {},
                        lastModified: doc.LastModified,
                        fullKey: doc.Key,
                        path: pathParts.slice(0, newPath.length + index + 1).join('/')
                    };
                }
                treeCurrent = treeCurrent[part].children;
            });
        });

        setCurrentItems(Object.values(tree));
        updateBreadcrumb(newPath);
    };

    const navigateBack = (targetIndex) => {
        const newPath = currentPath.slice(0, targetIndex);
        if (targetIndex === 0) {
            setCurrentPath([]);
            processDocuments(documents);
        } else {
            // Navigate to the specific path by rebuilding the tree for that path
            setCurrentPath(newPath);

            const tree = {};

            // Rebuild tree for the target path
            documents.forEach(doc => {
                const cleanPath = doc.Key.replace(/^Matrix\/navigos\//, '');
                const pathParts = cleanPath.split('/');

                // Check if this document belongs to target path
                const matchesPath = newPath.every((pathPart, index) => pathParts[index] === pathPart);
                if (!matchesPath) return;

                // Build tree from target path onwards
                let treeCurrent = tree;
                pathParts.slice(newPath.length).forEach((part, index) => {
                    if (!treeCurrent[part]) {
                        treeCurrent[part] = {
                            name: part,
                            type: index === pathParts.slice(newPath.length).length - 1 ? 'file' : 'folder',
                            children: {},
                            lastModified: doc.LastModified,
                            fullKey: doc.Key,
                            path: pathParts.slice(0, newPath.length + index + 1).join('/')
                        };
                    }
                    treeCurrent = treeCurrent[part].children;
                });
            });

            setCurrentItems(Object.values(tree));
            updateBreadcrumb(newPath);
        }
    };

    const updateBreadcrumb = (path) => {
        const items = [
            {
                label: 'Home',
                command: () => {
                    if (!processing) {
                        navigateBack(0);
                    }
                },
                disabled: processing
            }
        ];

        path.forEach((pathPart, index) => {
            items.push({
                label: pathPart,
                command: () => {
                    if (!processing) {
                        navigateBack(index + 1);
                    }
                },
                disabled: processing
            });
        });

        setBreadcrumbItems(items);
    };

    // Function to trigger file download
    const downloadFile = (url, filename) => {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename || 'download';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleFileClick = async (item) => {
        try {
            setProcessing(true);

            // Call API endpoint with POST method
            const payload = { key: item.fullKey };

            console.log('Processing document:', payload);

            const response = await Axios.post(API.S3Parquete2Excel, payload);
            console.log('API Response:', response.data);
 
            if (response.data && response.data.download_url) {
                // Extract filename from the original file
                const originalFilename = item.name.replace('.parquet', '.csv');

                // Trigger download
                downloadFile(response.data.download_url, originalFilename);

                toast.current?.show({
                    severity: 'success',
                    summary: 'Download Started',
                    detail: response.data.message || `Successfully processed: ${item.name}`,
                    life: 3000
                });
            } else {
                toast.current?.show({
                    severity: 'warn',
                    summary: 'No Download URL',
                    detail: 'File processed but no download link received.',
                    life: 3000
                });
            }

        } catch (error) {
            console.error('Error processing document:', error);
            toast.current?.show({
                severity: 'error',
                summary: 'Processing Failed',
                detail: error.response?.data?.message || 'Failed to process document. Please try again.',
                life: 5000
            });
        } finally {
            setProcessing(false);
        }
    };

    const nameTemplate = (rowData) => {
        const handleClick = () => {
            if (processing) return; // Don't allow clicks during processing

            if (rowData.type === 'folder') {
                navigateToFolder(rowData.name);
            } else {
                handleFileClick(rowData);
            }
        };

        const isDisabled = processing;
        const cursorStyle = isDisabled ? 'not-allowed' : 'pointer';
        const opacity = isDisabled ? 0.6 : 1;

        return (
            <div
                className="flex align-items-center"
                onClick={handleClick}
                style={{
                    cursor: cursorStyle,
                    opacity: opacity,
                    pointerEvents: isDisabled ? 'none' : 'auto'
                }}
            >
                <i className={`pi ${rowData.type === 'folder' ? 'pi-folder' : 'pi-file'} mr-2`}
                    style={{ color: rowData.type === 'folder' ? '#f39c12' : '#3498db' }}></i>
                <span>{rowData.name}</span>
                {processing && rowData.type === 'file' && (
                    <i className="pi pi-spin pi-spinner ml-2" style={{ color: '#007bff' }}></i>
                )}
            </div>
        );
    };



    const lastModifiedTemplate = (rowData) => {
        return moment(rowData.lastModified).format('DD-MMM-YYYY HH:mm');
    };

    if (loading && currentItems.length === 0) {
        return (
            <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
                <ProgressSpinner />
            </div>
        );
    }

    return (
        <div className="document-browser">
            <Toast ref={toast} />

            <Card
                title={
                    <div className="flex align-items-center">
                        <span>S3 Bucket Browser</span>
                        {processing && (
                            <div className="ml-3 flex align-items-center">
                                <i className="pi pi-spin pi-spinner mr-2" style={{ color: '#007bff' }}></i>
                                <span style={{ color: '#007bff', fontSize: '0.9rem' }}>Processing...</span>
                            </div>
                        )}
                    </div>
                }
                className="mb-4"
            >
                <div className="mb-3">
                    <BreadCrumb model={breadcrumbItems} />
                </div>

                <DataTable
                    value={currentItems}
                    scrollable
                    emptyMessage="No documents found"
                    loading={loading || processing}
                    style={{ width: '100%' }}
                >
                    <Column
                        field="name"
                        header="Name"
                        body={nameTemplate}
                        style={{ width: '70%' }}
                    />
                    <Column
                        field="lastModified"
                        header="Last Modified"
                        body={lastModifiedTemplate}
                        sortable
                        style={{ width: '30%' }}
                    />
                </DataTable>
            </Card>
        </div>
    );
};

export default DocumentBrowser;
