import { useEffect, useRef, useState } from "react";

import { But<PERSON> } from "primereact/button";
import { ScrollPanel } from "primereact/scrollpanel";
import { Dropdown } from "primereact/dropdown";


import { saveAs } from "file-saver";

import { principles } from "./components/principles";
import ExecutiveSummary from "./components/ExecutiveSummary";
import DefinitionsAbbreviations from "./components/DefinitionsAbbreviations";
import OrganizationProfile from "./components/OrganizationProfile";
import GhgInventoryDesignAndMethodology from "./components/GhgInventoryDesignAndMethodology";
import CalculatingGhgEmissions from "./components/CalculatingGhgEmissions";
import ReductionTargetsAndImprovementMeasures from "./components/ReductionTargetsAndImprovementMeasures";
import { API } from "../../../constants/api_url";
import APIServices from "../../../service/APIService";
import { getFiscalYearsFromStartDate } from "../../../components/BGHF/helper";
import { useSelector } from "react-redux";

function RotaryCFP() {
  const [loadingType, setLoadingType] = useState(null);
  const admin_data = useSelector((state) => state.user.admindetail);
  const [yearOption, setYearOption] = useState([]);
  const [indicatorData, setIndicatorData] = useState([]);
  const [selected, setSelected] = useState(principles[0]);
  const sectionRefs = useRef([]);
  const [rawsitelist, setRawSitelist] = useState([]);
  const { fymonth } = useSelector((state) => state.user.fyStartMonth);

  // Filter states
  const [selectedYear, setSelectedYear] = useState(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: Array.from({ length: 11 }, (_, i) => i * 0.1),
    };

    const observer = new IntersectionObserver((entries) => {
      const visible = entries.filter((e) => e.isIntersecting);
      if (visible.length) {
        const top = visible.sort(
          (a, b) => b.intersectionRatio - a.intersectionRatio
        )[0];
        const title = top.target.getAttribute("data-title");
        if (title) setSelected(title);
      }
    }, options);

    sectionRefs.current.forEach((ref) => ref && observer.observe(ref));

    return () => {
      sectionRefs.current.forEach((ref) => ref && observer.unobserve(ref));
    };
  }, []);
  useEffect(() => {
    let yrOptions = getFiscalYearsFromStartDate(
      admin_data.information.startdate, 1
    );

    setYearOption(yrOptions)
    // Set default selected year to the last year from options
    if (yrOptions.length > 0) {
      const lastYear = yrOptions[yrOptions.length - 1];
      setSelectedYear(lastYear);
    }
    setLocationData()
  }, [])

  // Effect to fetch indicator data when year changes
  useEffect(() => {
    if (selectedYear) {
      fetchIndicatorData(selectedYear);
    }
  }, [selectedYear]);

  const setLocationData = async () => {
    try {
      const locationData = await APIServices.post(API.FilteredClientLocation_UP(291), { userId: 291, roles: [] })
      setRawSitelist(locationData.data)
    } catch (error) {
      console.error('Error fetching location data:', error);
    }
  }

  const fetchIndicatorData = async (year) => {
    console.log(yearOption,year)
    if (!year) return;

    setIsLoadingData(true);
    try {
      const startMonth = year.startMonth
      const endMonth = year.endMonth

      const indicatorData = await APIServices.post(API.GetAssuranceIndicator_UP(291), {
        year: { startMonth, endMonth },
        indicatorId: [826, 827, 169]
      });
      setIndicatorData(indicatorData);
    } catch (error) {
      console.error('Error fetching indicator data:', error);
      setIndicatorData([]);
    } finally {
      setIsLoadingData(false);
    }
  }
  const generateTocList = (items) => {
    return `<ol>${items
      .map((item) => {
        const childrenList = item.children
          ? `<ul>${item.children
            .map(
              (child) =>
                `<li style="list-style: none; font-size: 0.9rem;">${child.title}</li>`
            )
            .join("")}</ul>`
          : "";
        return `<li><strong>${item.title}</strong>${childrenList}</li>`;
      })
      .join("")}</ol>`;
  };

  const exportWord = () => {
    setLoadingType("word");
    setTimeout(() => {
      try {
        const element = document.getElementById("report-content");
        const html = element?.outerHTML;
        const css = `
        <style>
          table { width:100%; border-collapse:collapse; }
          th, td { border:1px solid #333; padding:6px; }
        </style>`;
        const fullDoc = `<!DOCTYPE html><html><head><meta charset='utf-8'/>${css}</head><body>${html}</body></html>`;
        const blob = new Blob([fullDoc], {
          type: "application/msword;charset=utf-8",
        });
        saveAs(blob, "CFP_Report.doc");
      } catch (e) {
        console.error(e);
        alert("Word export failed");
      } finally {
        setLoadingType(null);
      }
    }, 100);
  };

  const exportPDF = async () => {
    setLoadingType("pdf");

    try {
      const content = document.getElementById("report-content");
      if (!content) throw new Error("Report content not found");

      const html = `
        <html>
          <head>
            <style>
              body { font-family: Arial; padding: 40px; }
              .page-break { page-break-before: always; }
              table { width: 100%; border-collapse: collapse; margin-top: 10px; }
              th, td { border: 1px solid #333; padding: 6px; text-align: left; }
              h2 { color: #2c3e50; }
            </style>
          </head>
          <body>
            ${content.innerHTML}
          </body>
        </html>
      `;

      const response = await fetch(
        API.DownloadSRReport('pdf'),
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ html }),
        }
      );

      if (!response.ok) throw new Error("PDF generation failed");

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = "CFP_Report.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error("PDF export failed", err);
      alert("PDF export failed");
    } finally {
      setLoadingType(null);
    }
  };

  return (
    <div className="brsr-report-container">
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          .brsr-report-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
          }

          .main-content.scroll-sections {
            position: relative;
            overflow-y: auto;
            height: 100vh;
            flex: 1;
          }

         

          .report-content-wrapper {
            padding: 5px;
            background: #fff;
          }

          .report-sidebar {
            width: 300px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            overflow: hidden;
            flex-shrink: 0;
          }
        `}
      </style>
      <div className="report-sidebar">
        <h2 className="report-sidebar-title">Rotary Group of Companies Index</h2>
        <ScrollPanel className="report-sidebar-scroll">
          <ul className="principles-list">
            {principles.map((item) => (
              <li key={item.title}>
                <div
                  className={`principle-item ${selected === item.title ? "selected" : ""
                    }`}
                  onClick={() => {
                    setSelected(item.title);
                    const idx = principles.indexOf(item.title);
                    if (idx !== -1) {
                      sectionRefs.current[idx]?.scrollIntoView({
                        behavior: "smooth",
                      });
                    }
                  }}
                >
                  {item.title}
                </div>
                {item.children && (
                  <ul style={{ marginLeft: "-2rem" }}>
                    {item.children.map((sub) => (
                      <li style={{ listStyle: "none" }} key={sub.title}>
                        <div
                          className="principle-item"
                          style={{ fontSize: "0.8rem" }}
                          onClick={() => {
                            const el = document.getElementById(sub.targetId);
                            if (el) el.scrollIntoView({ behavior: "smooth" });
                          }}
                        >
                          {sub.title}
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </ScrollPanel>
      </div>

      <div id="report-content" className="main-content scroll-sections" style={{
        flex: 1,
        overflow: "auto",
        height: "100vh",
        position: "relative"
      }}>
        <div  style={{
          padding: "1rem 2rem",
          backgroundColor: "#f8f9fa",
          borderBottom: "1px solid #dee2e6",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          marginTop: 0
        }}>
          <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
            <label style={{ fontWeight: "bold", fontSize: "14px", color: "#333" }}>Year:</label>
            <Dropdown
              value={selectedYear}
              options={yearOption}
              optionLabel="label"
              onChange={(e) => {
                console.log('Year changed to:', e.value);
                setSelectedYear(e.value);
              }}
              placeholder="Select Year"
              style={{
                minWidth: "180px",
                fontSize: "14px"
              }}
              panelStyle={{ zIndex: 1001 }}
            />
          </div>
          {isLoadingData ? (
            <>
                
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
              color: "#666",
              fontSize: "12px"
            }}>
              <div style={{
                width: "16px",
                height: "16px",
                border: "2px solid #f3f3f3",
                borderTop: "2px solid #3498db",
                borderRadius: "50%",
                animation: "spin 1s linear infinite"
              }}></div>
              Loading data...
            </div> </>
          ):
           <div >
          <Button
            label={loadingType === "word" ? "Exporting Word..." : "Export Word"}
            icon="pi pi-file-word"
            onClick={exportWord}
            loading={loadingType === "word"}
            severity="info"
          />
    
        </div> }
        </div>

        <div className="report-content-wrapper" style={{
          padding: 10,
          minHeight: "calc(100vh - 80px)"
        }}>
          <div id="executive-summary">
          <ExecutiveSummary
            locationData={rawsitelist}
            selectedYear={selectedYear}
          />
        </div>
        <div id="definitions-abbreviation">
          <DefinitionsAbbreviations
            locationData={rawsitelist}
            selectedYear={selectedYear}
          />
        </div>
        <div id="organization-profile">
          <OrganizationProfile
            locationData={rawsitelist}
            selectedYear={selectedYear}
          />
        </div>
        <div id="ghg-inventory-design-and-methodology">
          <GhgInventoryDesignAndMethodology
            locationData={rawsitelist}
            selectedYear={selectedYear}
          />
        </div>
        <div id="calculating-ghg-emissions">
          <CalculatingGhgEmissions
            locationData={rawsitelist}
            selectedYear={selectedYear}
            indicatorData={indicatorData}
            isLoadingData={isLoadingData}
          />
        </div>
        <div id="reduction-targets-and-improvement-measures">
          <ReductionTargetsAndImprovementMeasures
            locationData={rawsitelist}
            selectedYear={selectedYear}
          />
        </div>
        </div>

   
      </div>
    </div>
  );
}

export default RotaryCFP;
