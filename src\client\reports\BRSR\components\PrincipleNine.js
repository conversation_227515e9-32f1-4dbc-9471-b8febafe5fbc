import React from "react";

const PrincipleNine = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          PRINCIPLE 9- BUSINESSES SHOULD ENGAGE WITH AND PROVIDE VALUE TO THEIR
          CONSUMERS IN A RESPONSIBLE MANNER
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          1. Describe the mechanisms in place to receive and respond to consumer
          complaints and feedback.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Turnover of products and/services as a percentage of turnover from
          all products/service that carry information about:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Details</th>
              <th>As a percentage to total turnover</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Environmental and social parameters relevant to the product
              </td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Safe and responsible usage</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Recycling and/or safe disposal
              </td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Number of consumer complaints:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2}>Category</th>
              <th colSpan={2} style={{ textAlign: "center" }}>
                FY 2023-24
              </th>
              <th rowSpan={2}>Remarks</th>
              <th colSpan={2} style={{ textAlign: "center" }}>
                FY 2022-23
              </th>
              <th rowSpan={2}>Remarks</th>
            </tr>
            <tr>
              <th>Received during the year</th>
              <th>Pending resolution at end of year</th>
              <th>Received during the year</th>
              <th>Pending resolution at end of year</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Data privacy</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>Advertising</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>Cyber-security</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>Delivery of essential services</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>Restrictive Trade Practices</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>Unfair Trade Practices</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>Other-- Packaging, Quality, Transit and others</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{ fontWeight: "normal", color: "black", marginBottom: "1rem" }}
        >
          4. Details of instances of product recalls on accounts of safety
          issues
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "40%" }}>&nbsp;</th>
              <th>Number</th>
              <th>Reasons for recall</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>Voluntary recalls</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Forced recalls</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          5. Does the entity have a framework/ policy on cyber security and
          risks related to data privacy? 41 (Yes/No) If available, provide a
          web-link of the policy.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          6. Provide details of any corrective actions taken or underway on
          issues relating to advertising and delivery of essential services;
          cyber security and data privacy of customers; re- occurrence of
          instances of product recalls; penalty / action taken by regulatory
          authorities on safety of products / services.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          1. Channels / platforms where information on products and services of
          the Company can be accessed
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          2. Steps taken to inform and educate consumers, especially vulnerable
          and marginalised consumers, about safe and responsible usage of
          products and services.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          3. Mechanisms in place to inform consumers of any risk of disruption /
          discontinuation of essential services.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          4. Does the entity display product information on the product over and
          above what is mandated as per local laws? (Yes/No/Not Applicable) If
          yes, provide details in brief.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          5. Did your entity carry out any survey with regard to consumer
          satisfaction relating to the major products / services of the entity,
          significant locations of operation of the entity or the entity as a
          whole? (Yes/No)
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          6. Provide the following information relating to data breaches:
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          a. Number of instances of data breaches along-with impact: Percentage
          of data breaches involving personally identifiable information of
          customers):
        </p>
      </div>
    </div>
  );
};

export default PrincipleNine;
