import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

const dummyData = {
  name: "Total Emissions",
  children: [
    {
      name: "Scope 1",
      children: [
        { name: "Stationary", value: 30 },
        { name: "Mobile", value: 20 },
        { name: "Fugitive", value: 40 },
      ],
    },
    {
      name: "Scope 2",
      value: 60,
    },
    {
      name: "Scope 3",
      children: [
        { name: "Category 1", value: 50 },
        { name: "Category 11", value: 40 },
        { name: "Category 12", value: 60 },
      ],
    },
  ],
};

const OverviewWaterfallChart = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    scope1: true,
    scope2: true,
    scope3: true,
  });
  const chartRef = useRef(null);

  useEffect(() => {
    renderWaterfallChart();
  }, [visibleSeries]);

  const renderWaterfallChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 20, right: 30, bottom: 40, left: 40 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // Clear any existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const color = d3
      .scaleOrdinal()
      .domain(["Scope 1", "Scope 2", "Scope 3"])
      .range(["#e74c3c", "#f39c12", "#16a085"]); // A more vibrant color palette

    // Prepare data for the waterfall chart (flatten the nested structure)
    const data = [
      { name: "Scope 1", value: getValueForScope("scope1") },
      { name: "Scope 2", value: getValueForScope("scope2") },
      { name: "Scope 3", value: getValueForScope("scope3") },
    ];

    // Calculate the cumulative totals
    let cumulative = 0;
    data.forEach((d) => {
      d.cumulativeValue = cumulative;
      cumulative += d.value;
    });

    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.name))
      .range([0, chartWidth])
      .padding(0.3); // Reduce padding to make bars closer
    const y = d3
      .scaleLinear()
      .domain([0, d3.max(data, (d) => d.cumulativeValue)])
      .nice()
      .range([chartHeight, 0]);

    svg
      .selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("x", (d) => x(d.name))
      .attr("y", (d) => y(d.cumulativeValue + d.value)) // Stack the bars
      .attr("width", x.bandwidth())
      .attr("height", (d) => chartHeight - y(d.value))
      .attr("fill", (d) => color(d.name))
      .style("stroke", "#fff")
      .style("stroke-width", 1)
      .style("transition", "all 0.3s ease") // Smooth transition on hover
      .on("mouseover", (event, d) => {
        d3.select(event.currentTarget)
          .style("opacity", 0.7)
          .style("cursor", "pointer");
      })
      .on("mouseout", (event) => {
        d3.select(event.currentTarget).style("opacity", 1);
      });

    // Add axis labels
    svg
      .append("g")
      .selectAll(".x-axis")
      .data(data)
      .enter()
      .append("text")
      .attr("x", (d) => x(d.name) + x.bandwidth() / 2)
      .attr("y", chartHeight + 20)
      .style("text-anchor", "middle")
      .text((d) => d.name)
      .style("font-size", "14px")
      .style("fill", "#333");

    svg
      .append("g")
      .selectAll(".y-axis")
      .data([0, d3.max(data, (d) => d.cumulativeValue)])
      .enter()
      .append("text")
      .attr("x", -10)
      .attr("y", (d, i) => y(d) - i * 20) // Add some spacing for the axis labels
      .style("text-anchor", "middle")
      .text((d) => d)
      .style("font-size", "12px")
      .style("fill", "#333");

    // Add labels inside bars
    svg
      .selectAll(".label")
      .data(data)
      .enter()
      .append("text")
      .attr("x", (d) => x(d.name) + x.bandwidth() / 2)
      .attr(
        "y",
        (d) => y(d.cumulativeValue + d.value) + (chartHeight - y(d.value)) / 2
      )
      .attr("dy", ".35em")
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .style("fill", "#fff") // White text for contrast
      .text((d) => `${d.value}`);

    function getValueForScope(scope) {
      let total = 0;
      if (scope === "scope1" && visibleSeries.scope1) {
        dummyData.children[0].children.forEach((item) => {
          total += item.value;
        });
      }
      if (scope === "scope2" && visibleSeries.scope2) {
        total += dummyData.children[1].value;
      }
      if (scope === "scope3" && visibleSeries.scope3) {
        dummyData.children[2].children.forEach((item) => {
          total += item.value;
        });
      }
      return total;
    }
  };

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "10px",
        }}
      >
        Waterfall Chart for Emissions
        <div style={{ fontWeight: 200, fontSize: "14px" }}>
          View the incremental contributions of Scope 1, Scope 2, and Scope 3
          emissions.
        </div>
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Legends */}
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope1"]}
            onChange={() => handleCheckboxChange("scope1")}
            style={{
              color: "#e74c3c",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 1</span>
        </div>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope2"]}
            onChange={() => handleCheckboxChange("scope2")}
            style={{
              color: "#f39c12",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 2</span>
        </div>
        <div style={{ display: "inline-block" }}>
          <Checkbox
            checked={visibleSeries["scope3"]}
            onChange={() => handleCheckboxChange("scope3")}
            style={{
              color: "#16a085",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 3</span>
        </div>
      </div>
    </div>
  );
};

export default OverviewWaterfallChart;
