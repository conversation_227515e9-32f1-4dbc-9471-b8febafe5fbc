import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const data = [
  { department: "HR", internallyAudited: 50, externallyCertified: 30 },
  { department: "Finance", internallyAudited: 40, externallyCertified: 60 },
  { department: "IT", internallyAudited: 60, externallyCertified: 20 },
  { department: "Operations", internallyAudited: 70, externallyCertified: 50 },
];

const CoverageByAuditTypeChart = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderChart();
  }, []);

  const renderChart = () => {
    const width = 800;
    const height = 500;
    const margin = { top: 50, right: 30, bottom: 140, left: 80 }; // Increased bottom margin for X-axis label and legend

    // Clear any previous chart
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Stack data
    const keys = ["internallyAudited", "externallyCertified"];
    const stackedData = d3.stack().keys(keys)(data);

    // X Scale
    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.department))
      .range([0, chartWidth])
      .padding(0.4); // Reduced padding for larger bars

    // Y Scale
    const y = d3
      .scaleLinear()
      .domain([0, d3.max(stackedData[stackedData.length - 1], (d) => d[1])])
      .nice()
      .range([chartHeight, 0]);

    // Color Scale
    const color = d3.scaleOrdinal().domain(keys).range(["#7fc97f", "#fdc086"]); // Subtle colors for audit types

    // Draw Axes
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(x).tickSize(0))
      .selectAll("text")
      .style("text-anchor", "middle");

    g.append("g").call(d3.axisLeft(y));

    // Add X-Axis Label
    g.append("text")
      .attr("x", chartWidth / 2)
      .attr("y", chartHeight + 40)
      .style("text-anchor", "middle")
      .text("Department or Location")
      .style("font-size", "14px");

    // Add Y-Axis Label
    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -chartHeight / 2)
      .attr("y", -60)
      .style("text-anchor", "middle")
      .text("Number of Employees/Workers")
      .style("font-size", "14px");

    // Add Bars
    g.selectAll("g.stack")
      .data(stackedData)
      .enter()
      .append("g")
      .attr("fill", (d) => color(d.key))
      .selectAll("rect")
      .data((d) => d)
      .enter()
      .append("rect")
      .attr("x", (d) => x(d.data.department))
      .attr("y", (d) => y(d[1]))
      .attr("height", (d) => y(d[0]) - y(d[1]))
      .attr("width", 60) // Fixed bar width of 60
      .append("title") // Tooltip
      .text((d) => `${d.key}: ${d[1] - d[0]}`);

    // Add Legend with adjusted position further down
    const legend = svg
      .append("g")
      .attr(
        "transform",
        `translate(${width / 2 - 80},${height - margin.bottom + 50})`
      ); // Adjusted position of legend further down

    keys.forEach((key, index) => {
      legend
        .append("rect")
        .attr("x", index * 120)
        .attr("y", 0)
        .attr("width", 15)
        .attr("height", 15)
        .attr("fill", color(key));

      legend
        .append("text")
        .attr("x", index * 120 + 20)
        .attr("y", 12)
        .style("font-size", "12px")
        .text(key);
    });
  };

  return (
    <>
      <h3 style={{ fontSize: "18px" }}>Coverage by Audit Type</h3>

      <div
        ref={chartRef}
        style={{ display: "flex", justifyContent: "center" }}
      />
    </>
  );
};

export default CoverageByAuditTypeChart;
