import React, { createContext, useContext, useCallback, useEffect } from 'react';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { AppState, defaultAppState, SelectedState, SubSectionCuration } from '@/types/app-state';
import { Framework, Section, SubSection } from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';

const STORAGE_KEY = 'eco-data-navigator-app-state';
const AUTO_SAVE_DELAY = 1000; // 1 second delay for auto-save

interface AppStateContextType {
  // State
  appState: AppState;

  // Framework operations
  addFramework: (name: string) => void;
  editFramework: (id: string, newName: string) => void;
  deleteFramework: (id: string) => void;

  // Section operations
  addSection: (name: string, frameworkId: string) => void;
  editSection: (id: string, newName: string) => void;
  deleteSection: (id: string) => void;

  // SubSection operations
  addSubSection: (name: string, sectionId: string) => void;
  editSubSection: (id: string, newName: string) => void;
  deleteSubSection: (id: string) => void;

  // Selection operations
  setSelected: (selected: SelectedState) => void;

  // Header operations
  editHeaderTitle: (newTitle: string) => void;
  
  // Curation operations
  saveCuration: (subSectionId: string, subSectionName: string, components: any[]) => void;
  getCuration: (subSectionId: string) => SubSectionCuration | null;
  
  // Storage operations
  saveState: () => void;
  loadState: (newState: AppState) => void;
  clearState: () => void;
  exportState: () => void;
  importState: () => Promise<void>;
}

const AppStateContext = createContext<AppStateContextType | null>(null);

export function useAppState() {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
}

interface AppStateProviderProps {
  children: React.ReactNode;
}

export function AppStateProvider({ children }: AppStateProviderProps) {
  const [appState, setAppState, removeAppState] = useLocalStorage(STORAGE_KEY, defaultAppState);
  const { toast } = useToast();

  // Auto-save functionality with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Auto-save is handled by the useLocalStorage hook
      console.log('Auto-saved application state');
    }, AUTO_SAVE_DELAY);

    return () => clearTimeout(timeoutId);
  }, [appState]);

  // Framework operations
  const addFramework = useCallback((name: string) => {
    const newFramework: Framework = {
      id: `fw${Date.now()}`,
      name,
      frameworkId: ''
    };
    
    setAppState(prev => ({
      ...prev,
      frameworks: [...prev.frameworks, newFramework],
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Framework added successfully' });
  }, [setAppState, toast]);

  const editFramework = useCallback((id: string, newName: string) => {
    setAppState(prev => ({
      ...prev,
      frameworks: prev.frameworks.map(f => f.id === id ? { ...f, name: newName } : f),
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Framework updated successfully' });
  }, [setAppState, toast]);

  const deleteFramework = useCallback((id: string) => {
    setAppState(prev => {
      const relatedSections = prev.sections.filter(s => s.frameworkId === id);
      const relatedSubSections = prev.subSections.filter(sub => 
        relatedSections.some(section => section.id === sub.sectionId)
      );
      
      // Remove curations for deleted sub-sections
      const newCurations = { ...prev.curations };
      relatedSubSections.forEach(sub => {
        delete newCurations[sub.id];
      });
      
      return {
        ...prev,
        frameworks: prev.frameworks.filter(f => f.id !== id),
        sections: prev.sections.filter(s => s.frameworkId !== id),
        subSections: prev.subSections.filter(sub => 
          !relatedSections.some(section => section.id === sub.sectionId)
        ),
        curations: newCurations,
        selected: prev.selected.framework === id 
          ? { framework: null, section: null, subSection: null }
          : prev.selected,
        lastSaved: new Date().toISOString()
      };
    });
    
    toast({ title: 'Framework deleted successfully' });
  }, [setAppState, toast]);

  // Section operations
  const addSection = useCallback((name: string, frameworkId: string) => {
    const newSection: Section = {
      id: `sec${Date.now()}`,
      name,
      frameworkId
    };
    
    setAppState(prev => ({
      ...prev,
      sections: [...prev.sections, newSection],
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Section added successfully' });
  }, [setAppState, toast]);

  const editSection = useCallback((id: string, newName: string) => {
    setAppState(prev => ({
      ...prev,
      sections: prev.sections.map(s => s.id === id ? { ...s, name: newName } : s),
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Section updated successfully' });
  }, [setAppState, toast]);

  const deleteSection = useCallback((id: string) => {
    setAppState(prev => {
      const relatedSubSections = prev.subSections.filter(sub => sub.sectionId === id);
      
      // Remove curations for deleted sub-sections
      const newCurations = { ...prev.curations };
      relatedSubSections.forEach(sub => {
        delete newCurations[sub.id];
      });
      
      return {
        ...prev,
        sections: prev.sections.filter(s => s.id !== id),
        subSections: prev.subSections.filter(sub => sub.sectionId !== id),
        curations: newCurations,
        selected: prev.selected.section === id 
          ? { ...prev.selected, section: null, subSection: null }
          : prev.selected,
        lastSaved: new Date().toISOString()
      };
    });
    
    toast({ title: 'Section deleted successfully' });
  }, [setAppState, toast]);

  // SubSection operations
  const addSubSection = useCallback((name: string, sectionId: string) => {
    const newSubSection: SubSection = {
      id: `sub${Date.now()}`,
      name,
      sectionId
    };
    
    setAppState(prev => ({
      ...prev,
      subSections: [...prev.subSections, newSubSection],
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Sub-section added successfully' });
  }, [setAppState, toast]);

  const editSubSection = useCallback((id: string, newName: string) => {
    setAppState(prev => ({
      ...prev,
      subSections: prev.subSections.map(s => s.id === id ? { ...s, name: newName } : s),
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Sub-section updated successfully' });
  }, [setAppState, toast]);

  const deleteSubSection = useCallback((id: string) => {
    setAppState(prev => {
      const newCurations = { ...prev.curations };
      delete newCurations[id];
      
      return {
        ...prev,
        subSections: prev.subSections.filter(s => s.id !== id),
        curations: newCurations,
        selected: prev.selected.subSection === id 
          ? { ...prev.selected, subSection: null }
          : prev.selected,
        lastSaved: new Date().toISOString()
      };
    });
    
    toast({ title: 'Sub-section deleted successfully' });
  }, [setAppState, toast]);

  // Selection operations
  const setSelected = useCallback((selected: SelectedState) => {
    setAppState(prev => ({
      ...prev,
      selected,
      lastSaved: new Date().toISOString()
    }));
  }, [setAppState]);

  // Header operations
  const editHeaderTitle = useCallback((newTitle: string) => {
    setAppState(prev => ({
      ...prev,
      headerTitle: newTitle,
      lastSaved: new Date().toISOString()
    }));

    toast({ title: 'Header title updated successfully' });
  }, [setAppState, toast]);

  // Curation operations
  const saveCuration = useCallback((subSectionId: string, subSectionName: string, components: any[]) => {
    const curation: SubSectionCuration = {
      subSectionId,
      subSectionName,
      components,
      lastModified: new Date().toISOString()
    };
    
    setAppState(prev => ({
      ...prev,
      curations: {
        ...prev.curations,
        [subSectionId]: curation
      },
      lastSaved: new Date().toISOString()
    }));
    
    toast({ title: 'Curation saved successfully' });
  }, [setAppState, toast]);

  const getCuration = useCallback((subSectionId: string): SubSectionCuration | null => {
    return appState.curations[subSectionId] || null;
  }, [appState.curations]);

  // Storage operations
  const saveState = useCallback(() => {
    setAppState(prev => ({
      ...prev,
      lastSaved: new Date().toISOString()
    }));
    toast({ title: 'Application state saved successfully' });
  }, [setAppState, toast]);

  const loadState = useCallback((newState: AppState) => {
    setAppState({
      ...newState,
      lastSaved: new Date().toISOString()
    });
    toast({ title: 'Application state loaded successfully' });
  }, [setAppState, toast]);

  const clearState = useCallback(() => {
    removeAppState();
    toast({ title: 'Application state cleared successfully' });
  }, [removeAppState, toast]);

  const exportState = useCallback(() => {
    try {
      const exportData = {
        ...appState,
        exportedAt: new Date().toISOString(),
        exportedBy: 'Eco Data Navigator',
        appName: 'Environmental Framework Assessment'
      };
      
      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `eco-data-navigator-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({ title: 'Data exported successfully' });
    } catch (error) {
      toast({ title: 'Export failed', description: 'Please try again', variant: 'destructive' });
    }
  }, [appState, toast]);

  const importState = useCallback(async () => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      
      const file = await new Promise<File>((resolve, reject) => {
        input.onchange = (event) => {
          const file = (event.target as HTMLInputElement).files?.[0];
          if (file) resolve(file);
          else reject(new Error('No file selected'));
        };
        input.click();
      });
      
      const text = await file.text();
      const importedData = JSON.parse(text);
      
      // Basic validation
      if (!importedData.frameworks || !importedData.sections || !importedData.subSections) {
        throw new Error('Invalid file format');
      }
      
      loadState(importedData);
    } catch (error) {
      toast({ 
        title: 'Import failed', 
        description: 'Please check the file format and try again', 
        variant: 'destructive' 
      });
    }
  }, [loadState, toast]);

  const contextValue: AppStateContextType = {
    appState,
    addFramework,
    editFramework,
    deleteFramework,
    addSection,
    editSection,
    deleteSection,
    addSubSection,
    editSubSection,
    deleteSubSection,
    setSelected,
    editHeaderTitle,
    saveCuration,
    getCuration,
    saveState,
    loadState,
    clearState,
    exportState,
    importState,
  };

  return (
    <AppStateContext.Provider value={contextValue}>
      {children}
    </AppStateContext.Provider>
  );
}
