import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  <PERSON>ltip,
  Legend,
  LineChart,
  Line,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { Menu } from "primereact/menu";
import { Button } from "primereact/button";
import moment from "moment";

const MSIScoresOverTimeTab = ({ tabIndex, data }) => {
  const [chartData, setChartData] = useState([]);
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);
  const tableRef = useRef(null);
  const chartRef = useRef(null);

  // Process data to create chart data when data changes
  useEffect(() => {
    if (!data || !data.length) {
      setChartData([]);
      return;
    }

    // Create a map to store scores by month
    const monthlyData = {};

    // Process calibration data for APS and Area Office
    data.forEach(item => {
      if (item.date_of_calibration && item.total_score) {
        const month = moment(item.date_of_calibration).format('MMM YY');
        if (!monthlyData[month]) {
          monthlyData[month] = {
            month,
            calibrationScores: [],
            // For APS and Area Office, we don't have separate self-assessment data
            // so we'll use the same calibration scores for both lines
            selfAssessmentScores: [],
            entityCount: new Set() // Track unique entities (APS/Area Office)
          };
        }

        // Add entity to count (using area_office or branch_code as identifier)
        const entityId = item.area_office || item.branch_code || item.city;
        if (entityId) {
          monthlyData[month].entityCount.add(entityId);
        }

        monthlyData[month].calibrationScores.push(item.total_score);
        // For APS and Area Office, use calibration score as self-assessment score
        monthlyData[month].selfAssessmentScores.push(item.total_score);
      }
    });

    // Convert to chart format and calculate averages
    const chartDataArray = Object.values(monthlyData)
      .map(monthData => ({
        month: monthData.month,
        selfAssessment: monthData.selfAssessmentScores.length > 0
          ? Math.round(monthData.selfAssessmentScores.reduce((sum, score) => sum + score, 0) / monthData.selfAssessmentScores.length)
          : null,
        calibration: monthData.calibrationScores.length > 0
          ? Math.round(monthData.calibrationScores.reduce((sum, score) => sum + score, 0) / monthData.calibrationScores.length)
          : null,
        entityCount: monthData.entityCount.size
      }))
      .filter(item => item.selfAssessment !== null || item.calibration !== null)
      .sort((a, b) => moment(a.month, 'MMM YY').valueOf() - moment(b.month, 'MMM YY').valueOf());

    setChartData(chartDataArray);
  }, [data]);

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const entityType = tabIndex === 1 ? 'APS' : 'Area Offices';
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: '0 0 5px 0', fontWeight: 'bold' }}>{`Month: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ margin: '2px 0', color: entry.color }}>
              {`${entry.name}: ${entry.value !== null ? entry.value : 'N/A'}`}
            </p>
          ))}
          {payload[0] && payload[0].payload && payload[0].payload.entityCount && (
            <p style={{ margin: '2px 0', color: '#666', fontSize: '12px' }}>
              {`${entityType}: ${payload[0].payload.entityCount}`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            // downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            // printChart(chartRef);
          },
        },
      ],
    },
  ];

  // Use the processed chart data

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h3 style={{ fontSize: "18px", margin: "25px" }}>
          MSI Scores Over Time
        </h3>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19" />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" textAnchor="end" interval={0} />
          <YAxis />
          <Tooltip content={<CustomTooltip />} />
          <Legend content={CustomLegend} />
          <Line
            type="monotone"
            dataKey="selfAssessment"
            stroke="#0000cc"
            name="MSI Self Assessment Score"
            connectNulls={false}
          />
          <Line
            type="monotone"
            dataKey="calibration"
            stroke="#006600"
            name="MSI Calibration Score"
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default MSIScoresOverTimeTab;
