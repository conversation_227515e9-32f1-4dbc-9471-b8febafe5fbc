// SuppliersHeatmap.js
import React from "react";
import { useSelector } from "react-redux";

const EnvironmentDashboard = () => {
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const tvsGD = useSelector(state => state.user.tvs)

    const crypto = require("crypto-js");
    const SECRET_KEY = "e!sq6esgdash1";
    function encryptNumber(number, secret) {
        return crypto.AES.encrypt(number, SECRET_KEY).toString();  // Return IV and encrypted text
    }
    return (
        <div className="w-full h-screen p-4">

            <div className="w-full h-full rounded-2xl shadow-lg overflow-hidden border">
                <iframe
                    src={`https://esg-enterprise-admin.web.app/?${encryptNumber(JSON.stringify({ userId: login_data.id, adminId: admin_data.id }))}`}
                    title="Environment & Energy to Environment Dashboard"
                    className="w-full h-full"
                    frameBorder="0"
                    allowFullScreen
                ></iframe>
            </div>
        </div>
    );
};

export default EnvironmentDashboard;
