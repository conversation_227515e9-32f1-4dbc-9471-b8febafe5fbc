import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LabelList,
  ResponsiveContainer,
} from "recharts";

const SubGraph3Demo = ({ supplyData,count=0 }) => {

  // console.log(supplyData[0]?.assessmentSubSection1s)
  const [chartData, setChartData] = useState([]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "#fff",
            border: "1px solid #ccc",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "14px",
            fontFamily: "Lato",
            lineHeight: "1.5",
          }}
        >
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map((entry) => (
            <p key={entry.name} style={{ margin: 0, color: "black" }}>{`${entry.name
              }:${ entry.value}`}</p>
          ))}
        </div>
      );
    }

    return null;
  };

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
          marginTop: "10px",
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,

              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}

      </ul>
    );
  };

useEffect(() => {
  const sectionList = supplyData?.[0]?.assessmentSubSection1s ?? [];

  if (!sectionList.length) {
    setChartData([]);            // keep the chart empty
    return;
  }

  const getAvg = (title) =>
    (
      sectionList
        .filter((x) => x.title === title)
        .reduce((sum, item) => sum + (item.totalScore ?? 0) / 3, 0) /
      1 /* or supplyData.length if multiple suppliers */
    ).toFixed(2);

  const avgWater = getAvg("Water");
  const avgWaste = getAvg("Waste");
  const avgEnergy = getAvg("Energy");
  const avgProductStewardship = getAvg("Product Stewardship");

  setChartData([
    {
      category: "Water",
      achievedScore: +avgWater,
      remainingScore: Math.max(0, 11.33 - (+avgWater)),
      maxScore: 11.33
    },
    {
      category: "Waste",
      achievedScore: +avgWaste,
      remainingScore: Math.max(0, 15.33 - (+avgWaste)),
      maxScore: 15.33
    },
    {
      category: "Energy",
      achievedScore: +avgEnergy,
      remainingScore: Math.max(0, 5.67 - (+avgEnergy)),
      maxScore: 5.67
    },
    {
      category: "Product Stewardship",
      achievedScore: +avgProductStewardship,
      remainingScore: Math.max(0, 4.67 - (+avgProductStewardship)),
      maxScore: 4.67,
    },
  ]);
}, [supplyData]);

  const wrapText = (text, width = 40) => {
    let words = text.split(" ");
    let lines = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + " " + word).length > width) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine += (currentLine ? " " : "") + word;
      }
    });

    lines.push(currentLine); // Push the remaining line
    return lines.map((line, index) => (
      <tspan key={index} x="0" dy={index === 0 ? 0 : 10}>
        {line}
      </tspan>
    ));
  };

  const CustomizedTick = ({ x, y, payload }) => {
    return (
      <g transform={`translate(${x},${y})`}>
        <text
          textAnchor="middle"
          fontSize={10}
          fill="#666"
          dominantBaseline="middle"
        >
          {wrapText(payload.value, 20)} {/* Wrap text with custom width */}
        </text>
      </g>
    );
  };

  return (
    <div className="container mt-4 pt-2" style={{background:'#DAF3EF'}}>
      <h5 className="mb-3 text-center text-dark">
        Environmental Section Performance
      </h5>

      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData} barSize={50} margin={{ top: 20, right: 10, left: 0, bottom: 5 }}>
          <XAxis
            dataKey="category"
            fontSize={12}
            tick={<CustomizedTick />} // Custom tick rendering
            interval={0} // Show all labels
          />
          <YAxis domain={[0, 10]} label={{ value: 'Score', angle: -90, position: 'insideLeft' }} />
          <Tooltip content={CustomTooltip} />
          <Legend content={CustomLegend} />
          <Bar dataKey="achievedScore" stackId="a" fill="#2C7C69" name="Achieved">
            <LabelList
              dataKey="achievedScore"
              position="center"
              style={{ fontSize: "12px", fill: "white" }}
            />
          </Bar>
          <Bar dataKey="maxScore" stackId="a" fill="#7FC8A9" name="Maximum">
          <LabelList
              dataKey="maxScore"
              position="top"
              style={{ fontSize: "12px", fill: "black" }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      {/* <div className="col-12 flex justify-content-center">
        <CriticalNonCompliances count={count} />

      </div> */}
    </div>
  );
};

export default SubGraph3Demo;
