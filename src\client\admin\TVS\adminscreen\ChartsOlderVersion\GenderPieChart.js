import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, LabelList } from "recharts";
import { useState } from "react";

// Data for the chart
const data = [
  { name: "Male", value: 45 },
  { name: "Female", value: 55 },
];

// Colors for each segment
const COLORS = ["#4F81BD", "#FF7F7F"]; // Adjust these to match the original colors

export const GenderPieChart = () => {
  const [activeIndex, setActiveIndex] = useState(-1);

  const onPieEnter = (_, index) => {
    setActiveIndex(index);
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          margin: "18px 10px 18px 10px",
          display: "flex",
          flexDirection: "column",
          marginLeft: "3.5rem",
        }}
      >
        Employee Distribution by Gender
      </div>
      <PieChart width={400} height={400}>
        <Pie
          activeIndex={activeIndex}
          data={data}
          dataKey="value"
          outerRadius={150}
          fill="green"
          onMouseEnter={onPieEnter}
          style={{ cursor: "pointer", outline: "none" }}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
          {/* Adding LabelList to show values inside the pie */}
          <LabelList
            dataKey="value"
            position="inside"
            style={{
              fill: "white",
              fontSize: "16px",
            }}
          />
        </Pie>
        <Legend
          layout="horizontal"
          verticalAlign="bottom"
          align="center"
          formatter={(value, entry) => (
            <span style={{ color: entry.color }}>{value}</span>
          )}
        />
        <Tooltip />
      </PieChart>
    </div>
  );
};
