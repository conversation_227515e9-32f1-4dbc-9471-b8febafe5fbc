import React, { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend, ResponsiveContainer } from "recharts";
import { <PERSON><PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { Menu } from "primereact/menu";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { ChartSortDropdown, createSortOptions, sortData } from "./components/ChartSortDropdown";

// const initialData = [
//   { name: "Forging Machining", value: 8.1, number: 1930.44, suppliers: 57 },
//   { name: "Casting Machining", value: 11.0, number: 2618.04, suppliers: 48 },
//   { name: "Pressing Fabrication", value: 18.0, number: 4290.68, suppliers: 47 },
//   { name: "Prop Mechanical", value: 19.8, number: 4728.91, suppliers: 108 },
//   { name: "Prop Electrical", value: 21.8, number: 5205.96, suppliers: 78 },
//   {
//     name: "Plastics Rubber Painting Stickers",
//     value: 10.2,
//     number: 2438.64,
//     suppliers: 88,
//   },
//   { name: "ev3w2w", value: 4.3, number: 1021.32, suppliers: 18 },
//   { name: "BMW", value: 1.9, number: 443, suppliers: 118 },
//   { name: "Accessories", value: 1.1, number: 272.1, suppliers: 137 },
//   { name: "IDM", value: 3.9, number: 920, suppliers: 819 },
// ];

const initialData = [
  { name: "Forging & Machining", value: 8.0, number: 2216.471, suppliers: 57 },
  { name: "Casting & Machining", value: 13.7, number: 3795.506, suppliers: 48 },
  { name: "Pressing & Fabrication", value: 11.6, number: 3210.776, suppliers: 47 },
  { name: "Prop Mechanical", value: 18.9, number: 5237.688, suppliers: 108 },
  { name: "Prop Electrical", value: 20.2, number: 5591.178, suppliers: 78 },
  { name: "Plastics & Rubber", value: 9.0, number: 2477.639, suppliers: 88 },
  { name: "EV", value: 11.6, number: 3215.751, suppliers: 18 },
  { name: "BMW", value: 1.9, number: 519, suppliers: 118 },
  { name: "Accessories", value: 1.3, number: 367, suppliers: 137 },
  { name: "IDM", value: 3.7, number: 1016, suppliers: 819 },
];



const COLORS = [
  "#5B8FF7",
  "#BDD1F7",
  "#F9DF7F",
  "#F5C37B",
  "#F4A460",
  "#FF9D7C",
  "#A0522D",
  "#CCCED5",
  "#A7FECD",
  "#29C76F",
  "#209F00",
];

const SecondPieDemo = () => {
  const [data, setData] = useState(initialData);
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);
  const tableRef = useRef(null);

  // Sorting state
  const [sortField, setSortField] = useState('none');
  const [sortOrder, setSortOrder] = useState(1); // 1 for ascending, -1 for descending

  // Memoize sorting options to prevent re-creation
  const sortOptions = useMemo(() => createSortOptions([
    { name: 'name', label: 'Category', type: 'string' },
    { name: 'value', label: 'Percentage', type: 'number' },
    { name: 'number', label: 'Amount (Cr. INR)', type: 'number' },
    { name: 'suppliers', label: 'Suppliers Count', type: 'number' },
  ]), []);

  // Handle sort change - memoized to prevent re-creation
  const handleSortChange = useCallback((e) => {
    const selectedSort = e.value;
    const [, direction] = selectedSort.split('_');

    setSortField(selectedSort);
    setSortOrder(direction === 'asc' ? 1 : -1);

    // Apply sorting to the data using the imported sortData function
    const sortedData = sortData(data, selectedSort);
    setData(sortedData);
  }, [data]);

  // Memoize total spend calculation
  const totalSpend = useMemo(() => {
    return initialData.reduce((sum, item) => sum + (item?.number || 0), 0).toFixed(2);
  }, []);

  // Memoize pie chart props
  const pieProps = useMemo(() => ({
    dataKey: "value",
    nameKey: "name",
    cx: "50%",
    cy: "50%",
    outerRadius: 150,
    fill: "#8884d8"
  }), []);

  // Apply sorting when sort field or order changes
  useEffect(() => {
    if (sortField !== 'none') {
      const sortedData = sortData(initialData, sortField);
      setData(sortedData);
    }
  }, [sortField, sortOrder]);

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            // downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            // printChart(chartRef);
          },
        },
      ],
    },
  ];
  const CustomLegend = ({ position }) => {
    const legendItems = position === "left" ? data.slice(0, 5) : data.slice(5);

    return (
      <ul
        style={{
          listStyleType: "none",
          padding: 0,
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
        }}
      >
        {legendItems.map((entry) => {
          const index = data.findIndex((item) => item.name === entry.name);
          return (
            <div
              key={entry.name}
              style={{
                display: "flex",
                flexDirection: "column",
                marginBottom: "25px",
              }}
            >
              <li>
                <span
                  style={{
                    width: "12px",
                    height: "12px",
                    backgroundColor: COLORS[index % COLORS.length],
                    display: "inline-block",
                    marginRight: "8px",
                    borderRadius: "50%",
                  }}
                ></span>
                <span style={{ color: "#555", fontSize: "14px" }}>
                  {entry.name}
                </span>
              </li>
              <div style={{ display: "flex" }}>
                <h4 style={{ fontSize: "20px", fontWeight: 600 }}>
                  {entry.number}
                </h4>
                <p
                  style={{
                    marginLeft: "10px",
                    color: "#828282",
                    fontSize: "16px",
                    fontWeight: 400,
                  }}
                >
                  {entry.value}%
                </p>
              </div>
            </div>
          );
        })}
      </ul>
    );
  };

  const renderCustomizedLabel = ({ suppliers, x, y }) => {
    return (
      <text
        x={x}
        y={y}
        fill="black"
        textAnchor="middle"
        dominantBaseline="10"
        fontSize={12}
        fontWeight={600}
      >
        {suppliers}
      </text>
    );
  };

  return (
    <Card>
      <div>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <h3 style={{ fontSize: "18px", margin: "25px" }}>
            Supplier Spend (2024-25) in Cr. INR by category
          </h3>
          <div
            style={{
              margin: "18px 10px 18px 10px",
              display: "flex",
            }}
          >
            {/* Sort Dropdown */}
            <ChartSortDropdown
              value={sortField}
              options={sortOptions}
              onChange={handleSortChange}
            />

            <div
              className="buttons"
              style={{
                background: "#F0F2F4",
                borderRadius: "3px",
                width: "4.5rem",
                marginLeft: "10px",
                height: "30px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Button
                style={{
                  background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                  padding: "6px",
                  color: "black",
                  border: "0px",
                  marginRight: "4px",
                }}
                onClick={() => {
                  setActiveMode(false);
                }}
              >
                <i className="pi pi-table fs-19" />
              </Button>
              <Button
                style={{
                  background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                  padding: "6px",
                  color: "black",
                  border: "0px",
                }}
                onClick={() => {
                  setActiveMode(true);
                }}
              >
                <i className="pi pi-chart-bar fs-19" />
              </Button>
            </div>
            <div ref={menuRef}>
              <Button
                style={{
                  color: "black",
                  height: "30px",
                  marginLeft: "3px",
                  background: "#F0F2F4",
                  border: "0px",
                  padding: "6px",
                  position: "relative",
                }}
                onClick={() => {
                  setDropdownOpen(!dropdownOpen);
                }}
              >
                <i className="pi pi-angle-down fs-19" />
              </Button>
              {dropdownOpen && (
                <Menu
                  model={panelItems}
                  style={{
                    position: "absolute",
                    right: 45,
                    zIndex: "1",
                    padding: 0,
                  }}
                ></Menu>
              )}
            </div>
          </div>
        </div>
        {activeMode ? (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div>
              <CustomLegend position="left" />
            </div>

            {/* Pie Chart */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                marginTop: "0px",
                marginBottom: "1px",
                marginRight: "25px",
                marginLeft: "25px",
              }}
            >
              <PieChart width={600} height={400}>
                <Pie
                  data={data}
                  {...pieProps}
                  label={renderCustomizedLabel}
                >
                  {data.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
              </PieChart>
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  marginLeft: "225px",
                  marginTop: "1px",
                }}
              >
                <span>Total Spend 2024 - 25</span>
                <h5
                  style={{
                    fontWeight: 700,
                    fontSize: "24px",
                    marginRight: "70px",
                  }}
                >
                  {totalSpend}
                  {/* cr INR */}
                </h5>
              </div>
            </div>

            <div>
              <CustomLegend position="right" />
            </div>
          </div>
        ) : (
          <div style={{ padding: "20px" }}>
            <div style={{ marginBottom: "20px" }}>
              <span style={{ fontWeight: "bold" }}>Total Spend 2024 - 25: </span>
              <span style={{ fontSize: "18px", fontWeight: "600" }}>
                {totalSpend} Cr. INR
              </span>
            </div>
            <DataTable value={data} paginator rows={10} ref={tableRef} sortMode="multiple">
              <Column field="name" header="Category" sortable />
              <Column
                field="value"
                header="Percentage (%)"
                sortable
                body={(rowData) => `${rowData.value}%`}
              />
              <Column
                field="number"
                header="Amount (Cr. INR)"
                sortable
                body={(rowData) => rowData.number.toFixed(2)}
              />
              <Column field="suppliers" header="Suppliers Count" sortable />
            </DataTable>
          </div>
        )}
      </div>
    </Card>
  );
};

export default SecondPieDemo;
