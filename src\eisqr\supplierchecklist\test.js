import { useState, useEffect } from 'react';
import { Plus, GripVertical, Trash2, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { EditCheckpointDialog } from './EditCheckpointDialog';
import { EditHeaderDialog } from './EditHeaderDialog';
import { Question } from '@/types';
import { useAppState } from '@/contexts/AppStateContext';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface Component {
  id: string;
  type: string;
  title: string;
  description: string;
  icon: string;
  content?: string;
  questions?: Question[];
}

interface CurateModalProps {
  isOpen: boolean;
  onClose: () => void;
  subSectionName: string;
  subSectionId: string;
}

const availableComponents: Component[] = [
  { id: '1', type: 'Header', title: 'Header', description: 'Main page header', icon: 'H1' },
  { id: '10', type: 'Checkpoint Group', title: 'Checkpoint Group', description: 'Group of checkpoints', icon: '📋' },
];

export function CurateModal({ isOpen, onClose, subSectionName, subSectionId }: CurateModalProps) {
  const { saveCuration, getCuration } = useAppState();
  const [selectedComponents, setSelectedComponents] = useState<Component[]>([]);
  const [editingComponent, setEditingComponent] = useState<Component | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isHeaderEditDialogOpen, setIsHeaderEditDialogOpen] = useState(false);

  // Load existing curation when modal opens
  useEffect(() => {
    if (isOpen && subSectionId) {
      const existingCuration = getCuration(subSectionId);
      if (existingCuration) {
        setSelectedComponents(existingCuration.components);
      } else {
        setSelectedComponents([]);
      }
    }
  }, [isOpen, subSectionId, getCuration]);

  const addComponent = (component: Component) => {
    const newComponent = {
      ...component,
      id: `${component.id}-${Date.now()}`,
      content: `No ${component.type.toLowerCase()} text added yet`
    };
    setSelectedComponents(prev => [...prev, newComponent]);
  };

  const removeComponent = (id: string) => {
    setSelectedComponents(prev => prev.filter(comp => comp.id !== id));
  };

  const handleEditComponent = (component: Component) => {
    if (component.type === 'Checkpoint Group') {
      setEditingComponent(component);
      setIsEditDialogOpen(true);
    } else if (component.type === 'Header') {
      setEditingComponent(component);
      setIsHeaderEditDialogOpen(true);
    }
  };

  const handleSaveComponent = (data: { title: string; questions: Question[] }) => {
    if (editingComponent) {
      setSelectedComponents(prev => prev.map(comp =>
        comp.id === editingComponent.id
          ? {
              ...comp,
              content: data.title,
              questions: data.questions,
              title: data.title
            }
          : comp
      ));
    }
    setEditingComponent(null);
  };

  const handleSaveHeaderComponent = (data: { title: string; description?: string }) => {
    if (editingComponent) {
      setSelectedComponents(prev => prev.map(comp =>
        comp.id === editingComponent.id
          ? {
              ...comp,
              content: data.title,
              title: data.title,
              description: data.description || comp.description
            }
          : comp
      ));
    }
    setEditingComponent(null);
  };

  const getResponseTypeDisplay = (type: string) => {
    switch (type) {
      case 'A': return 'Yes/No/NA';
      case 'B': return 'Yes/No';
      case 'C': return '% selector';
      case 'D': return 'Description';
      case 'E': return 'Attachment';
      default: return type;
    }
  };

  const getCheckpointGroupIndex = (componentId: string) => {
    const checkpointGroups = selectedComponents.filter(comp => comp.type === 'Checkpoint Group');
    return checkpointGroups.findIndex(comp => comp.id === componentId) + 1;
  };

  const handleSaveCuration = () => {
    saveCuration(subSectionId, subSectionName, selectedComponents);
  };

  const handleClose = () => {
    // Auto-save when closing
    saveCuration(subSectionId, subSectionName, selectedComponents);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl h-[90vh] flex flex-col p-0">
       {/* <DialogHeader>
          <DialogTitle>Curate Content: {subSectionName}</DialogTitle>
        </DialogHeader> */}

        <div className="flex flex-1 gap-4 overflow-hidden">
          {/* Left Sidebar - All Components - Fixed height, no scroll */}
          <div className="w-80 bg-white border-r border-gray-200 p-4 flex flex-col">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold text-gray-900">All Components</h2>
                <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {availableComponents.length} items
                </span>
              </div>
              <p className="text-sm text-gray-600">
                📌 Drag components to the canvas area
              </p>
            </div>

            <div className="space-y-2 flex-1">
              {availableComponents.map((component) => (
                <div
                  key={component.id}
                  className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => addComponent(component)}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center text-blue-600 font-medium text-sm">
                      {component.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 text-sm">{component.title}</h3>
                      <p className="text-xs text-gray-600 mt-1">{component.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Main Content Area - Scrollable workspace */}
          <div className="flex-1 p-6 flex flex-col overflow-hidden">
            <div className="max-w-4xl mx-auto flex-1 flex flex-col overflow-hidden">
              {/* Content Canvas */}
              <div className="bg-white rounded-lg border border-gray-200 flex-1 flex flex-col overflow-hidden">
                {selectedComponents.length === 0 ? (
                  <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
                    <Plus className="w-12 h-12 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No components added yet</h3>
                    <p className="text-sm">Click on components from the left panel to add them here</p>
                  </div>
                ) : (
                  <div className="p-6 space-y-4 overflow-y-auto flex-1">
                    {selectedComponents.map((component, index) => (
                      <div
                        key={component.id}
                        className="group border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors"
                      >
                        <div className="flex items-start gap-3">
                          <div className="flex items-center gap-2">
                            <GripVertical className="w-4 h-4 text-gray-400" />
                            <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center text-blue-600 text-xs">
                              {component.icon}
                            </div>
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-medium text-gray-900">{component.title}</span>
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                Content
                              </span>
                            </div>
                            
                            {/* Show configured questions for Checkpoint Group */}
                            {component.type === 'Checkpoint Group' && component.questions ? (
                              <div className="space-y-3">
                                <div className="text-sm text-gray-600 font-medium">{component.content}</div>
                                <div className="text-xs text-gray-500 mb-2">
                                  {component.questions.length} question(s) configured
                                </div>
                                
                                {component.questions.map((question: Question, qIndex: number) => (
                                  <div key={question.id} className="border border-gray-200 rounded p-3 bg-gray-50">
                                    <div className="flex items-center justify-between mb-2">
                                      <span className="text-sm font-medium text-gray-700">
                                        Question {question.questionNumber || 'No number'}: {question.text || 'Untitled Question'}
                                      </span>
                                      <div className="flex items-center gap-2">
                                        <span className="text-xs bg-gray-200 px-2 py-1 rounded">
                                          Type {question.type} - {getResponseTypeDisplay(question.type)}
                                        </span>
                                        {(question.type === 'A' || question.type === 'B') && (
                                          <span className="text-xs text-gray-600">
                                            Score: {question.numerator}/{question.denominator}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                    
                                    {/* Display appropriate answer options based on response type */}
                                    {question.type === 'A' && (
                                      <RadioGroup className="space-y-1">
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem value="yes" id={`${question.id}-yes`} />
                                          <Label htmlFor={`${question.id}-yes`} className="text-sm">Yes</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem value="no" id={`${question.id}-no`} />
                                          <Label htmlFor={`${question.id}-no`} className="text-sm">No</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem value="na" id={`${question.id}-na`} />
                                          <Label htmlFor={`${question.id}-na`} className="text-sm">Not Applicable</Label>
                                        </div>
                                      </RadioGroup>
                                    )}
                                    
                                    {question.type === 'B' && (
                                      <RadioGroup className="space-y-1">
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem value="yes" id={`${question.id}-yes`} />
                                          <Label htmlFor={`${question.id}-yes`} className="text-sm">Yes</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem value="no" id={`${question.id}-no`} />
                                          <Label htmlFor={`${question.id}-no`} className="text-sm">No</Label>
                                        </div>
                                      </RadioGroup>
                                    )}
                                    
                                    {question.type === 'C' && (
                                      <div className="text-sm text-gray-600">
                                        Percentage selector dropdown will appear here
                                      </div>
                                    )}
                                    
                                    {question.type === 'D' && (
                                      <div className="text-sm text-gray-600">
                                        Description text area will appear here
                                      </div>
                                    )}
                                    
                                    {question.type === 'E' && (
                                      <div className="text-sm text-gray-600">
                                        File attachment upload will appear here
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <p className="text-sm text-gray-600 italic">{component.content}</p>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleEditComponent(component)}
                            >
                              ✏️
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => removeComponent(component.id)}
                            >
                              🗑️
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {/* <button className="w-full py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-400 hover:text-blue-600 transition-colors">
                      + Add Another Component
                    </button> */}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Fixed Footer with Save Controls */}
        <div className="border-t border-gray-200 p-4 bg-white">
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-gray-600">
              {selectedComponents.length} component(s) configured
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleClose}
              >
                Close
              </Button>
              <Button
                onClick={handleSaveCuration}
                className="flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                Save Curation
              </Button>
            </div>
          </div>
        </div>

        {/* Edit Dialog */}
        <EditCheckpointDialog
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setEditingComponent(null);
          }}
          onSave={handleSaveComponent}
          initialData={editingComponent ? {
            title: editingComponent.content || '',
            description: '',
            questions: editingComponent.questions || [],
            required: false
          } : undefined}
        />

        {/* Header Edit Dialog */}
        <EditHeaderDialog
          isOpen={isHeaderEditDialogOpen}
          onClose={() => {
            setIsHeaderEditDialogOpen(false);
            setEditingComponent(null);
          }}
          onSave={handleSaveHeaderComponent}
          initialData={editingComponent ? {
            title: editingComponent.content || '',
            description: editingComponent.description || ''
          } : undefined}
        />
      </DialogContent>
    </Dialog>
  );
}