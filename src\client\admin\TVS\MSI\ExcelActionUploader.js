import React, { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import { FileUpload } from 'primereact/fileupload';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { ScrollPanel } from 'primereact/scrollpanel';
import { Divider } from 'primereact/divider';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';

const ExcelActionUploader = ({ dealerList, assessorList }) => {
  const [rawRows, setRawRows] = useState([]);
  const [jsonData, setJsonData] = useState([]);
  const [uploadStatus, setUploadStatus] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const toast = useRef(null);

  const isExcelDate = (value) => typeof value === 'number' && value > 25000 && value < 100000;

  const formatExcelDate = (serialDate) => {
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(excelEpoch.getTime() + (serialDate - 2) * 86400000);
    return `${String(date.getDate()).padStart(2, '0')}-${String(date.getMonth() + 1).padStart(2, '0')}-${date.getFullYear()}`;
  };

  const parseDDMMYYYY = (dateString) => {
    if (typeof dateString !== 'string') return null;
    const [dd, mm, yyyy] = dateString.split('-');
    const date = new Date(`${yyyy}-${mm}-${dd}`);
    return !isNaN(date) ? date : null;
  };

  const handleFileUpload = async (event) => {
    const file = event.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (evt) => {
      try {
        const bstr = evt.target.result;
        const wb = XLSX.read(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const rawData = XLSX.utils.sheet_to_json(ws, { header: 1, defval: '' });
        const headers = rawData[1];
        const dataRows = rawData.slice(2);

        const data = dataRows.map((row) => {
          const rowData = {};
          headers.forEach((header, index) => {
            if (!header) return;
            let value = row[index] || '';
            if (isExcelDate(value)) value = formatExcelDate(value);
            rowData[header] = value;
          });
          return rowData;
        });

        const lastKnown = {};
        const filledRows = data.map(row => {
          const filled = { ...row };
          for (const key in row) {
            if (row[key]) lastKnown[key] = row[key];
            else filled[key] = lastKnown[key] || '';
          }
          return filled;
        });

        setRawRows(filledRows);
        setJsonData([]);
        setUploadStatus('');

        toast.current.show({
          severity: 'success',
          summary: 'Excel Loaded',
          detail: `Loaded ${filledRows.length} rows. Ready to convert.`,
        });
      } catch (error) {
        console.error('Error reading Excel:', error);
        toast.current.show({ severity: 'error', summary: 'Read Error', detail: 'Could not process the Excel file.' });
      }
      event.options.clear();
    };
    reader.readAsBinaryString(file);
  };

  const convertRawToActionData = () => {
    try {
      if (!Array.isArray(rawRows) || rawRows.length === 0) {
        toast.current.show({ severity: 'warn', summary: 'No Data', detail: 'Please upload and preview Excel first.' });
        return;
      }

      if (!Array.isArray(dealerList)) {
        throw new Error("dealerList is not an array or is undefined");
      }

      console.log("Converting rows:", rawRows.slice(0, 3));
      console.log("Dealer list sample:", dealerList.slice(0, 3));

      const converted = rawRows
        .filter(row => row['MSI ID'] && row['Required Action'])
        .map((row, index) => {
          try {
            const dealerCode = String(row['Dealer Code'] || '').trim();
            const dealer = dealerList.find(d => d.vendorData?.code === dealerCode || d.customLabel === dealerCode);
            const dealerId = dealer?.vendorData?.id || dealer?.id || null;
            const checkPoint = String(row['Checkpoint'] || '');
            const msiId = String(row['MSI ID'] || '');
            const msiPointNo = String(row['MSI Point no.'] || '').trim();
            const datePart = msiId.includes('-') ? msiId.split('-').pop() : '';

            let dueDate = '';
            const rawDate = row['Target date to complete'];
            if (rawDate && rawDate !== 'As per Plan') {
              const parsed = parseDDMMYYYY(rawDate);
              if (parsed instanceof Date && !isNaN(parsed)) {
                dueDate = parsed.toISOString();
              } else {
                console.warn(`Invalid date in row ${index + 1}:`, rawDate);
              }
            }

            return {
              applicationId: 'DAA',
              maskId: `MSI-${dealerCode}-${datePart}-${msiPointNo}`,
              application: 'DealerAssessmentAssignment',
              actionType: 'Checklist Submission',
              actionToBeTaken: row['Required Action'] || '',
              applicationDetails: {
                personResponsible: row['Responsible Person'] || '',
                criteria: '',
                subCriteria: '',
              },
              uploads: [],
              dueDate,
              assignedToId: dealerId ? [dealerId] : [],
              status: 'Initiated',
              description: `${msiPointNo}. ${checkPoint}`,
              createdDate: new Date().toISOString(),
              created: new Date().toString(),
              updated: new Date().toString(),
              remarks: row['Observation Evidence'] || '',
              objectId: null,
              submittedBy: null,
              appId: 33,
              category:
                row['Sales / Service']?.toLowerCase() === 'sales'
                  ? '1'
                  : row['Sales / Service']?.toLowerCase() === 'service'
                    ? '2'
                    : '',
              vendorId: dealerId,
              trackId: crypto?.randomUUID?.() || Math.random().toString(36).substr(2, 9),
            };
          } catch (err) {
            console.error(`Conversion error in row ${index + 1}:`, err);
            return null;
          }
        })
        .filter(Boolean);

      setJsonData(converted);
      toast.current.show({
        severity: 'info',
        summary: 'Converted',
        detail: `Converted ${converted.length} records.`,
      });

      console.log("Converted data:", converted.slice(0, 3));
    } catch (err) {
      console.error('Conversion failed:', err);
      toast.current.show({ severity: 'error', summary: 'Conversion Error', detail: err.message });
    }
  };

  const uploadActionsToAPI = async () => {
    if (!jsonData.length) {
      toast.current.show({ severity: 'warn', summary: 'No Data', detail: 'Convert data to action format first.' });
      return;
    }

    setIsUploading(true);
    setUploadStatus('Uploading...');

    try {
      const response = await APIServices.post(API.UploadChecklistActions, jsonData);
      setUploadStatus(`Upload success: ${response.data.message || 'OK'}`);

      toast.current.show({
        severity: 'success',
        summary: 'Upload Complete',
        detail: `Uploaded ${jsonData.length} records.`,
      });

      setJsonData([]);
      setRawRows([]);
    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus(`Failed: ${error.response?.data?.message || error.message}`);
      toast.current.show({ severity: 'error', summary: 'Upload Failed', detail: error.message });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="p-6">
      <Toast ref={toast} />
      <Card>
        <div className="mb-4">
          <h2 className="text-xl font-bold mb-2">Upload MSI Checklist Excel</h2>
          <p className="text-gray-600">Upload an Excel file, convert to action format, then upload to server.</p>
        </div>

        <FileUpload
          name="file"
          customUpload
          uploadHandler={handleFileUpload}
          accept=".xlsx,.xls"
          mode="basic"
          auto
          chooseLabel="Choose Excel File"
          className="mb-3"
        />

        <Divider />

        <Panel header={`Raw Excel Data (${rawRows.length})`} toggleable>
          <ScrollPanel style={{ width: '100%', height: '300px' }}>
            {rawRows.length > 0 ? (
              <DataTable value={rawRows} scrollable scrollHeight="300px" size="small">
                {Object.keys(rawRows[0] || {}).map((key, i) => (
                  <Column key={i} field={key} header={key} style={{ minWidth: '150px' }} />
                ))}
              </DataTable>
            ) : (
              <div className="text-center text-gray-500 p-4">No data yet. Upload Excel first.</div>
            )}
          </ScrollPanel>

          {rawRows.length > 0 && (
            <div className="mt-3 flex justify-content-end">
              <Button
                label="Convert to Action Format"
                icon="pi pi-sync"
                className="p-button-info"
                onClick={convertRawToActionData}
              />
            </div>
          )}
        </Panel>

        {jsonData.length > 0 && (
          <Panel header={`Converted Action Data (${jsonData.length})`} toggleable className="mt-4">
            <ScrollPanel style={{ width: '100%', height: '300px' }}>
              <DataTable value={jsonData} scrollable scrollHeight="300px" size="small">
                {Object.keys(jsonData[0] || {}).map((key, i) => (
                  <Column
                    key={i}
                    field={key}
                    header={key}
                    style={{ minWidth: '150px' }}
                    body={(rowData) =>
                      typeof rowData[key] === 'object'
                        ? JSON.stringify(rowData[key])
                        : rowData[key]
                    }
                  />
                ))}
              </DataTable>
            </ScrollPanel>

            <div className="flex justify-content-end mt-3">
              <Button
                label="Upload to API"
                icon="pi pi-upload"
                onClick={uploadActionsToAPI}
                className="p-button-success"
                loading={isUploading}
                disabled={isUploading || jsonData.length === 0}
              />
            </div>
          </Panel>
        )}
      </Card>
    </div>
  );
};

export default ExcelActionUploader;
