
import { Tab<PERSON><PERSON><PERSON>, TabView } from 'primereact/tabview';
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux';
import { EnterpriseEFAssignment } from './EnterpriseEFAssignment';
import { Tag } from 'primereact/tag';
import { Login } from '@mui/icons-material';
import SupplierLCATable from '../../forms/ValueChainForm/SupplierLCATable';
import { EnterpriseRawData } from './EnterpriseRawData';
import { EnterpriseIndicator } from './EnterpriseIndicators';
import { NewEnterpriseIndicator } from './NewEnterpriseIndicator';
import { SupplierRawData } from './SupplierRawData';
import { EnterpriseRawDataBk } from './EnterpriseDataBk';


const OverallAssurance = () => {
    const admin_data = useSelector((state) => state.user.admindetail);
    const userList = useSelector((state) => state.userlist.userList);
    const [rawsitelist, setRawsitelist] = useState([]);
    const login_data = useSelector((state) => state.user.userdetail)
    const tvsGD = useSelector(state => state.user.tvs)


    return (
        <div className="col-12">
            <div className="col-12">
                <div
                    className="col-12 flex align-items-center"
                    style={{ padding: "0px 20px" }}
                >
                    <p className="text-big-one">

                        Welcome {login_data?.information?.empname} !
                    </p>
                    <Tag className="ml-3 p-tag-blue" style={{ width: "10rem" }}>

                        Enterprise Administrator
                    </Tag>
                </div>

                <div className="col-12" style={{ padding: "0px 20px" }}>
                    <label className="text-big-one text-navy flex fs-16">

                        Data Assurance
                    </label>
                    <label className="text-small-one text-navy flex fs-16">

                        Data integrity and transparency though a comprehensive audit trail
                        for internal assurance and external validation.
                    </label>
                </div>
            </div>

            <TabView>
                <TabPanel header="Enterprise Raw Data">
                    <EnterpriseRawData admin={admin_data} user={login_data} />
                </TabPanel>

                {admin_data.id === 289 && !tvsGD?.isUKUser && <TabPanel header="Supplier's Raw Data">
                    <SupplierRawData admin={admin_data} />
                </TabPanel>}
                {admin_data.id === 289 && !tvsGD?.isUKUser &&<TabPanel header="Dealer's Raw Data" disabled></TabPanel>}
                {admin_data.id === 289 && !tvsGD?.isUKUser && <TabPanel header="LCA Data">
                    <SupplierLCATable />
                </TabPanel>}
                <TabPanel header="Indicator">
                    <EnterpriseIndicator admin={admin_data} user={login_data} />
                </TabPanel>
                <TabPanel header="Indicator Clone">
                    <NewEnterpriseIndicator admin={admin_data} user={login_data} />
                </TabPanel>
               {!tvsGD?.isUKUser && <TabPanel header="Emission Factor(s)">
                    <EnterpriseEFAssignment admin={admin_data} />
                </TabPanel>}
            </TabView>

        </div>
    )


}

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(OverallAssurance, comparisonFn);