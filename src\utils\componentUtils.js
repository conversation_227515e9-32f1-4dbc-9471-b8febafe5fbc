/**
 * Utility functions for safely handling component operations
 */

/**
 * Safely calls a method on an object, with error handling
 * @param {Object} obj - The object to call the method on
 * @param {string} methodName - The name of the method to call
 * @param {Array} args - Arguments to pass to the method
 * @returns {any} - The result of the method call, or undefined if it fails
 */
export const safelyCallMethod = (obj, methodName, ...args) => {
  if (!obj) return undefined;
  
  try {
    // Check if the method exists and is a function
    if (typeof obj[methodName] === 'function') {
      return obj[methodName](...args);
    } else {
      console.warn(`Method ${methodName} is not a function on the provided object`);
      return undefined;
    }
  } catch (error) {
    console.warn(`Error calling method ${methodName}:`, error);
    return undefined;
  }
};

/**
 * Safely destroys a component instance
 * @param {Object} componentRef - Reference to the component
 * @returns {boolean} - Whether the operation was successful
 */
export const safelyDestroyComponent = (componentRef) => {
  if (!componentRef || !componentRef.current) return false;
  
  try {
    // Try to call destroy method if it exists
    if (typeof componentRef.current.destroy === 'function') {
      componentRef.current.destroy();
      return true;
    }
    return false;
  } catch (error) {
    console.warn('Error destroying component:', error);
    return false;
  }
};

/**
 * Safely hides a dialog component
 * @param {Function} setVisibleFn - State setter function for dialog visibility
 */
export const safelyHideDialog = (setVisibleFn) => {
  if (typeof setVisibleFn !== 'function') return;
  
  try {
    setVisibleFn(false);
  } catch (error) {
    console.warn('Error hiding dialog:', error);
  }
};
