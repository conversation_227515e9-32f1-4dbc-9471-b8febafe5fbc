import React, { useEffect, useState, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  LabelList,
  ResponsiveContainer,
} from "recharts";
import CriticalNonCompliances from "./NonComplianceComponent";
import NonComplianceDetailsDialog from "./NonComplianceDetailsDialog";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const SubGraph3Demo = ({ supplyData }) => {
  const [chartData, setChartData] = useState([]);
  const [nonComplianceCount, setNonComplianceCount] = useState(0);
  const [nonComplianceData, setNonComplianceData] = useState([]);
  const [dialogVisible, setDialogVisible] = useState(false);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: "#fff",
          border: "1px solid #ccc",
          borderRadius: "8px",
          padding: "10px",
          fontSize: "14px",
          fontFamily: "Lato",
          lineHeight: "1.5",
        }}>
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map((entry) => (
            <p key={entry.name} style={{ margin: 0, color: "black" }}>
              {`${entry.name}:${entry.name === "Maximum" ? 10 : entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }) => (
    <ul style={{
      display: "flex",
      listStyleType: "none",
      justifyContent: "center",
      padding: 0,
      marginTop: "10px",
    }}>
      {payload.map((entry, index) => (
        <li key={`item-${index}`} style={{ marginRight: "5px" }}>
          <span style={{
            backgroundColor: entry.color,
            marginRight: 4,
            fontSize: "20px",
            width: "10px",
            height: "10px",
            borderRadius: "50%",
            display: "inline-block",
            marginTop: "10px",
          }}></span>
          <span style={{ color: "#555", fontSize: "14px" }}>{entry.value}</span>
        </li>
      ))}
    </ul>
  );

  const CustomizedTick = ({ x, y, payload }) => (
    <g transform={`translate(${x},${y})`}>
      <text x={0} y={10} textAnchor="middle" fontSize={11} fill="#666">
        {payload.value}
      </text>
    </g>
  );

  useEffect(() => {
    const fetchNonComplianceCount = async () => {
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];
       
        // Group by vendorCode
        const groupedByVendor = allAssignments.reduce((acc, item) => {
          if (!item.vendorCode) return acc;
          if (!acc[item.vendorCode]) acc[item.vendorCode] = [];
          acc[item.vendorCode].push(item);
          return acc;
        }, {});


        let totalEnvironmentalNonCompliances = 0;
        const allEnvironmentalNonCompliances = [];

        Object.values(groupedByVendor).forEach(assignments => {
          // Sort to get the latest
          const latest = assignments.sort((a, b) =>
            new Date(b.created_on) - new Date(a.created_on)
          )[0];

          const responseStr = latest?.auditorAssignmentSubmission?.response;
          if (!responseStr) return;

          let sectionMap = [];

          try {
            const auditorResponse = JSON.parse(responseStr);
            sectionMap = auditorResponse.flatMap(section =>
              (section.assessmentSubSection1s || []).map(sub => ({
                ...sub,
                order: section.order,
                esg: section.order === 1 ? 1 : section.order >= 5 ? 3 : 2
              }))
            );
          } catch (e) {
            console.error("Invalid response JSON for auditorAssignmentSubmission", e);
            return;
          }

          const getESG = (subsection2Id) => {
            return sectionMap.find(s => s.id === subsection2Id)?.esg || null;
          };

          const actions = (latest.supplierActions || []).map(action => ({
            ...action,
            esg: getESG(action.assessmentSubSection2Id),
            vendorName: latest?.vendor?.supplierName || 'Unknown Supplier',
            vendorLocation: latest?.vendor?.supplierLocation || latest?.location || 'Unknown Location',
            msiId: latest?.msiId,
            auditStartDate: latest?.auditStartDate,
            auditEndDate: latest?.auditEndDate,
            
          }));

          const envNonConformances = actions.filter(
            a => a.categoryOfFinding === 3 && a.esg === 1
          );

          totalEnvironmentalNonCompliances += envNonConformances.length;
          allEnvironmentalNonCompliances.push(...envNonConformances);
        });

        setNonComplianceCount(totalEnvironmentalNonCompliances);
        setNonComplianceData(allEnvironmentalNonCompliances);
      } catch (error) {
        console.error("Error fetching environmental non-compliances:", error);
        setNonComplianceCount(0);
      }
    };

    fetchNonComplianceCount();
  }, [supplyData]);



  // Memoize chart data calculation to prevent unnecessary re-renders
  const memoizedChartData = useMemo(() => {
    if (supplyData.length === 0) return [];

    const totalSuppliers = supplyData.length;

    const totalWater = supplyData.reduce((sum, item) => sum + (parseFloat(item.water) || 0), 0);
    const totalWaste = supplyData.reduce((sum, item) => sum + (parseFloat(item.waste) || 0), 0);
    const totalProductStewardship = supplyData.reduce(
      (sum, item) => sum + (parseFloat(item.product_stewardship) || 0), 0);
    const totalEnergy = supplyData.reduce((sum, item) => sum + (parseFloat(item.energy) || 0), 0);

    const avgWater = (totalWater / totalSuppliers).toFixed(1);
    const avgWaste = (totalWaste / totalSuppliers).toFixed(1);
    const avgProductStewardship = (totalProductStewardship / totalSuppliers).toFixed(1);
    const avgEnergy = (totalEnergy / totalSuppliers).toFixed(1);

    return [
      { category: "Water", maxScore: 10 - avgWater, avgScore: avgWater },
      { category: "Waste", maxScore: 10 - avgWaste, avgScore: avgWaste },
      { category: "Energy", maxScore: 10 - avgEnergy, avgScore: avgEnergy },
      {
        category: "Product Stewardship",
        maxScore: 10 - avgProductStewardship,
        avgScore: avgProductStewardship,
      },
    ];
  }, [supplyData]);

  useEffect(() => {
    setChartData(memoizedChartData);
  }, [memoizedChartData]);

  return (
    <div className="container mt-4">
      <h5 className="mb-3 text-center text-dark">
        Environmental Section Performance
      </h5>

      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} barSize={50}>
          <XAxis dataKey="category" fontSize={12} tick={<CustomizedTick />} interval={0} />
          <YAxis domain={[0, 10]} />
          <Tooltip content={CustomTooltip} />
          <Legend content={CustomLegend} />
          <Bar dataKey="avgScore" stackId="a" fill="#2C7C69" name="Achieved">
            <LabelList
              dataKey="avgScore"
              position="insideBottom"
              style={{ fontSize: "12px", fill: "white" }}
            />
          </Bar>
          <Bar dataKey="maxScore" stackId="a" fill="#7FC8A9" name="Maximum">
            <LabelList
              position="top"
              content={({ x, y, width }) => (
                <text
                  x={x + width / 2}
                  y={y - 10}
                  textAnchor="middle"
                  fill="black"
                  fontSize="12px"
                >
                  10
                </text>
              )}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>

      <div className="col-12 flex justify-content-center">
        <CriticalNonCompliances
          count={nonComplianceCount}
          onClick={() => setDialogVisible(true)}
        />
      </div>

      {/* Non-Compliance Details Dialog */}
      <NonComplianceDetailsDialog
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        nonComplianceData={nonComplianceData}
        title="Environmental Non-Compliance Details"
        esgType="Environmental"
      />
    </div>
  );
};

export default SubGraph3Demo;
