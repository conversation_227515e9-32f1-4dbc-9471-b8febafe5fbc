import React from "react";
import { render } from "react-dom";
import Highcharts from "highcharts/highstock";
import HighchartsReact from "highcharts-react-official";
import TreemapModule from "highcharts/modules/treemap";

const CalculatingGhgEmissions = ({locationData, selectedYear, indicatorData, isLoadingData}) => {
  // Process indicator data to calculate emissions by scope
  const processEmissionData = () => {
    if (!indicatorData?.data || !Array.isArray(indicatorData.data) || !locationData || !Array.isArray(locationData)) {
      return {
        scope1Stationary: { co2: 0, ch4: 0, n2o: 0, total: 0 },
        scope1Mobile: { co2: 0, ch4: 0, n2o: 0, total: 0 },
        scope2Electricity: { co2: 0, ch4: 0, n2o: 0, total: 0 }
      };
    }

    // Create a map of locationId to business unit name for consistent filtering
    const locationMap = {};
    locationData.forEach(location => {
      location.locationTwos?.forEach(locationTwo => {
        locationTwo.locationThrees?.forEach(locationThree => {
          const fullName = locationThree.name;
          const nameWithoutParentheses = fullName.replace(/\s*\([^)]*\)\s*/g, '').trim();
          locationMap[locationThree.id] = nameWithoutParentheses;
        });
      });
    });

    const scope1Stationary = { co2: 0, ch4: 0, n2o: 0, total: 0 };
    const scope1Mobile = { co2: 0, ch4: 0, n2o: 0, total: 0 };
    const scope2Electricity = { co2: 0, ch4: 0, n2o: 0, total: 0 };

    indicatorData.data.forEach(item => {
      const co2Value = parseFloat(item.computedCo2Value) || 0;
      const ch4Value = parseFloat(item.computedCh4Value) || 0;
      const n2oValue = parseFloat(item.computedN2oValue) || 0;
      const totalValue = parseFloat(item.computedValue) || 0;

      // For Scope 1 (826, 827), only include items with valid business unit mapping
      // For Scope 2 (169), include all items
      const hasValidBusinessUnit = locationMap[item.locationId];

      if (item.indicatorId === 826 && hasValidBusinessUnit) { // Stationary Combustion
        scope1Stationary.co2 += co2Value;
        scope1Stationary.ch4 += ch4Value;
        scope1Stationary.n2o += n2oValue;
        scope1Stationary.total += totalValue;
      } else if (item.indicatorId === 827 && hasValidBusinessUnit) { // Mobile Combustion
        scope1Mobile.co2 += co2Value;
        scope1Mobile.ch4 += ch4Value;
        scope1Mobile.n2o += n2oValue;
        scope1Mobile.total += totalValue;
      } else if (item.indicatorId === 169) { // Scope 2 - Electricity (include all)
        scope2Electricity.co2 += co2Value;
        scope2Electricity.ch4 += ch4Value;
        scope2Electricity.n2o += n2oValue;
        scope2Electricity.total += totalValue;
      }
    });

    return { scope1Stationary, scope1Mobile, scope2Electricity };
  };

  const emissionData = processEmissionData();

  // Process business unit data for Scope 1 emissions chart
  const processBusinessUnitData = () => {
    if (!indicatorData?.data || !Array.isArray(indicatorData.data) || !locationData || !Array.isArray(locationData)) {
      return { categories: [], data: [] };
    }

    // Create a map of locationId to business unit name
    const locationMap = {};
    locationData.forEach(location => {
      location.locationTwos?.forEach(locationTwo => {
        locationTwo.locationThrees?.forEach(locationThree => {
          // Extract name without parentheses content
          const fullName = locationThree.name;
          const nameWithoutParentheses = fullName.replace(/\s*\([^)]*\)\s*/g, '').trim();
          locationMap[locationThree.id] = nameWithoutParentheses;
        });
      });
    });

    // Group Scope 1 emissions (826 + 827) by business unit
    const businessUnitEmissions = {};

    indicatorData.data.forEach(item => {
      // Only process Scope 1 indicators (826: Stationary, 827: Mobile)
      if (item.indicatorId === 826 || item.indicatorId === 827) {
        const locationId = item.locationId;
        const businessUnit = locationMap[locationId];
        const emissionValue = parseFloat(item.computedValue) || 0;

        if (businessUnit) {
          if (!businessUnitEmissions[businessUnit]) {
            businessUnitEmissions[businessUnit] = 0;
          }
          businessUnitEmissions[businessUnit] += emissionValue;
        }
      }
    });

    // Convert to arrays for chart
    const categories = Object.keys(businessUnitEmissions);
    const data = Object.values(businessUnitEmissions);

    return { categories, data };
  };

  const businessUnitChartData = processBusinessUnitData();

  // Process detailed business unit data for Table 6 2
  const processBusinessUnitTableData = () => {
    if (!indicatorData?.data || !Array.isArray(indicatorData.data) || !locationData || !Array.isArray(locationData)) {
      return { businessUnits: [], totals: { co2: 0, ch4: 0, n2o: 0, total: 0 } };
    }

    // Create a map of locationId to business unit info (name and abbreviation)
    const locationMap = {};
    locationData.forEach(location => {
      location.locationTwos?.forEach(locationTwo => {
        locationTwo.locationThrees?.forEach(locationThree => {
          const fullName = locationThree.name;
          const nameWithoutParentheses = fullName.replace(/\s*\([^)]*\)\s*/g, '').trim();

          // Extract abbreviation from parentheses, fallback to full name
          const abbreviationMatch = fullName.match(/\(([^)]+)\)$/);
          let abbreviation;
          if (abbreviationMatch) {
            abbreviation = abbreviationMatch[1];
          } else {
            // If no parentheses, use full name
            abbreviation = nameWithoutParentheses;
          }

          locationMap[locationThree.id] = {
            name: nameWithoutParentheses,
            abbreviation: abbreviation
          };
        });
      });
    });

    // Group Scope 1 emissions by business unit with detailed breakdown
    const businessUnitDetails = {};

    indicatorData.data.forEach(item => {
      // Only process Scope 1 indicators (826: Stationary, 827: Mobile)
      if (item.indicatorId === 826 || item.indicatorId === 827) {
        const locationId = item.locationId;
        const businessUnitInfo = locationMap[locationId];

        if (businessUnitInfo) {
          const co2Value = parseFloat(item.computedCo2Value) || 0;
          const ch4Value = parseFloat(item.computedCh4Value) || 0;
          const n2oValue = parseFloat(item.computedN2oValue) || 0;
          const totalValue = parseFloat(item.computedValue) || 0;

          const key = businessUnitInfo.abbreviation;

          if (!businessUnitDetails[key]) {
            businessUnitDetails[key] = {
              name: businessUnitInfo.name,
              abbreviation: businessUnitInfo.abbreviation,
              co2: 0,
              ch4: 0,
              n2o: 0,
              total: 0
            };
          }

          businessUnitDetails[key].co2 += co2Value;
          businessUnitDetails[key].ch4 += ch4Value;
          businessUnitDetails[key].n2o += n2oValue;
          businessUnitDetails[key].total += totalValue;
        }
      }
    });

    // Convert to array and sort by abbreviation
    const businessUnits = Object.values(businessUnitDetails).sort((a, b) =>
      a.abbreviation.localeCompare(b.abbreviation)
    );

    // Calculate totals
    const totals = businessUnits.reduce((acc, bu) => ({
      co2: acc.co2 + bu.co2,
      ch4: acc.ch4 + bu.ch4,
      n2o: acc.n2o + bu.n2o,
      total: acc.total + bu.total
    }), { co2: 0, ch4: 0, n2o: 0, total: 0 });

    return { businessUnits, totals };
  };

  const businessUnitTableData = processBusinessUnitTableData();

  // Process emission source data for Table 6 3
  const processEmissionSourceData = () => {
    const emissionSources = {};

    // Check if indicatorData and data exist
    if (!indicatorData?.data) {
      return emissionSources;
    }

    indicatorData.data.forEach(item => {
      // Only process Mobile Combustion (dcfId 311) - indicator 827
      if (item.indicatorId === 827 && item.dcfId === 311) {
        const title = item.title || '';

        // Filter by vehicle types using title - expanded to include more types
        const isPassengerCarsVans = title.includes('Passenger Cars, Vans');
        const isLightDutyTrucks = title.includes('Light Duty Trucks');
        const isForklift = title.includes('Forklift');
        const isMediumHeavyVehicles = title.includes('Medium and Heavy-Duty Vehicles');
        const isMobileCrane = title.includes('Mobile Crane');
        const isLorryCrane = title.includes('Lorry Crane');
        const isCrawlerCrane = title.includes('Crawler Crane');
        const isBoomLift = title.includes('Boom Lift');
        const isScissorLift = title.includes('Scissor Lift');
        const isExcavators = title.includes('Excavators');
        const isOtherVehicles = title.includes('Other');

        if (isPassengerCarsVans || isLightDutyTrucks || isForklift || isMediumHeavyVehicles ||
            isMobileCrane || isLorryCrane || isCrawlerCrane || isBoomLift ||
            isScissorLift || isExcavators || isOtherVehicles) {

          const totalValue = parseFloat(item.computedValue) || 0;

          // Determine the emission source category
          let sourceCategory;
          if (isPassengerCarsVans) {
            sourceCategory = 'Passenger Cars, Vans';
          } else if (isLightDutyTrucks) {
            sourceCategory = 'Light Duty Trucks';
          } else if (isForklift) {
            sourceCategory = 'Forklift';
          } else if (isMediumHeavyVehicles) {
            sourceCategory = 'Medium and Heavy-Duty Vehicles';
          } else if (isMobileCrane) {
            sourceCategory = 'Mobile Crane';
          } else if (isLorryCrane) {
            sourceCategory = 'Lorry Crane';
          } else if (isCrawlerCrane) {
            sourceCategory = 'Crawler Crane';
          } else if (isBoomLift) {
            sourceCategory = 'Boom Lift';
          } else if (isScissorLift) {
            sourceCategory = 'Scissor Lift';
          } else if (isExcavators) {
            sourceCategory = 'Excavators';
          } else if (isOtherVehicles) {
            sourceCategory = 'Other Vehicles';
          }

          if (sourceCategory) {
            if (!emissionSources[sourceCategory]) {
              emissionSources[sourceCategory] = 0;
            }
            emissionSources[sourceCategory] += totalValue;
          }
        }
      }
    });

    return emissionSources;
  };

  const emissionSourceData = processEmissionSourceData();

  // Process Scope 2 emissions by business unit for Table 6 4
  const processScope2BusinessUnitData = () => {
    const businessUnitDetails = {};

    // Check if indicatorData and data exist
    if (!indicatorData?.data || !Array.isArray(indicatorData.data) || !locationData || !Array.isArray(locationData)) {
      return { businessUnits: [], totals: { total: 0 } };
    }

    // Create a map of locationId to business unit info (name and abbreviation)
    const locationMap = {};
    locationData.forEach(location => {
      location.locationTwos?.forEach(locationTwo => {
        locationTwo.locationThrees?.forEach(locationThree => {
          const fullName = locationThree.name;
          const nameWithoutParentheses = fullName.replace(/\s*\([^)]*\)\s*/g, '').trim();

          // Extract abbreviation from parentheses, fallback to full name
          const abbreviationMatch = fullName.match(/\(([^)]+)\)$/);
          let abbreviation;
          if (abbreviationMatch) {
            abbreviation = abbreviationMatch[1];
          } else {
            // If no parentheses, use full name
            abbreviation = nameWithoutParentheses;
          }

          locationMap[locationThree.id] = {
            name: nameWithoutParentheses,
            abbreviation: abbreviation,
            location: location.name || 'Unknown' // Add location country/region
          };
        });
      });
    });

    indicatorData.data.forEach(item => {
      // Only process Scope 2 indicators (169: Purchased electricity)
      if (item.indicatorId === 169) {
        const locationId = item.locationId;
        const businessUnitInfo = locationMap[locationId];

        if (businessUnitInfo) {
          const totalValue = parseFloat(item.computedValue) || 0;

          const key = businessUnitInfo.abbreviation;

          if (!businessUnitDetails[key]) {
            businessUnitDetails[key] = {
              name: businessUnitInfo.name,
              abbreviation: businessUnitInfo.abbreviation,
              location: businessUnitInfo.location || 'Unknown',
              total: 0
            };
          }

          businessUnitDetails[key].total += totalValue;
        }
      }
    });

    // Convert to array and sort by total emissions (descending)
    const businessUnits = Object.values(businessUnitDetails).sort((a, b) => b.total - a.total);

    // Calculate totals
    const totals = businessUnits.reduce((acc, bu) => ({
      total: acc.total + bu.total
    }), { total: 0 });

    return { businessUnits, totals };
  };

  const scope2BusinessUnitData = processScope2BusinessUnitData();

  // Debug: Log the totals to verify they match
  React.useEffect(() => {
    if (indicatorData?.data) {
      const scope1TableTotal = emissionData.scope1Stationary.total + emissionData.scope1Mobile.total;
      const scope1ChartTotal = businessUnitChartData.data.reduce((sum, value) => sum + value, 0);

      console.log('=== EMISSION TOTALS DEBUG ===');
      console.log('Table Scope 1 Total:', scope1TableTotal.toFixed(3));
      console.log('Chart Business Units Total:', scope1ChartTotal.toFixed(3));
      console.log('Difference:', Math.abs(scope1TableTotal - scope1ChartTotal).toFixed(3));
      console.log('Business Unit Data:', businessUnitChartData);
      console.log('Emission Data:', emissionData);
      console.log('Business Unit Table Data:', businessUnitTableData);
      console.log('Total Business Units Found:', businessUnitTableData.businessUnits.length);

      // Debug: Check for missing business units
      const allLocationIds = [...new Set(indicatorData.data
        .filter(item => item.indicatorId === 826 || item.indicatorId === 827)
        .map(item => item.locationId))];
      console.log('All Location IDs with Scope 1 data:', allLocationIds);

      // Check which location IDs don't have business unit mappings
      const locationMap = {};
      locationData.forEach(location => {
        location.locationTwos?.forEach(locationTwo => {
          locationTwo.locationThrees?.forEach(locationThree => {
            locationMap[locationThree.id] = locationThree.name;
          });
        });
      });

      const missingMappings = allLocationIds.filter(id => !locationMap[id]);
      if (missingMappings.length > 0) {
        console.log('Location IDs without business unit mappings:', missingMappings);
      }
    }
  }, [indicatorData, emissionData, businessUnitChartData, businessUnitTableData, locationData]);

  const methodologyList = [
    {
      title: "Selection of GHG Calculation Approach",
    },
    {
      title: "Collection of Activity Data and Selection of Emission Factors",
    },
    {
      title: "Apply Calculation Tools",
    },
    {
      title: "Consolidation of GHG Emission Data",
    },
  ];

  const co2businessUnitsOptions = {
    chart: {
      type: "column",
      backgroundColor: "#ffffff",
    },
    title: {
      text: "SCOPE 1 EMISSIONS (TONNE CO2 EQ) BY BUSINESS UNIT",
      style: {
        fontWeight: "bold",
        textTransform: "uppercase",
        color: "#333333",
      },
    },
    xAxis: {
      categories: businessUnitChartData.categories,
      title: {
        text: "Business Unit",
      },
      crosshair: true,
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: "tonnes CO2 eq",
      },
      labels: {
        format: "{value:.2f}",
      },
    },
    tooltip: {
      headerFormat: "<span style='font-size:10px'>{point.key}</span><br/>",
      pointFormat:
        "<span style='color:{series.color}'>{series.name}</span>: <b>{point.y:.2f}</b><br/>",
      shared: true,
      useHTML: true,
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
      },
    },
    series: [
      {
        name: "Tonnes CO2 eq",
        data: businessUnitChartData.data,
        colorByPoint: true,
      },
    ],
  };

  // Create dynamic chart data from emission source data
  const createEmissionChartData = () => {
    const categories = [];
    const data = [];

    // Get all emission sources with data
    Object.entries(emissionSourceData).forEach(([category, value]) => {
      if (value > 0) {
        categories.push(category.toUpperCase());
        data.push(parseFloat(value));
      }
    });

    return { categories, data };
  };

  const chartData = createEmissionChartData();

  const emissionOptions = {
    chart: {
      type: "column",
      backgroundColor: "#ffffff",
      height: 700,
    },
    title: {
      text: "SCOPE 1 EMISSIONS (TONNES CO₂ EQ) BY EMISSION SOURCE",
      style: {
        fontWeight: "bold",
        textTransform: "uppercase",
        color: "#333333",
      },
    },
    xAxis: {
      categories: chartData.categories,
      labels: {
        rotation: -90,
        style: {
          fontSize: "11px",
          textAlign: "right",
        },
      },
    },
    yAxis: {
      visible: false,
    },
    tooltip: {
      enabled: true,
      headerFormat: "<b>{point.key}</b><br/>",
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.3f} tCO₂e</b><br/>',
    },
    legend: {
      enabled: false,
    },
    plotOptions: {
      column: {
        color: "#00aaff",
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true,
          rotation: -90,
          align: "right",
          verticalAlign: "bottom",
          inside: false,
          style: {
            fontSize: "8px",
            color: "#000000",
          },
          format: "{y:.3f}",
        },
      },
    },
    series: [
      {
        name: "Emissions",
        data: chartData.data,
      },
    ],
  };

  // Create dynamic treemap data from emission source data
  const createMobileEmissionsTreemapData = () => {
    const colors = [
      "#e76f51", "#264653", "#2a9d8f", "#264d27", "#6a994e",
      "#d62828", "#0077b6", "#9e6f21", "#6c757d", "#f4a261",
      "#e9c46a", "#457b9d", "#1d3557", "#a8dadc", "#f1faee"
    ];

    const data = [];
    let colorIndex = 0;

    // Get all emission sources with data
    Object.entries(emissionSourceData).forEach(([category, value]) => {
      if (value > 0) {
        data.push({
          name: category,
          value: parseFloat(value),
          color: colors[colorIndex % colors.length]
        });
        colorIndex++;
      }
    });

    return data;
  };

  const treemapData = createMobileEmissionsTreemapData();

  const mobileEmissionsOptions = {
    chart: {
      type: "treemap",
      height: 600,
      backgroundColor: "#ffffff",
    },
    title: {
      text: "Emissions By Type of Vehicles (Mobile Combustion)",
      style: {
        fontWeight: "bold",
      },
    },
    tooltip: {
      pointFormat: "<b>{point.name}</b>: {point.value:.3f} tonnes CO₂ eq",
    },
    legend: {
      enabled: true,
      layout: "horizontal",
      align: "center",
      verticalAlign: "top",
      itemStyle: {
        fontWeight: "normal",
      },
    },
    plotOptions: {
      treemap: {
        allowDrillToNode: false,
        layoutAlgorithm: "squarified",
        dataLabels: {
          enabled: true,
          format: "{point.name}<br/>{point.value:.3f}",
          style: {
            color: "#ffffff",
            textOutline: "none",
            fontSize: "10px",
          },
        },
      },
      series: {
        showInLegend: true,
      },
    },
    series: [
      {
        type: "treemap",
        name: "Emissions",
        colorByPoint: true,
        legendType: "point", // Show each point as a legend item
        data: treemapData,
      },
    ],
  };

  const stationeryCombustionOptions = {
    chart: {
      type: "treemap",
      height: 600,
      backgroundColor: "#ffffff",
    },
    title: {
      text: "Emissions By Type of Equipment (Stationary Combustion)",
      style: {
        fontWeight: "bold",
      },
    },
    tooltip: {
      pointFormat: "<b>{point.name}</b>: {point.value:.2f} tonnes CO₂ eq",
    },
    legend: {
      enabled: true,
      layout: "horizontal",
      align: "center",
      verticalAlign: "top",
      itemStyle: {
        fontSize: "12px",
        fontWeight: "normal",
      },
    },
    plotOptions: {
      series: {
        showInLegend: true,
      },
      treemap: {
        layoutAlgorithm: "squarified",
        dataLabels: {
          enabled: true,
          format: "{point.name}",
          style: {
            color: "#ffffff",
            textOutline: "none",
          },
        },
      },
    },
    series: [
      {
        type: "treemap",
        name: "Emissions",
        colorByPoint: true,
        legendType: "point",
        data: [
          { name: "Generator", value: 4315.77, color: "#4CAF50" },
          { name: "Air Compressor", value: 1377.69, color: "#0D47A1" },
          {
            name: "Mobile Lighting Equipment",
            value: 342.58,
            color: "#03A9F4",
          },
          { name: "Welding Generator Set", value: 197.96, color: "#263238" },
          { name: "Pumping Equipment", value: 81.18, color: "#EF6C00" },
          {
            name: "Other Construction Equipment",
            value: 54.73,
            color: "#2E7D32",
          },
          { name: "Cutting Equipment", value: 53.92, color: "#8D6E63" },
          { name: "Diesel Engine", value: 14.82, color: "#8E24AA" },
        ],
      },
    ],
  };

  // Create dynamic Scope 2 chart data
  const createScope2ChartData = () => {
    const categories = [];
    const data = [];

    scope2BusinessUnitData.businessUnits.forEach(bu => {
      categories.push(bu.abbreviation);
      data.push(parseFloat(bu.total));
    });

    return { categories, data };
  };

  const scope2ChartData = createScope2ChartData();

  const scope2Emissions = {
    chart: {
      type: "column",
      backgroundColor: "#ffffff",
      height: 400,
    },
    title: {
      text: "Scope 2 Emissions for 2024 (Tonne CO2 eq)",
      style: {
        fontWeight: "normal",
      },
    },
    xAxis: {
      categories: scope2ChartData.categories,
      crosshair: true,
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: null,
      },
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    tooltip: {
      headerFormat: "<b>{point.key}</b><br/>",
      pointFormat: "Emissions: <b>{point.y:.3f}</b> tonnes CO₂ eq",
    },
    legend: {
      enabled: false,
    },
    plotOptions: {
      column: {
        color: "#125c75",
        borderWidth: 0,
        pointPadding: 0.1,
        groupPadding: 0.2,
        dataLabels: {
          enabled: true,
          style: {
            fontSize: "10px",
            color: "#000000",
          },
          format: "{y:.3f}",
        },
      },
    },
    series: [
      {
        name: "Emissions",
        data: scope2ChartData.data,
      },
    ],
  };

  // Loading component
  const LoadingSpinner = () => (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '200px',
      flexDirection: 'column',
      gap: '1rem'
    }}>
      <div style={{
        border: '4px solid #f3f3f3',
        borderTop: '4px solid #3498db',
        borderRadius: '50%',
        width: '40px',
        height: '40px',
        animation: 'spin 1s linear infinite'
      }}></div>
      <p style={{ color: '#666', fontSize: '14px' }}>Loading emission data...</p>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );

  return (
    <div style={{ minHeight: "80vh", fontFamily: "Lato, sans-serif" }}>
      <div
        style={{
          maxWidth: "900px",
          margin: "auto",
        }}
      >
        <h3
          class="section-marker"
          id="section-calculating-ghg-emissions"
          style={{
            fontWeight: "bold",
            marginBottom: "1rem",
            marginTop: "3rem",
          }}
        >
          5 CALCULATING GHG EMISSIONS
        </h3>

        {isLoadingData ? (
          <LoadingSpinner />
        ) : (
          <React.Fragment>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          5.1 CALCULATON METHODOLOGY
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          With the definition of operational boundaries and identification of
          emission sources for both Scope 1 and Scope 2 emissions, the GHG
          emissions will be calculated with the following calculation
          methodology:
        </p>

        {methodologyList.list && (
          <ul>
            {methodologyList.list.map((item, i) => (
              <li key={i}>
                <strong>{i + 1}:</strong> {item.title}
              </li>
            ))}
          </ul>
        )}

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          The data obtained for the calculation of GHG emissions include
          purchase records for fuels, such as diesel, gasoline and liquified
          petroleum gases (emission source activity data). These fuels are used
          for various construction and project activities in each of the
          Business Unit. The usage of these fuels is further categorized into
          the equipment and vehicle type to improve the accuracy of the
          calculations for the GHG emissions. The calculation methodology is
          applied with the data obtained with the emission source activity data
          multiplied by the emission factors.
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          For Scope 2 emissions, purchase and utilities bills were used to
          obtain data for electricity consumption. The emissions are calculated
          by multiplying the relevant emission factors.
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          5.2 EMISSION CALCULATION RESULTS
        </p>
        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          5.2 EMISSION PROFILE SUMMARY
        </p>

        <p
          class="section-marker"
          style={{
            textDecoration: "underline",
            marginBottom: "1rem",
          }}
        >
          Table 6 1: Summary for Scope 1 and Scope 2 Emissions
        </p>

        <table
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
            marginBottom: "1rem",
            border: "1px solid black",
          }}
        >
          <thead>
            <tr>
              <th
                colSpan={5}
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Component Gas (expressed as tonne CO<sub>2</sub> eq)
              </th>
            </tr>
            <tr>
              <th style={{ border: "1px solid black", padding: "8px" }}>
                Emission Scope
              </th>
              <th style={{ border: "1px solid black", padding: "8px" }}>
                tCO<sub>2</sub>e
              </th>
              <th style={{ border: "1px solid black", padding: "8px" }}>
                CO<sub>2</sub>
              </th>
              <th style={{ border: "1px solid black", padding: "8px" }}>
                CH<sub>4</sub>
              </th>
              <th style={{ border: "1px solid black", padding: "8px" }}>
                N<sub>2</sub>O
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td
                colSpan={5}
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                Scope 1: Direct Emissions
              </td>
            </tr>
            <tr>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                Stationary Combustion (Fuel Used)
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Stationary.total.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Stationary.co2.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Stationary.ch4.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Stationary.n2o.toFixed(3)}
              </td>
            </tr>
            <tr>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                Mobile Combustion (Owned and Leased Vehicles)
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Mobile.total.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Mobile.co2.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Mobile.ch4.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope1Mobile.n2o.toFixed(3)}
              </td>
            </tr>
            <tr>
              <td
                colSpan={5}
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                Scope 2 – Indirect Emission
              </td>
            </tr>
            <tr>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                Emissions from purchased electricity (Grid Electricity)
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope2Electricity.total.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope2Electricity.co2.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope2Electricity.ch4.toFixed(3)}
              </td>
              <td style={{ border: "1px solid black", padding: "8px" }}>
                {emissionData.scope2Electricity.n2o.toFixed(3)}
              </td>
            </tr>
            <tr>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                Total Emissions
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  background: "#b1b1b1",
                }}
              >
                {(emissionData.scope1Stationary.total + emissionData.scope1Mobile.total + emissionData.scope2Electricity.total).toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  background: "#b1b1b1",
                }}
              >
                {(emissionData.scope1Stationary.co2 + emissionData.scope1Mobile.co2 + emissionData.scope2Electricity.co2).toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  background: "#b1b1b1",
                }}
              >
                {(emissionData.scope1Stationary.ch4 + emissionData.scope1Mobile.ch4 + emissionData.scope2Electricity.ch4).toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  background: "#b1b1b1",
                }}
              >
                {(emissionData.scope1Stationary.n2o + emissionData.scope1Mobile.n2o + emissionData.scope2Electricity.n2o).toFixed(3)}
              </td>
            </tr>
          </tbody>
        </table>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          The total GHG emissions for Group Level from Scope 1 and Scope 2
          emissions is 15,340.65 tonnes CO2 eq. In FY 2024, the overall Group
          Revenue is SGD $385 million, and the total GHG emissions per million
          is calculated to be 39.8 tonnes CO2 eq /million.
        </p>
        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          5.3 SCOPE 1 GHG EMISSIONS BY BUSINESS UNIT / SUBSIDIARY COMPANY
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          The table below shows the GHG emissions from various BUs / Subsidiary
          Companies. All values are expressed in CO2 eq in the respective units
          stated.
        </p>

        <div>
          <HighchartsReact
            highcharts={Highcharts}
            options={co2businessUnitsOptions}
          />
        </div>

  
        <p
          class="section-marker"
          style={{
            textAlign: "center",
            color: "blue",
          }}
        >
          Figure 2: Scope 1 Emission by Business Unit
        </p>
        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          Table 6 2: Scope 1 Emissions by Business Units
        </p>



        <table
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
            marginBottom: "1rem",
            border: "1px solid black",
          }}
        >
          <thead>
            <tr>
              {[
                "BU",
                "kg CO2",
                "Tonne CO2",
                "kg CH4",
                "tonne CH4",
                "kg N2O",
                "tonne N2O",
                "Total",
              ].map((header) => (
                <th
                  key={header}
                  style={{
                    border: "1px solid black",
                    padding: "8px",
                    fontWeight: "bold",
                  }}
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {businessUnitTableData.businessUnits && businessUnitTableData.businessUnits.length > 0 ? businessUnitTableData.businessUnits.map((bu, idx) => (
              <tr key={idx}>
                <td
                  style={{
                    border: "1px solid black",
                    padding: "8px",
                  }}
                >
                  {bu.abbreviation}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {(bu.co2 * 1000).toFixed(3)}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.co2.toFixed(3)}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {(bu.ch4 * 1000).toFixed(3)}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.ch4.toFixed(3)}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {(bu.n2o * 1000).toFixed(3)}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.n2o.toFixed(3)}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.total.toFixed(3)}
                </td>
              </tr>
            )) : (
              <tr>
                <td colSpan={8} style={{ border: "1px solid black", padding: "8px", textAlign: "center" }}>
                  No data available
                </td>
              </tr>
            )}
            {businessUnitTableData.totals && (
            <tr>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                Total
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {(businessUnitTableData.totals.co2 * 1000).toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {businessUnitTableData.totals.co2.toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {(businessUnitTableData.totals.ch4 * 1000).toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {businessUnitTableData.totals.ch4.toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {(businessUnitTableData.totals.n2o * 1000).toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {businessUnitTableData.totals.n2o.toFixed(3)}
              </td>
              <td
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                {businessUnitTableData.totals.total.toFixed(3)}
              </td>
            </tr>
            )}
          </tbody>
        </table>
        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          5.4 SCOPE 1 EMISSIONS BY EQUIPMENT / VEHICLE CATEGORY
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          Table 6 3: Scope 1 Emission by Emission Source
        </p>

   

        <table
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
            marginBottom: "1rem",
            border: "1px solid black",
          }}
        >
          <thead>
            <tr style={{ backgroundColor: "#d3d3d3" }}>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Category
              </th>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Total (tCO<sub>2</sub> eq)
              </th>
            </tr>
          </thead>
          <tbody>
            <tr style={{ backgroundColor: "#d3d3d3", fontWeight: "bold" }}>
              <td
                style={{ border: "1px solid black", padding: "8px" }}
                colSpan={2}
              >
                Mobile Combustion
              </td>
            </tr>

            {[
              "Passenger Cars, Vans",
              "Light Duty Trucks",
              "Forklift",
              "Medium and Heavy-Duty Vehicles",
              "Mobile Crane",
              "Lorry Crane",
              "Crawler Crane",
              "Boom Lift",
              "Scissor Lift",
              "Excavators",
              "Other Vehicles",
            ].map((item, idx) => {
              const value = emissionSourceData[item] || 0;
              // Only show rows that have data
              if (value > 0) {
                return (
                  <tr key={idx}>
                    <td style={{ border: "1px solid black", padding: "8px" }}>
                      {item}
                    </td>
                    <td style={{ border: "1px solid black", padding: "8px" }}>
                      {value.toFixed(3)}
                    </td>
                  </tr>
                );
              }
              return null;
            }).filter(Boolean)}


          </tbody>
        </table>

        <div>
          <HighchartsReact highcharts={Highcharts} options={emissionOptions} />
        </div>

 

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
            textAlign: "center",
          }}
        >
          Figure 3: Scope 1 Emission by Emission Source
        </p>

        <div>
          <HighchartsReact
            highcharts={Highcharts}
            options={mobileEmissionsOptions}
          />
        </div>

  

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
            textAlign: "center",
          }}
        >
          Figure 4: Emissions by Type of Vehicles (Mobile Combustion) –
          Graph/Block Diagram
        </p>

        {/* <div>
          <HighchartsReact
            highcharts={Highcharts}
            options={stationeryCombustionOptions}
          />
        </div>



        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
            textAlign: "center",
          }}
        >
          Figure 5: Emissions by Type of Equipment (Stationary Combustion)
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          The main source of emissions for Scope 1 emissions are the from the
          combustion of diesel for equipment (e.g. Generator sets and air
          compressors) for construction power and power for site facilities in
          ongoing projects. Other major source of Scope 1 emissions is from the
          usage of fuel for transportation purposes (light duty to heavy duty
          vehicles) for the transportation of manpower and materials.
        </p> */}
        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          5.5 SCOPE 2 EMISSIONS
        </p>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          The main sources for Scope 2 Emissions are the emissions associated
          with the purchase of electricity for use in buildings and offices. The
          following table details the Scope 2 emissions associated with the
          respective Business Unit and locations.
        </p>



        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
          }}
        >
          Table 6 4: Scope 2 Emissions by Business Unit
        </p>

        <table
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
            marginBottom: "1rem",
            border: "1px solid black",
          }}
        >
          <thead>
            <tr>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Business Unit
              </th>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                Location
              </th>
              <th
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                }}
              >
                tonne CO<sub>2</sub> eq
              </th>
            </tr>
          </thead>
          <tbody>
            {scope2BusinessUnitData.businessUnits && scope2BusinessUnitData.businessUnits.length > 0 ? scope2BusinessUnitData.businessUnits.map((bu, idx) => (
              <tr key={idx}>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.abbreviation}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.location}
                </td>
                <td style={{ border: "1px solid black", padding: "8px" }}>
                  {bu.total.toFixed(3)}
                </td>
              </tr>
            )) : (
              <tr>
                <td colSpan={3} style={{ border: "1px solid black", padding: "8px", textAlign: "center" }}>
                  No Scope 2 data available
                </td>
              </tr>
            )}
            {scope2BusinessUnitData.totals && (
            <tr>
              <td
                colSpan={2}
                style={{
                  border: "1px solid black",
                  padding: "8px",
                  fontWeight: "bold",
                  background: "#b1b1b1",
                }}
              >
                Total
              </td>
              <td style={{
                border: "1px solid black",
                padding: "8px",
                fontWeight: "bold",
                background: "#b1b1b1",
              }}>
                {scope2BusinessUnitData.totals.total.toFixed(3)}
              </td>
            </tr>
            )}
          </tbody>
        </table>

        <div>
          <HighchartsReact highcharts={Highcharts} options={scope2Emissions} />
        </div>

        <p
          class="section-marker"
          style={{
            marginBottom: "1rem",
            textAlign: "center",
            color: "red",
          }}
        >
          Figure 6: Scope 2 Emissions by Business Unit
        </p>
          </React.Fragment>
        )}
      </div>
    </div>
  );
};

export default CalculatingGhgEmissions;
