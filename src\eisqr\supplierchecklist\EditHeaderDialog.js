import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface EditHeaderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: { title: string; description?: string }) => void;
  initialData?: {
    title: string;
    description?: string;
  };
}

export function EditHeaderDialog({ 
  isOpen, 
  onClose, 
  onSave, 
  initialData 
}: EditHeaderDialogProps) {
  const [title, setTitle] = useState(initialData?.title || '');
  const [description, setDescription] = useState(initialData?.description || '');
  const [showPreview, setShowPreview] = useState(false);

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      setTitle(initialData.title || '');
      setDescription(initialData.description || '');
    }
  }, [initialData]);

  const handleSave = () => {
    if (title.trim()) {
      onSave({
        title: title.trim(),
        description: description.trim()
      });
      setShowPreview(true);
      // Hide preview after 2 seconds, then close
      setTimeout(() => {
        setShowPreview(false);
        onClose();
      }, 2000);
    }
  };

  const handleCancel = () => {
    // Reset form to initial values
    setTitle(initialData?.title || '');
    setDescription(initialData?.description || '');
    setShowPreview(false);
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Header</DialogTitle>
        </DialogHeader>

        {showPreview ? (
          <div className="space-y-4 py-4">
            <div className="text-center p-6 bg-green-50 border border-green-200 rounded-lg">
              <div className="text-green-600 mb-2">✓ Header Updated!</div>
              <div className="space-y-2">
                <div className="font-semibold text-lg text-gray-800">
                  Preview: {title}
                </div>
                {description && (
                  <div className="text-sm text-gray-600">
                    {description}
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Header Title */}
            <div className="space-y-2">
              <Label htmlFor="header-title">Header Title *</Label>
              <Input
                id="header-title"
                placeholder="Enter header title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onKeyDown={handleKeyPress}
                autoFocus
              />
            </div>

            {/* Header Description (Optional) */}
            <div className="space-y-2">
              <Label htmlFor="header-description">Description (Optional)</Label>
              <Textarea
                id="header-description"
                placeholder="Enter header description or subtitle"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onKeyDown={handleKeyPress}
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button 
                onClick={handleSave}
                disabled={!title.trim()}
              >
                Save Header
              </Button>
            </div>

            {/* Keyboard shortcuts hint */}
            <div className="text-xs text-gray-500 text-center">
              Press Ctrl+Enter to save, Escape to cancel
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
