import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import { useState, useEffect } from "react";

const EnvironmentTab = ({ data }) => {
  const [selectedData, setSelectedData] = useState([]);

  const transformData = (data) => {
    // Define the categories and their max values
    const categories = [
      { key: "waste_management", label: "Waste Management", maxValue: 10 },
      { key: "water_management", label: "Water Management", maxValue: 10 },
      { key: "energy_management", label: "Energy Management", maxValue: 10 },
      { key: "environment", label: "Statutory Compliance", maxValue: 20 },
    ];

    // Helper function to clamp values between 0 and maxValue for graph display
    const clampForGraph = (value) => Math.max(0, value);
    const clampRemaining = (avgValue, maxValue) =>
      Math.max(0, Math.min(maxValue, maxValue - avgValue));

    return categories.map(({ key, label, maxValue }) => {
      const avgValue =
        data.reduce((sum, item) => sum + (parseFloat(item[key]) || 0), 0) / data.length;

      return {
        category: label,
        avgValue: parseFloat(avgValue.toFixed(2)),
        avgValueForGraph: clampForGraph(parseFloat(avgValue.toFixed(2))),
        maxValue,
        remainingToMax: clampRemaining(parseFloat(avgValue.toFixed(2)), maxValue),
      };
    });
  };


  useEffect(() => {
    setSelectedData(transformData(data));
  }, [data]);

  const getYAxisDomain = () => {
    return [0, 12];
  };

  const environmentYAxisDomain = getYAxisDomain();



  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        width: "100%",
        padding: "20px",
      }}
    >
      <div style={{ width: "100%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Environment Management
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={selectedData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 7, width: "25px" }} />
            <YAxis domain={environmentYAxisDomain} />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                if (name === "Maximum") {
                  return [
                    `Remaining: ${payload.remainingToMax.toFixed(2)}`,
                    name,
                  ];
                }
                return [value, name];
              }}
            />

            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              fill="#2C7C69"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#7FC8A9"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default EnvironmentTab;
