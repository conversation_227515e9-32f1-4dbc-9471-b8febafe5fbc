import React from "react";
import { useSelector } from "react-redux";
import { TabView, TabPanel } from 'primereact/tabview';


const NewPerformanceDashboard = () => {
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const tvsGD = useSelector(state => state.user.tvs)

    const crypto = require("crypto-js");
    const SECRET_KEY = "e!sq6esgdash1";
    function encryptNumber(number, secret) {
        return crypto.AES.encrypt(number, SECRET_KEY).toString();  // Return IV and encrypted text
    }

    // Function to check if user has access to specific tab
    const hasTabAccess = (tabName) => {
        const userRole = login_data?.role;
        const userPermissions = login_data?.permissions || [];
        const companyId = login_data?.information?.companyid;

        // You can customize this logic based on your requirements:

        switch (tabName) {
            case 'environment':
                // Example permission checks (uncomment and modify as needed):
                return true;
            // return userRole === 'admin' || userRole === 'clientadmin';
            // return userPermissions.includes('environment_dashboard_access');
            // return ['admin', 'manager', 'analyst'].includes(userRole);
            // return companyId && ['company1', 'company2'].includes(companyId);

            case 'water':
                // Example permission checks (uncomment and modify as needed):
                return false;
            // return userRole === 'admin' || userPermissions.includes('water_dashboard_access');
            // return userRole !== 'viewer'; // All except viewers
            // return login_data?.information?.department === 'sustainability';
         
            default:
                return false;
        }
    };
    return (
        <div className="bg-smoke font-lato">
            <div className="col-12">
                <div>
                    <div className="col-12 flex align-items-center">
                        <span className="text-big-one">
                            Hello
                            {login_data?.role === "clientadmin"
                                ? login_data?.information?.companyname
                                : login_data?.information?.empname}
                            !
                        </span>
                        <span className="ml-1">{`<${login_data.email}>`} </span>
                    </div>

                    <div
                        className="flex col-12 flex-start"
                        style={{ flexDirection: "column" }}
                    >
                        <span className="text-big-one">
                            Sustainability performance dashboard
                        </span>
                        <p className="ml-1">Detailed progress report of ESG Indicators</p>
                        {/* <Tag className="ml-3 p-tag-blue">
        
                      {login_data.role === "clientadmin"
                        ? "Enterprise Admin"
                        : getRoles(login_data.information)}
                    </Tag> */}
                    </div>

                    <div style={{ width: '100%', height: '100%' }}>
                        <TabView>
                            {hasTabAccess('environment') && (
                                <TabPanel header="Environment - India" leftIcon="pi pi-leaf">
                                    <div style={{ width: '100%', height: 'calc(100vh - 120px)', justifyContent: "center", display: 'flex' }}>
                                        <iframe
                                            style={{ width: '100%', height: '100%', border: 'none' }}
                                            src={`https://esg-enterprise-admin.web.app/?${encryptNumber(JSON.stringify({ userId: login_data.id, adminId: admin_data.id }))}`}
                                            title="Environment & Energy to Environment Dashboard"
                                        />
                                    </div>
                                </TabPanel>
                            )}

                            {/* {hasTabAccess('water') && (
                                <TabPanel header="Water & Waste" leftIcon="pi pi-tint">
                                    <div style={{ width: '100%', height: 'calc(100vh - 120px)', justifyContent: "center", display: 'flex' }}>
                                        <iframe
                                            style={{ width: '100%', height: '100%', border: 'none' }}
                                            src={'https://water-and-waste.web.app/'}
                                            title="Water & Waste Dashboard"
                                        />
                                    </div>
                                </TabPanel>
                            )} */}

                            {/* Show message if no tabs are accessible */}
                            {!hasTabAccess('environment') && !hasTabAccess('water') && (
                                <TabPanel header="No Access" leftIcon="pi pi-lock">
                                    <div style={{ width: '100%', height: 'calc(100vh - 120px)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                        <div className="text-center">
                                            <i className="pi pi-lock" style={{ fontSize: '3rem', color: '#6c757d' }}></i>
                                            <h3 style={{ color: '#6c757d', marginTop: '1rem' }}>Access Restricted</h3>
                                            <p style={{ color: '#6c757d' }}>You don't have permission to view any dashboard tabs.</p>
                                        </div>
                                    </div>
                                </TabPanel>
                            )}

                          


                        </TabView>
                    </div>


                </div>
            </div>
        </div>
    )
}

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(NewPerformanceDashboard, comparisonFn);