import React, { useState, useMemo } from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { Tag } from 'primereact/tag';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { MultiSelect } from 'primereact/multiselect';
import { DateTime } from 'luxon';

const NonComplianceDetailsDialog = ({ 
    visible, 
    onHide, 
    nonComplianceData, 
    title = "Critical Non-Compliance Details",
    esgType = "All" // "Environmental", "Social", "Governance", or "All"
}) => {
    const [globalFilter, setGlobalFilter] = useState('');

    // Custom filter states
    const [selectedCategories, setSelectedCategories] = useState([]);
    const [selectedNonComplianceTypes, setSelectedNonComplianceTypes] = useState([]);
    const [selectedESGTypes, setSelectedESGTypes] = useState([]);
    const [selectedStatuses, setSelectedStatuses] = useState([]);
    const [selectedSuppliers, setSelectedSuppliers] = useState([]);
    const [selectedLocations, setSelectedLocations] = useState([]);

    // Category labels mapping
    const getCategoryLabel = (categoryOfFinding) => {
        switch (categoryOfFinding) {
            case 1: return 'Good Practices';
            case 2: return 'Opportunity for Improvement';
            case 3: return 'Non-compliance';
            default: return 'Unknown';
        }
    };

    // Non-compliance type labels mapping
    const getNonComplianceLabel = (nonComplianceType) => {
        switch (nonComplianceType) {
            case 1: return 'Regulatory (Major)';
            case 2: return 'Regulatory (Minor)';
            case 3: return 'Minor';
            default: return 'Not Specified';
        }
    };

    // Status labels mapping
    const getStatusLabel = (status) => {
        switch (status) {
            case 1: return 'Open';
            case 2: return 'In Progress';
            case 3: return 'Completed';
            default: return 'Initiated';
        }
    };

    // ESG type labels mapping
    const getESGLabel = (esg) => {
        switch (esg) {
            case 1: return 'Environmental';
            case 2: return 'Social';
            case 3: return 'Governance';
            default: return 'Not Specified';
        }
    };

    // Date formatting template
    const dateTemplate = (rowData, field) => {
        console.log(rowData, ' Row Data ')
        const dateStr = rowData?.[field];
        return dateStr ? DateTime.fromISO(dateStr).toFormat('dd-MM-yyyy') : 'NA';
    };

    // Status template with colored tags
    const statusTemplate = (rowData) => {
        const status = getStatusLabel(rowData.status);
        const severity = rowData.status === 3 ? 'success' : 
                        rowData.status === 2 ? 'warning' : 'info';
        return <Tag value={status} severity={severity} />;
    };

    // Category template with colored tags
    const categoryTemplate = (rowData) => {
        const category = getCategoryLabel(rowData.categoryOfFinding);
        const severity = rowData.categoryOfFinding === 3 ? 'danger' : 
                        rowData.categoryOfFinding === 2 ? 'warning' : 'success';
        return <Tag value={category} severity={severity} />;
    };

    // Non-compliance type template
    const nonComplianceTypeTemplate = (rowData) => {
        if (rowData.categoryOfFinding !== 3) return 'N/A';
        const type = getNonComplianceLabel(rowData.nonComplianceType);
        const severity = rowData.nonComplianceType === 1 ? 'danger' : 
                        rowData.nonComplianceType === 2 ? 'warning' : 'info';
        return <Tag value={type} severity={severity} />;
    };

    // ESG type template
    const esgTemplate = (rowData) => {
        const esgLabel = getESGLabel(rowData.esg);
        const severity = rowData.esg === 1 ? 'success' : 
                        rowData.esg === 2 ? 'info' : 
                        rowData.esg === 3 ? 'warning' : 'secondary';
        return <Tag value={esgLabel} severity={severity} />;
    };

    // Description template with truncation
    const descriptionTemplate = (rowData) => {
        const description = rowData.description || 'No description available';
        return (
            <div title={description} style={{ maxWidth: '200px' }}>
                {description.length > 100 ? `${description.substring(0, 100)}...` : description}
            </div>
        );
    };

    // Finding template with truncation
    const findingTemplate = (rowData) => {
        const finding = rowData.finding || 'No finding specified';
        return (
            <div title={finding} style={{ maxWidth: '200px' }}>
                {finding.length > 80 ? `${finding.substring(0, 80)}...` : finding}
            </div>
        );
    };



    // Helper function to get unique options for filters based on actual data
    const getUniqueOptions = (field, labelMapFn) => {
        if (!nonComplianceData || nonComplianceData.length === 0) return [];

        let values = [...new Set(nonComplianceData.map(d => d[field]).filter(val => val !== undefined && val !== null))];

        return values.map(val => ({
            label: labelMapFn ? labelMapFn(val) : val.toString(),
            value: val
        }));
    };

    // Get dynamic filter options based on actual data
    const categoryOptions = useMemo(() => getUniqueOptions('categoryOfFinding', getCategoryLabel), [nonComplianceData]);
    const nonComplianceOptions = useMemo(() => getUniqueOptions('nonComplianceType', getNonComplianceLabel), [nonComplianceData]);
    const esgOptions = useMemo(() => getUniqueOptions('esg', getESGLabel), [nonComplianceData]);
    const statusOptions = useMemo(() => getUniqueOptions('status', getStatusLabel), [nonComplianceData]);
    const supplierOptions = useMemo(() => getUniqueOptions('vendorName'), [nonComplianceData]);
    const locationOptions = useMemo(() => getUniqueOptions('vendorLocation'), [nonComplianceData]);

    // Filter the data based on selected filters
    const filteredData = useMemo(() => {
        if (!nonComplianceData) return [];

        return nonComplianceData.filter(item => {
            // Category filter
            if (selectedCategories.length > 0 && !selectedCategories.includes(item.categoryOfFinding)) {
                return false;
            }

            // Non-compliance type filter
            if (selectedNonComplianceTypes.length > 0 && !selectedNonComplianceTypes.includes(item.nonComplianceType)) {
                return false;
            }

            // ESG type filter
            if (selectedESGTypes.length > 0 && !selectedESGTypes.includes(item.esg)) {
                return false;
            }

            // Status filter
            if (selectedStatuses.length > 0 && !selectedStatuses.includes(item.status)) {
                return false;
            }

            // Supplier filter
            if (selectedSuppliers.length > 0 && !selectedSuppliers.includes(item.vendorName)) {
                return false;
            }

            // Location filter
            if (selectedLocations.length > 0 && !selectedLocations.includes(item.vendorLocation)) {
                return false;
            }

            return true;
        });
    }, [nonComplianceData, selectedCategories, selectedNonComplianceTypes, selectedESGTypes, selectedStatuses, selectedSuppliers, selectedLocations]);

    // Clear all filters
    const clearAllFilters = () => {
        setSelectedCategories([]);
        setSelectedNonComplianceTypes([]);
        setSelectedESGTypes([]);
        setSelectedStatuses([]);
        setSelectedSuppliers([]);
        setSelectedLocations([]);
    };

    // Custom filter section component - Horizontal inline layout
    const filterSection = (
        <div className="p-3 bg-gray-50 border-round mb-3">
            <div className="flex flex-wrap align-items-center gap-3">
                <span className="font-semibold text-sm">Filters:</span>

                <div className="flex flex-column">
                    <MultiSelect
                        value={selectedCategories}
                        options={categoryOptions}
                        optionLabel="label"
                        optionValue="value"
                        onChange={(e) => setSelectedCategories(e.value)}
                        placeholder="Category"
                        filter
                        className="w-full"
                        maxSelectedLabels={1}
                        style={{ minWidth: '140px' }}
                    />
                </div>

                <div className="flex flex-column">
                    <MultiSelect
                        value={selectedNonComplianceTypes}
                        options={nonComplianceOptions}
                        optionLabel="label"
                        optionValue="value"
                        onChange={(e) => setSelectedNonComplianceTypes(e.value)}
                        placeholder="Non-Compliance Type"
                        filter
                        className="w-full"
                        maxSelectedLabels={1}
                        style={{ minWidth: '160px' }}
                    />
                </div>

                <div className="flex flex-column">
                    <MultiSelect
                        value={selectedESGTypes}
                        options={esgOptions}
                        optionLabel="label"
                        optionValue="value"
                        onChange={(e) => setSelectedESGTypes(e.value)}
                        placeholder="ESG Type"
                        filter
                        className="w-full"
                        maxSelectedLabels={1}
                        style={{ minWidth: '120px' }}
                    />
                </div>

                <div className="flex flex-column">
                    <MultiSelect
                        value={selectedStatuses}
                        options={statusOptions}
                        optionLabel="label"
                        optionValue="value"
                        onChange={(e) => setSelectedStatuses(e.value)}
                        placeholder="Status"
                        filter
                        className="w-full"
                        maxSelectedLabels={1}
                        style={{ minWidth: '120px' }}
                    />
                </div>

                <div className="flex flex-column">
                    <MultiSelect
                        value={selectedSuppliers}
                        options={supplierOptions}
                        optionLabel="label"
                        optionValue="value"
                        onChange={(e) => setSelectedSuppliers(e.value)}
                        placeholder="Supplier"
                        filter
                        className="w-full"
                        maxSelectedLabels={1}
                        style={{ minWidth: '140px' }}
                    />
                </div>

                <div className="flex flex-column">
                    <MultiSelect
                        value={selectedLocations}
                        options={locationOptions}
                        optionLabel="label"
                        optionValue="value"
                        onChange={(e) => setSelectedLocations(e.value)}
                        placeholder="Location"
                        filter
                        className="w-full"
                        maxSelectedLabels={1}
                        style={{ minWidth: '140px' }}
                    />
                </div>

                <button
                    className="p-button p-button-outlined p-button-sm"
                    onClick={clearAllFilters}
                    style={{ height: '36px' }}
                >
                    <i className="pi pi-times mr-1"></i>
                    Clear All
                </button>
            </div>
        </div>
    );

    // Supplier name template to handle null/undefined values
    const supplierNameTemplate = (rowData) => {
        return rowData.vendorName || 'Unknown Supplier';
    };

    // Location template to handle null/undefined values
    const locationTemplate = (rowData) => {
        return rowData.vendorLocation || 'Unknown Location';
    };

    // Header template for search
    const header = (
        <div className="flex justify-content-between align-items-center">
            <h5 className="m-0">
                {title} ({filteredData?.length || 0} of {nonComplianceData?.length || 0} records)
            </h5>
            <span className="p-input-icon-left">
                <i className="pi pi-search" />
                <InputText
                    type="search"
                    onInput={(e) => setGlobalFilter(e.target.value)}
                    placeholder="Search..."
                />
            </span>
        </div>
    );

    return (
        <Dialog
            header={
                <div className="flex align-items-center">
                    <i className="pi pi-exclamation-triangle mr-2" style={{ fontSize: '1.5rem', color: '#dc3545' }}></i>
                    <span className="font-bold">{title}</span>
                </div>
            }
            visible={visible}
            onHide={onHide}
            style={{ width: '95vw', maxWidth: '1400px' }}
            breakpoints={{ '960px': '98vw' }}
            className="non-compliance-details-dialog"
        >
            <Card>
                {filterSection}
                <DataTable
                    value={filteredData}
                    paginator
                    rows={10}
                    rowsPerPageOptions={[10, 25, 50, 100]}
                    globalFilter={globalFilter}
                    header={header}
                    scrollable
                    scrollHeight="500px"
                    className="p-datatable-gridlines"
                    emptyMessage="No non-compliance records found."
                    removableSort
                >
                    <Column 
                        field="id" 
                        header="ID" 
                        sortable 
                        style={{ minWidth: '80px' }}
                    />
                    <Column 
                        field="finding" 
                        header="Finding" 
                        body={findingTemplate}
                        sortable 
                        style={{ minWidth: '200px' }}
                    />
                    <Column
                        field="categoryOfFinding"
                        header="Category"
                        body={categoryTemplate}
                        sortable
                        style={{ minWidth: '150px' }}
                    />
                    <Column
                        field="nonComplianceType"
                        header="Non-Compliance Type"
                        body={nonComplianceTypeTemplate}
                        sortable
                        style={{ minWidth: '180px' }}
                    />
                    <Column
                        field="esg"
                        header="ESG Type"
                        body={esgTemplate}
                        sortable
                        style={{ minWidth: '120px' }}
                    />
                    <Column
                        field="description"
                        header="Description"
                        body={descriptionTemplate}
                        style={{ minWidth: '250px' }}
                    />
                    <Column
                        field="vendorName"
                        header="Supplier"
                        body={supplierNameTemplate}
                        sortable
                        style={{ minWidth: '200px' }}
                    />
                    <Column
                        field="vendorLocation"
                        header="Location"
                        body={locationTemplate}
                        sortable
                        style={{ minWidth: '150px' }}
                    />
                    <Column
                        field="status"
                        header="Status"
                        body={statusTemplate}
                        sortable
                        style={{ minWidth: '120px' }}
                    />
                    <Column 
                        field="created_on" 
                        header="Created On" 
                        body={(rowData) => dateTemplate(rowData, 'created_on')}
                        sortable 
                        style={{ minWidth: '120px' }}
                    />
                    <Column 
                        field="dueDate" 
                        header="Due Date" 
                        body={(rowData) => dateTemplate(rowData, 'dueDate')}
                        sortable 
                        style={{ minWidth: '120px' }}
                    />
                </DataTable>
            </Card>
        </Dialog>
    );
};

export default NonComplianceDetailsDialog;
