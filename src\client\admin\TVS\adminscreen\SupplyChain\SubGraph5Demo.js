import React, { useEffect, useState, useMemo } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON>Axis,
  <PERSON>ltip,
  Legend,
  CartesianGrid,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import CriticalNonCompliances from "./NonComplianceComponent";
import NonComplianceDetailsDialog from "./NonComplianceDetailsDialog";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const SubGraph5Demo = ({ supplyData }) => {
  const [chartData, setChartData] = useState([]);
  const [nonComplianceCount, setNonComplianceCount] = useState(0);
  const [nonComplianceData, setNonComplianceData] = useState([]);
  const [dialogVisible, setDialogVisible] = useState(false);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{ backgroundColor: "#fff", border: "1px solid #ccc", borderRadius: "8px", padding: "10px", fontSize: "14px" }}>
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map((entry) => (
            <p key={entry.name} style={{ margin: 0, color: "black" }}>
              {`${entry.name}:${entry.name === "Maximum" ? 5 : entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }) => (
    <ul style={{ display: "flex", listStyleType: "none", justifyContent: "center", padding: 0, marginTop: "10px" }}>
      {payload.map((entry, index) => (
        <li key={`item-${index}`} style={{ marginRight: "5px" }}>
          <span style={{ backgroundColor: entry.color, marginRight: 4, fontSize: "20px", width: "10px", height: "10px", borderRadius: "50%", display: "inline-block", marginTop: "10px" }}></span>
          <span style={{ color: "#555", fontSize: "14px" }}>{entry.value}</span>
        </li>
      ))}
    </ul>
  );

  const wrapText = (text, width = 40) => {
    let words = text.split(" ");
    let lines = [];
    let currentLine = "";
    words.forEach((word) => {
      if ((currentLine + " " + word).length > width) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine += (currentLine ? " " : "") + word;
      }
    });
    lines.push(currentLine);
    return lines.map((line, index) => (
      <tspan key={index} x="0" dy={index === 0 ? 0 : 10}>
        {line}
      </tspan>
    ));
  };

  const CustomizedTick = ({ x, y, payload }) => (
    <g transform={`translate(${x},${y})`}>
      <text x={0} y={0} textAnchor="middle" fontSize={11} fill="#666" dominantBaseline="hanging">
        {wrapText(payload.value, 18)}
      </text>
    </g>
  );

  useEffect(() => {
    const fetchGovernanceNonComplianceCount = async () => {
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];

        const groupedByVendor = allAssignments.reduce((acc, item) => {
          if (!item.vendorCode) return acc;
          if (!acc[item.vendorCode]) acc[item.vendorCode] = [];
          acc[item.vendorCode].push(item);
          return acc;
        }, {});

        let totalGovernanceNonCompliances = 0;
        const allGovernanceNonCompliances = [];

        Object.values(groupedByVendor).forEach(assignments => {
          const latest = assignments.sort((a, b) =>
            new Date(b.created_on) - new Date(a.created_on)
          )[0];

          const responseStr = latest?.auditorAssignmentSubmission?.response;
          if (!responseStr) return;

          let sectionMap = [];
          try {
            const auditorResponse = JSON.parse(responseStr);
            sectionMap = auditorResponse.flatMap(section =>
              (section.assessmentSubSection1s || []).map(sub => ({
                ...sub,
                order: section.order,
                esg: section.order === 1 ? 1 : section.order >= 5 ? 3 : 2
              }))
            );
          } catch (e) {
            console.error("Invalid response JSON", e);
            return;
          }

          const getESG = (subsection2Id) => {
            return sectionMap.find(s => s.id === subsection2Id)?.esg || null;
          };

          const actions = (latest.supplierActions || []).map(action => ({
            ...action,
            esg: getESG(action.assessmentSubSection2Id),
            vendorName: latest?.vendor?.supplierName || 'Unknown Supplier',
            vendorLocation: latest?.vendor?.supplierLocation || latest?.location || 'Unknown Location',
            msiId: latest?.msiId,
            auditStartDate: latest?.auditStartDate,
            auditEndDate: latest?.auditEndDate
          }));

          const govNonConformances = actions.filter(
            a => a.categoryOfFinding === 3 && a.esg === 3
          );

          totalGovernanceNonCompliances += govNonConformances.length;
          allGovernanceNonCompliances.push(...govNonConformances);
        });

        setNonComplianceCount(totalGovernanceNonCompliances);
        setNonComplianceData(allGovernanceNonCompliances);
      } catch (error) {
        console.error("Error fetching governance non-compliances:", error);
        setNonComplianceCount(0);
      }
    };

    fetchGovernanceNonComplianceCount();
  }, [supplyData]);

  const memoizedChartData = useMemo(() => {
    if (supplyData.length === 0) return [];

    const totalSuppliers = supplyData.length;

    const totalGovernance = supplyData.reduce(
      (sum, item) => sum + (parseFloat(item.governance_framework) || 0), 0
    );
    const totalLegal = supplyData.reduce(
      (sum, item) => sum + (parseFloat(item.legal_compliances) || 0), 0
    );

    const avgGovernance = (totalGovernance / totalSuppliers).toFixed(2);
    const avgLegal = (totalLegal / totalSuppliers).toFixed(2);

    return [
      {
        category: "Supplier Governance Framework",
        maxScore: 5 - avgGovernance,
        avgScore: avgGovernance,
      },
      {
        category: "Legal Compliances",
        maxScore: 5 - avgLegal,
        avgScore: avgLegal,
      },
    ];
  }, [supplyData]);

  useEffect(() => {
    setChartData(memoizedChartData);
  }, [memoizedChartData]);

  return (
    <div className="container mt-4">
      <h5 className="mb-3 text-center text-dark">Governance Section Performance</h5>
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} barSize={50}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="category" fontSize={12} tick={<CustomizedTick />} interval={0} />
          <YAxis domain={[0, 5]} />
          <Tooltip content={CustomTooltip} />
          <Legend content={CustomLegend} />
          <Bar dataKey="avgScore" stackId="a" fill="#4A90E2" name="Achieved">
            <LabelList dataKey="avgScore" position="insideBottom" style={{ fontSize: "12px", fill: "white" }} />
          </Bar>
          <Bar dataKey="maxScore" stackId="a" fill="#AFCBFF" name="Maximum" />
        </BarChart>
      </ResponsiveContainer>
      <div className="col-12 flex justify-content-center">
        <CriticalNonCompliances
          count={nonComplianceCount}
          onClick={() => setDialogVisible(true)}
        />
      </div>

      {/* Non-Compliance Details Dialog */}
      <NonComplianceDetailsDialog
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        nonComplianceData={nonComplianceData}
        title="Governance Non-Compliance Details"
        esgType="Governance"
      />
    </div>
  );
};

export default SubGraph5Demo;
