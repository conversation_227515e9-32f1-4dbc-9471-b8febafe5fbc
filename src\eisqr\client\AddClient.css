/* Custom styles for AddClient component */

.custom-datatable {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.custom-datatable .p-datatable-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
}

.custom-datatable .p-datatable-thead > tr > th {
    background-color: #f8f9fa;
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
}

.custom-datatable .p-datatable-tbody > tr {
    transition: all 0.2s ease;
}

.custom-datatable .p-datatable-tbody > tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid #f1f3f4;
}

.custom-datatable .p-paginator {
    background-color: #ffffff;
    border: none;
    padding: 1rem;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e9ecef;
}

.custom-dialog .p-dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.custom-dialog .p-dialog-content {
    padding: 2rem;
    background-color: #ffffff;
}

.custom-dialog .p-dialog-footer {
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
    padding: 1.5rem 2rem;
}

/* Form field enhancements */
.field {
    margin-bottom: 1.5rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.field .p-inputtext,
.field .p-dropdown,
.field .p-calendar,
.field .p-inputnumber,
.field .p-chips,
.field .p-multiselect {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.field .p-inputtext:focus,
.field .p-dropdown:focus,
.field .p-calendar:focus,
.field .p-inputnumber:focus,
.field .p-chips:focus,
.field .p-multiselect:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field .p-invalid {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

/* Button enhancements */
.p-button {
    transition: all 0.2s ease;
}

.p-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.p-button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.p-button-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.p-button-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

/* Loading state */
.p-datatable-loading-overlay {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .custom-datatable .p-datatable-thead > tr > th,
    .custom-datatable .p-datatable-tbody > tr > td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .custom-dialog .p-dialog-content {
        padding: 1rem;
    }
    
    .field {
        margin-bottom: 1rem;
    }
}

/* Animation for new rows */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.custom-datatable .p-datatable-tbody > tr {
    animation: fadeInUp 0.3s ease-out;
}

/* Enhanced search input */
.p-input-icon-left > .pi {
    color: #667eea;
    font-size: 1.1rem;
}

/* Checkbox styling */
.p-checkbox .p-checkbox-box {
    border-radius: 4px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.p-checkbox .p-checkbox-box.p-highlight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* Tooltip styling */
.p-tooltip .p-tooltip-text {
    background-color: #2c3e50;
    border-radius: 6px;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Error message styling */
.p-error {
    color: #e74c3c;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Success message styling */
.p-success {
    color: #27ae60;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Card enhancements */
.card {
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: none;
    background: #ffffff;
}

.surface-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
}

/* Header gradient */
.text-primary {
    color: #667eea !important;
}

/* Logo container styling */
.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Action buttons container */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
}

.action-buttons .p-button {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
}

/* Form section dividers */
.form-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Section headers */
.section-header {
    font-size: 1.125rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #667eea;
    display: inline-block;
}
