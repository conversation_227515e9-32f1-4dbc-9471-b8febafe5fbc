/* Custom styles for AddClient component - Scoped to avoid conflicts */

.add-client-container .custom-datatable {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.add-client-container .custom-datatable .p-datatable-header {
    background: #ffffff;
    color: #315975;
    border: none;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e9ecef;
}

.add-client-container .custom-datatable .p-datatable-thead > tr > th {
    background-color: #f8f9fa;
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
}

.add-client-container .custom-datatable .p-datatable-tbody > tr {
    transition: all 0.2s ease;
}

.add-client-container .custom-datatable .p-datatable-tbody > tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-client-container .custom-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid #f1f3f4;
}

.add-client-container .custom-datatable .p-paginator {
    background-color: #ffffff;
    border: none;
    padding: 1rem;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e9ecef;
}

.add-client-container .custom-dialog .p-dialog-header {
    background: #ffffff;
    color: #315975;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e9ecef;
}

.add-client-container .custom-dialog .p-dialog-content {
    padding: 2rem;
    background-color: #ffffff;
}

.add-client-container .custom-dialog .p-dialog-footer {
    background-color: #ffffff;
    border-radius: 0 0 12px 12px;
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
}

/* Form field enhancements - Scoped to AddClient */
.add-client-container .field {
    margin-bottom: 1.5rem;
}

.add-client-container .field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.add-client-container .field .p-inputtext,
.add-client-container .field .p-dropdown,
.add-client-container .field .p-calendar,
.add-client-container .field .p-inputnumber,
.add-client-container .field .p-chips,
.add-client-container .field .p-multiselect {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.add-client-container .field .p-inputtext:focus,
.add-client-container .field .p-dropdown:focus,
.add-client-container .field .p-calendar:focus,
.add-client-container .field .p-inputnumber:focus,
.add-client-container .field .p-chips:focus,
.add-client-container .field .p-multiselect:focus {
    border-color: #315975;
    box-shadow: 0 0 0 3px rgba(49, 89, 117, 0.1);
}

.add-client-container .field .p-invalid {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

/* Button enhancements - Scoped to AddClient */
.add-client-container .p-button {
    transition: all 0.2s ease;
}

.add-client-container .p-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.add-client-container .p-button-primary {
    background: #315975;
    border: none;
}

.add-client-container .p-button-success {
    background: linear-gradient(135deg, white 0%, #a8e6cf 100%);
    border: none;
}

.add-client-container .p-button-danger {
    background: linear-gradient(135deg, white 0%, #ff4b2b 100%);
    border: none;
}

/* Loading state - Scoped to AddClient */
.add-client-container .p-datatable-loading-overlay {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

/* Responsive adjustments - Scoped to AddClient */
@media (max-width: 768px) {
    .add-client-container .custom-datatable .p-datatable-thead > tr > th,
    .add-client-container .custom-datatable .p-datatable-tbody > tr > td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .add-client-container .custom-dialog .p-dialog-content {
        padding: 1rem;
    }

    .add-client-container .field {
        margin-bottom: 1rem;
    }
}

/* Animation for new rows - Scoped to AddClient */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.add-client-container .custom-datatable .p-datatable-tbody > tr {
    animation: fadeInUp 0.3s ease-out;
}

/* Enhanced search input - Scoped to AddClient */
.add-client-container .p-input-icon-left > .pi {
    color: #315975;
    font-size: 1.1rem;
}

/* Checkbox styling - Scoped to AddClient */
.add-client-container .p-checkbox .p-checkbox-box {
    border-radius: 4px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.add-client-container .p-checkbox .p-checkbox-box.p-highlight {
    background: #315975;
    border-color: #315975;
}

/* Tooltip styling - Scoped to AddClient */
.add-client-container .p-tooltip .p-tooltip-text {
    background-color: #2c3e50;
    border-radius: 6px;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Error message styling - Scoped to AddClient */
.add-client-container .p-error {
    color: #e74c3c;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Success message styling - Scoped to AddClient */
.add-client-container .p-success {
    color: #27ae60;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Card enhancements - Scoped to AddClient */
.add-client-container .card {
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: none;
    background: #ffffff;
}

.add-client-container .surface-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
}

/* Header color - Scoped to AddClient */
.add-client-container .text-primary {
    color: #315975 !important;
}

/* Logo container styling - Scoped to AddClient */
.add-client-container .logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Action buttons container - Scoped to AddClient */
.add-client-container .action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
}

.add-client-container .action-buttons .p-button {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
}

/* Form section dividers - Scoped to AddClient */
.add-client-container .form-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.add-client-container .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Section headers - Normal text style, not button-like */
.add-client-container .section-header {
    font-size: 1.125rem;
    font-weight: 600;
    color: #315975;
    margin-bottom: 1rem;
    padding: 0;
    border: none;
    background: none;
    display: block;
    text-align: left;
}
