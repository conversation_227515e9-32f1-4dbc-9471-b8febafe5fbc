import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  LineChart,
  Line,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { Menu } from "primereact/menu";
import { But<PERSON> } from "primereact/button";
import { API } from "../../../../../constants/api_url";
import APIServices from "../../../../../service/APIService";
import moment from "moment";

const MSIScoresOverTime = ({ data }) => {
  const [chartData, setChartData] = useState([]);
  const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);

  // Fetch dealer self-assessment submissions
  useEffect(() => {
    const fetchDealerSelfSubmissions = async () => {
      try {
        const response = await APIServices.get(API.DealerSelfSubmission);
        if (response.data) {
          console.log(response.data, ' Dealer Self Submissions ')
          setDealerSelfSubmissions(response.data);
        }
      } catch (error) {
        console.error("Error fetching dealer self submissions:", error);
        setDealerSelfSubmissions([]);
      }
    };

    fetchDealerSelfSubmissions();
  }, []);

  // Process data to create chart data when data or dealerSelfSubmissions change
  useEffect(() => {
    console.log("=== MSI Scores Over Time Data Processing ===");
    console.log("Received data:", data);
    console.log("Data length:", data?.length || 0);
    console.log("Self submissions:", dealerSelfSubmissions?.length || 0);

    if (!data || !data.length) {
      console.log("No data available, setting empty chart data");
      setChartData([]);
      return;
    }

    // Create a map to store scores by month
    const monthlyData = {};

    // Process dealer data (this contains completed MSI calibrations with both Sales and Service)
    data.forEach((dealer, index) => {
      console.log(`Processing dealer ${index + 1}:`, {
        date_of_calibration: dealer.date_of_calibration,
        total_score: dealer.total_score,
        branch_code: dealer.branch_code,
        dealer_name: dealer.dealer_name
      });

      if (dealer.date_of_calibration) {
        // Parse the date more carefully - your date format is "2025-04-30"
        const parsedDate = moment(dealer.date_of_calibration, 'YYYY-MM-DD');

        // Validate the parsed date
        if (!parsedDate.isValid()) {
          console.warn(`Invalid date for dealer ${index + 1}:`, dealer.date_of_calibration);
          return;
        }

        const month = parsedDate.format('MMM YY');
        console.log(`Dealer ${index + 1} date: ${dealer.date_of_calibration} -> month: ${month}`);

        if (!monthlyData[month]) {
          monthlyData[month] = {
            month,
            calibrationScores: [],
            updatedScores: [],
            selfAssessmentScores: [],
            dealerCount: new Set() // Track unique dealers
          };
        }

        // Add dealer to count
        monthlyData[month].dealerCount.add(dealer.branch_code);

        // Process calibration score (total_score from dealer dashboard data)
        if (dealer.total_score && typeof dealer.total_score === 'number') {
          console.log(`Adding calibration score for ${month}:`, dealer.total_score);
          monthlyData[month].calibrationScores.push(dealer.total_score);
        } else {
          console.warn(`Invalid total_score for dealer ${index + 1}:`, dealer.total_score);
        }
      }
    });

    // Process self-assessment data if available
    console.log("Processing self-assessment submissions...");
    if (dealerSelfSubmissions && dealerSelfSubmissions.length > 0) {
      dealerSelfSubmissions.forEach((submission, index) => {
        console.log(`Self-assessment ${index + 1}:`, {
          reportingPeriod: submission.reportingPeriod,
          score: submission.score
        });

        if (submission.reportingPeriod && submission.reportingPeriod[0] && submission.score) {
          try {
            const scoreObj = JSON.parse(submission.score);
            console.log(`Parsed score object:`, scoreObj);

            if (scoreObj.overallScore && scoreObj.overallScore !== 'NA' && scoreObj.overallScore !== '-') {
              // Parse the reporting period - your format is "May-2025"
              const reportingPeriod = submission.reportingPeriod[0];
              const parsedDate = moment(reportingPeriod, 'MMM-YYYY');

              // Validate the parsed date
              if (!parsedDate.isValid()) {
                console.warn(`Invalid reporting period:`, reportingPeriod);
                return;
              }

              const month = parsedDate.format('MMM YY');
              console.log(`Self-assessment period: ${reportingPeriod} -> month: ${month}, score: ${scoreObj.overallScore}`);

              if (!monthlyData[month]) {
                monthlyData[month] = {
                  month,
                  calibrationScores: [],
                  updatedScores: [],
                  selfAssessmentScores: [],
                  dealerCount: new Set()
                };
              }

              const score = parseFloat(scoreObj.overallScore);
              if (!isNaN(score)) {
                monthlyData[month].selfAssessmentScores.push(score);
              } else {
                console.warn(`Invalid overall score:`, scoreObj.overallScore);
              }
            }
          } catch (e) {
            console.error("Error parsing self-assessment score:", e);
          }
        }
      });
    } else {
      console.log("No self-assessment submissions available");
    }

    // Convert to chart format and calculate averages
    console.log("Monthly data before processing:", monthlyData);

    const chartDataArray = Object.values(monthlyData)
      .map(monthData => {
        const result = {
          month: monthData.month,
          selfAssessment: monthData.selfAssessmentScores.length > 0
            ? Math.round(monthData.selfAssessmentScores.reduce((sum, score) => sum + score, 0) / monthData.selfAssessmentScores.length)
            : null,
          calibration: monthData.calibrationScores.length > 0
            ? Math.round(monthData.calibrationScores.reduce((sum, score) => sum + score, 0) / monthData.calibrationScores.length)
            : null,
          dealerCount: monthData.dealerCount.size
        };
        console.log(`Chart data for ${monthData.month}:`, result);
        return result;
      })
      .filter(item => {
        // Only include months that have actual data
        const hasData = item.selfAssessment !== null || item.calibration !== null;

        // Also validate the month format and ensure it's a reasonable date
        const monthDate = moment(item.month, 'MMM YY');
        const isValidMonth = monthDate.isValid() &&
                           monthDate.year() >= 2020 &&
                           monthDate.year() <= 2030;

        // Manually limit to May 2025 and earlier
        const maxDate = moment('May 25', 'MMM YY'); // May 2025
        const isWithinLimit = monthDate.isSameOrBefore(maxDate);

        if (!isValidMonth) {
          console.warn(`Filtering out invalid month: ${item.month}`);
        }

        if (!isWithinLimit) {
          console.warn(`Filtering out month beyond May 25: ${item.month}`);
        }

        return hasData && isValidMonth && isWithinLimit;
      })
      .sort((a, b) => moment(a.month, 'MMM YY').valueOf() - moment(b.month, 'MMM YY').valueOf());

    // Convert to cumulative data
    console.log("Converting to cumulative data...");
    let cumulativeSelfAssessment = 0;
    let cumulativeCalibration = 0;
    let selfAssessmentCount = 0;
    let calibrationCount = 0;

    const cumulativeChartData = chartDataArray.map((item) => {
      // Add current month's data to cumulative totals
      if (item.selfAssessment !== null) {
        cumulativeSelfAssessment += item.selfAssessment;
        selfAssessmentCount += 1;
      }

      if (item.calibration !== null) {
        cumulativeCalibration += item.calibration;
        calibrationCount += 1;
      }

      const result = {
        month: item.month,
        selfAssessment: selfAssessmentCount > 0
          ? Math.round(cumulativeSelfAssessment / selfAssessmentCount)
          : null,
        calibration: calibrationCount > 0
          ? Math.round(cumulativeCalibration / calibrationCount)
          : null,
        dealerCount: item.dealerCount
      };

      console.log(`Cumulative data for ${item.month}:`, {
        ...result,
        cumulativeSelfTotal: cumulativeSelfAssessment,
        cumulativeCalibrationTotal: cumulativeCalibration,
        selfCount: selfAssessmentCount,
        calibrationCount: calibrationCount
      });

      return result;
    });

    console.log("Final cumulative chart data:", cumulativeChartData);
    console.log("Months in cumulative chart data:", cumulativeChartData.map(item => item.month));

    // Only use real data, no fallback
    console.log("Setting cumulative chart data - length:", cumulativeChartData.length);
    setChartData(cumulativeChartData);
  }, [data, dealerSelfSubmissions]);
  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: '0 0 5px 0', fontWeight: 'bold' }}>{`Month: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ margin: '2px 0', color: entry.color }}>
              {`${entry.name}: ${entry.value !== null ? entry.value : 'N/A'}`}
            </p>
          ))}
          {payload[0] && payload[0].payload && payload[0].payload.dealerCount && (
            <p style={{ margin: '2px 0', color: '#666', fontSize: '12px' }}>
              {`Dealers: ${payload[0].payload.dealerCount}`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            // downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            // printChart(chartRef);
          },
        },
      ],
    },
  ];

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h3 style={{ fontSize: "18px", margin: "25px" }}>
          MSI Scores Over Time
        </h3>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19" />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>

      {/* Debug info */}
    
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" textAnchor="end" interval={0} />
          <YAxis />
          <Tooltip content={<CustomTooltip />} />
          <Legend content={CustomLegend} />
          <Line
            type="monotone"
            dataKey="selfAssessment"
            stroke="#0000cc"
            name="MSI Self Assessment Score"
            connectNulls={false}
          />
          <Line
            type="monotone"
            dataKey="calibration"
            stroke="#006600"
            name="MSI Calibration Score"
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default MSIScoresOverTime;
