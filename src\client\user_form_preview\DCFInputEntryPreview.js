import Axios from "axios";
import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { EditText } from "react-edit-text";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { CascadeSelect } from "primereact/cascadeselect";
import Swal from "sweetalert2";
import { Checkbox } from "primereact/checkbox";
import $ from "jquery";
import { API } from "../../constants/api_url";
import { ContextMenu } from 'primereact/contextmenu';
import { Tooltip } from "primereact/tooltip";
import moment from "moment";
import { Calendar } from "primereact/calendar";
import { RadioButton } from "primereact/radiobutton";
import { InputTextarea } from 'primereact/inputtextarea'
import { useHistory, useLocation, useParams } from "react-router-dom";
import { Tag } from "primereact/tag";
import { BP8EQ2, BP2LQ3, BP2LQ5, Stationary_Combustion_, Fugitive_, Mobile_Combustion_, Purchase_Goods_Services_, GR2LQ1, GR2LQ2, GR2LQ3, Business_Travel_, HotelStay, Employee_Category, Diversity_of_Employees, Electricity, Employee_Category_Diversity_STT, Total_No_of_Employee_Left_STT, Total_No_of_New_Employee_STT, Capital_Goods, Scope3_Investments, Total_No_of_Employee_Hire_TurnOver_STT, Water_Withdrawl_STT, Water_Disposal_STT, Electricity_STT, Upstream_Trans_Dist, Downstream_Trans_Dist, ParentalLeave_STT, Proportion_Spending_Local_Suppliers_STT, Hazardous_Waste_Disposal_STT, Social_Impact_Programmes, Performance_Career_Development_STT, Employee_Training_Hours_STT, Business_Travel_Air, Business_Travel_Land, Business_Travel_Rail, NonHazardous_Waste_Disposal_STT, Stationary_Combustion_Rotary, Mobile_Combustion_Rotary, Hazardous_NonHazardous_Rotary, Employee_Demographics_263, VehicleInformation, VehicleSold, DistanceTravelledVehicle, SoldProduct, Employee_Diversity_TVS, Emissions_Due_Downstream_TransportationAndDistribution_SpendBased, HazardousWaste_TVS, New_Employee_Turnover_TVS, New_Employee_Hires_TVS, Employee_Commutte_TVS, Upstream_Leased_Assets_Electricity, Upstream_Leased_Assets_Stationary, Employees_TQC, Blue_Collar_Manufacturing_employees, OHC_Training, Leadership_B3, White_Collar_Manufacturing_Employees, Employees_Data_Privacy, HazardousWaste_TVS_ } from "../../client/hardcoded/hardcodedforms";
import { Editor } from "primereact/editor";
import { hardcoded } from "../constants/hardcodedid";
import APIServices from "../../service/APIService";
import XlsxPopulate from "xlsx-populate";
import { DateTime } from "luxon";
import { FileUpload } from "primereact/fileupload";
window.jQuery = $;
window.$ = $;

const DCFInputEntryPreview = () => {
    const admin_data = useSelector((state) => state.user.admindetail);
    const [data, setData] = useState([])
    const [sitelist, setSiteList] = useState([])
    const navigate = useHistory()
    const forceUpdate = useForceUpdate();
    const { id } = useParams();
    const params = useLocation()
    // const hardcodeddcf = ['10', '11', '36', '15', '16', '188', '195', '196','245']
    useEffect(async () => {


        let uriString = {
            "include": [{ "relation": "locationTwos", "scope": { "include": [{ "relation": "locationThrees" }] } }]

        }

        let site_url = API.LocationOne_UP(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        APIServices.get(API.DCF_Edit(id)).then((res) => {
            // res.data.standard = params.state.standard


            setData(res.data)


        })



        APIServices.get(site_url).then((res) => {
            let site_list = []
            res.data.forEach((country) => {
                if (country.locationTwos !== undefined) {
                    country.locationTwos.forEach((city) => {
                        if (city.locationThrees !== undefined) {
                            city.locationThrees.forEach((site) => {

                                site_list.push({ name: site.name + ' (' + city.name + ')', id: site.id, country: { id: country.id, name: country.name }, city: { id: city.id, name: city.name } })
                            })
                        }
                    })
                }
            })
            setSiteList(site_list)
        })

    }, [admin_data]);

    const getRP = (months) => {
        if (months.includes('to')) {
            let startDate = moment(months.split('to')[0].trim())
            let endDate = moment(months.split('to')[1].trim())
            let rp = []
            while (startDate <= endDate) {

                rp.push(startDate.format('MM-YYYY'));
                startDate.add(1, 'month');


            }
            return rp
        } else {
            return [moment(months).format('MM-YYYY')]
        }
    }

    const onCheckBoxSelected = (item, cbind) => {
        console.log(item)
        item.values.map((items, ind) => {
            if (ind === cbind) {

                items.selected = !items.selected
            }
        })
        forceUpdate()
    }
    const renderHeader = () => {
        return (
            <span className="ql-formats">
                <button className="ql-bold" aria-label="Bold"></button>
                <button className="ql-italic" aria-label="Italic"></button>
                <button className="ql-underline" aria-label="Underline"></button>
                <button className="ql-formats" aria-label="Underline"></button>

            </span>
        );
    };
    const onRadioButtonSelected = (item, cbind) => {
        console.log(item)
        item.values.map((items, ind) => {
            if (ind === cbind) {

                items.selected = true
            } else {
                items.selected = false
            }
        })
        forceUpdate()
    }
    const onDateSelected = (item, val) => {

        item.value = val;
        forceUpdate()
    }
    const onNumberChange = (item, val, nan) => {
        if (nan !== undefined) {
            if (isNaN(val)) {
                item.value = undefined
            } else {
                item.value = val;
            }

        } else {
            item.value = val;
        }

        forceUpdate()
    }

    const onChangeDropwdown = (item, val) => {
        item.value = val;
        console.log(val)
        item.values.forEach((i) => {
            if (i.value === val) {
                i.selected = true
            } else {
                i.selected = false
            }
        })
        forceUpdate()
    }
    function decodeHTMLEntities(text) {
        if (document) {
            const textarea = document.createElement('textarea');
            textarea.innerHTML = text;
            console.log('rendererere')
            return textarea.value;
        } else {
            // Fallback for non-browser environments
            const entityMap = {
                '&nbsp;': ' ',
                '&lt;': '<',
                '&gt;': '>',
                '&amp;': '&',
                '&quot;': '"',
                '&#39;': "'",
            };

            return text.replace(/&[a-zA-Z0-9#]+;/g, (match) => entityMap[match] || match);
        }
    }
    const renderItems = (item, index) => {


        if (item.type === 'checkbox-group') {

            return (
                <div className="flex flex-wrap  gap-3  grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 text-justify fs-16 fw-5'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')}
                        {item.description !== undefined && item.description.trim().length !== 0 && <i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i>} </label>
                    <div className="col-5">
                        {item.values.map((cb, cbind) => {
                            return (
                                <div className="flex text-justify fs-14 fw-5" style={{ marginBottom: 10 }}>
                                    <Checkbox inputId={"cb" + index + cbind} name={cb.label} value={cb.value} onChange={(e) => { onCheckBoxSelected(item, cbind) }} checked={cb.selected} />
                                    <label htmlFor={"cb" + index + cbind} className="ml-2">{cb.label}</label>
                                </div>
                            )
                        })

                        }
                    </div>

                </div>
            )
        } else if (item.type === 'date') {

            return (
                <div className="flex flex-wrap  gap-3  grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'> {item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')}
                        {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i></span>} </label>
                    <Calendar dateFormat={item.format === 'date' ? 'dd/mm/yy' : item.format === 'year' ? 'yy' : item.format === 'month' ? 'M-yy' : 'dd/mm/yy'} view={item.format || "date"} className="col-5 fs-14 fw-4" value={item.value !== null ? moment(item.value).toDate() : null} onChange={(e) => { onDateSelected(item, e.value) }} />
                </div>
            )
        } else if (item.type === 'number') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i></span>} </label>
                    <div className="col-5 fs-14 fw-4" >
                        <InputText type='number' onWheel={(e) => e.target.blur()} keyfilter="num" style={{ width: '100%' }} value={item.value} onChange={(e) => { onNumberChange(item, parseFloat(e.target.value), 'as') }} />

                    </div>
                </div>
            )
        } else if (item.type === 'paragraph') {
            return (
                <div className={`flex flex-wrap  gap-3 fs-16 fw-5`} style={{ padding: 10 }}>
                    <Tooltip target={".tooltip" + index} position='top' />

                    <div className={`col-12 text-${item.alignment || 'left'}`}>
                        <span
                            dangerouslySetInnerHTML={{
                                __html: decodeHTMLEntities(item.label)
                                    .replace(/\n/g, " ")       // Replace newline characters with spaces
                                    .replace(/&nbsp;/g, " ")   // Replace non-breaking spaces with regular spaces
                                    .replace(/&amp;/g, "&").replace(/style="[^"]*"/g, '')    // Replace encoded ampersands with `&`
                            }}
                        ></span>
                        {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i></span>}
                    </div>

                </div>
            )
        } else if (item.type === 'radio-group') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i></span>} </label>
                    <div className="col-5 grid" style={{ padding: 10 }} >
                        {item.values.map((cb, cbind) => {
                            return (
                                <div className="p-2 flex text-justify fs-14 fw-5 align-items-center" >
                                    <RadioButton inputId={"rg" + index + cbind} name={cb.label} value={cb.value} onChange={(e) => onRadioButtonSelected(item, cbind)} checked={cb.selected === true} />

                                    <label htmlFor={"rg" + index + cbind} className="ml-2">{cb.label}</label>
                                </div>
                            )
                        })

                        }
                    </div>

                </div>
            )
        } else if (item.type === 'select') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fw-5 fs-16 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i></span>} </label>


                    <div className="col-5 fw-4 fs-14">
                        <Dropdown options={item.values} style={{ width: '100%' }} optionLabel='label' optionValue="value" value={item.value} onChange={(e) => { onChangeDropwdown(item, e.value) }} />
                    </div>

                </div>
            )
        } else if (item.type === 'text') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}


                        > help</i></span>} </label>
                    <div className="col-5 fs-14 fw-4" >
                        <InputText style={{ width: '100%' }} value={item.value} onChange={(e) => { onNumberChange(item, e.target.value) }} />

                    </div>
                </div>
            )
        } else if (item.type === 'textarea') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}

                        > help</i></span>} </label>
                    <div className="col-5 " >
                        <Editor className="text-area" value={item.value} style={{ width: '100%', padding: 10, maxHeight: 350, height: 158, overflow: 'scroll' }} onTextChange={(e) => onNumberChange(item, e.htmlValue)} />

                    </div>

                </div>
            )
        } else if (item.type === 'file') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ marginBottom: 15, padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} />
                    <label style={{ display: 'flex' }} className='col-5 fw-5 fs-16 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={item.description}
                            data-pr-position="right"
                            data-pr-at="right+5 top"
                            data-pr-my="left center-2"> help</i></span>} </label>
                    <div className="col-5" >
                        <div style={{
                            background: '#f8f9fa',
                            border: '1px solid #ced4da',
                            borderRadius: '6px 6px 0px 0px',
                            padding: '8px'
                        }}>
                            <label htmlFor={'fp' + index} className="fs-14 clr-navy" style={{
                                marginRight: 10,
                                padding: '5px',

                                background: 'white',
                                border: '1px solid cornflowerblue',
                                borderRadius: '10px',

                            }} >
                                <i style={{ fontSize: 15, margin: 5 }} className="pi pi-folder-open clr-navy" />
                                Add Attachment
                            </label>
                            <label
                                onClick={() => { resetFiles(item, index) }}
                                style={{
                                    padding: '5px',
                                    fontSize: '15px',
                                    border: '1px solid indianred',
                                    background: 'white',
                                    borderRadius: '10px',
                                    color: 'indianred'
                                }} >
                                <i style={{ fontSize: 15, margin: 5 }} className="pi pi-undo" />
                                Reset
                            </label>
                            <input type='file' accept=".jpg,.JPG,.jpeg,.JPEG,.png,.PNG,.pdf,.PDF" id={'fp' + index} hidden onChange={(e) => { handleFileUpload(e, item) }} ></input>
                        </div>
                        {item.value && item.value.length !== 0 &&
                            <div className="col-12" style={{
                                maxHeight: 300,
                                overflow: 'auto',
                                border: '1px solid #ced4da'
                            }} >
                                <div style={{
                                    border: '1px solid #6366F170',
                                    borderRadius: '10px'
                                }}>
                                    {item.value.map((file, findex) => {

                                        return (
                                            <>
                                                <div style={{
                                                    display: 'flex',
                                                    alignItems: 'center', margin: 5
                                                }} >
                                                    <div className="flex align-items-center " style={{ width: '60%' }}>
                                                        {(file.extension === '.pdf' || file.extension === '.PDF') ?
                                                            <div>
                                                                <iframe src={API.Docs + file.originalname} /> </div> :
                                                            <img alt={file.originalname} role="presentation" src={API.Docs + file.originalname} width={100} style={{ borderRadius: 10 }} />}
                                                        <span className="flex flex-column text-left ml-3">
                                                            {file.originalname}
                                                            <small>{new Date().toLocaleDateString()}</small>
                                                        </span>
                                                    </div>
                                                    <Tag value={'View'} onClick={() => { window.open(API.Docs + file.originalname) }} style={{ width: '20%' }} severity="warning" className="px-3 py-2" />
                                                    <Button type="button" icon="pi pi-times" style={{ marginRight: 10 }} className="p-button-outlined p-button-rounded p-button-danger ml-auto" onClick={() => removeImage(index, findex)} />
                                                </div>

                                            </>
                                        )
                                    })

                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            )
        }

    }
    const handleFileUpload = (e, item) => {
        let ext = e.target.files[0].name.substr(e.target.files[0].name.lastIndexOf('.'))
        let allowedext = ['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.pdf', '.PDF']
        if (allowedext.includes(ext)) {
            let formData = new FormData()
            formData.append('file', e.target.files[0])
            APIServices.post(API.FilesUpload, formData, {
                headers: {
                    'content-type': 'multipart/form-data'

                }
            }).then((res) => {
                res.data.files[0].extension = ext
                if (item.value === undefined) {
                    item['value'] = [res.data.files[0]]
                } else {
                    if (item.multiple) {
                        item['value'].push(res.data.files[0])
                    } else {
                        item['value'] = [res.data.files[0]]
                    }

                }
                forceUpdate()

            })
        } else {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "invalid file format, supported format JPEG,PNG & PDF only",
                showConfirmButton: false,
                timer: 2000,
            });
        }
    }
    const resetFiles = (item, index) => {

        item.value = []
        forceUpdate()

    }
    const removeImage = (index, findex) => {

        data.data1[index].value.splice(findex, 1)
        forceUpdate()

    }

    const checkResponse = () => {
        let result = 0
        let total = data.data1.filter((i) => { return i.required === true }).length

        data.data1.forEach((item) => {


            if (item.type === 'checkbox-group' && item.required === true) {


                if (item.values.filter((i) => { return i.selected }).length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            } else if (item.type === 'date' && item.required) {

                if (item.value && item.value !== null) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'number' && item.required) {

                if (item.value && parseFloat(item.value.toString()) >= 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            } else if (item.type === 'number' && item.required === false) {
                if (item.value) {
                    if (item.value === null || isNaN(item.value)) {
                        result = result + 1
                        item.error = 1
                    } else if (parseFloat(item.value.toString()) < 0) {
                        result = result + 1
                        item.error = 1
                    } else if (parseFloat(item.value.toString()) >= 0) {
                        item.error = 0
                    }
                }
            } else if (item.type === 'radio-group' && item.required) {

                if (item.values.filter((i) => { return i.selected }).length === 1) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'select' && item.required) {

                if (item.values.filter((i) => { return i.selected }).length === 1) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'text' && item.required) {

                if (item.value && item.value.trim().length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'textarea' && item.required) {

                if (item.value && item.value.trim().length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'file' && item.required) {
                if (item.value && item.value.length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }


        })

        return result === total
    }

    const checkResponse_ = () => {
        console.log(data)
        if (data.data1.length === 0) {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Data set is Empty, requires minimum 1 record to submit/draft",
                showConfirmButton: false,
                timer: 1500,
            });
            return false
        } else {

            return true
        }
    }
    const UploadToExport = async (data1) => {
        const allowedTypes = [
            "checkbox-group",
            "radio-group",
            "select",
            "text",
            "number",
            "date",
            "textarea"
        ];

        const rows = [];
        const dropdownCells = [];

        data1.forEach((item) => {
            if (!allowedTypes.includes(item.type)) return;

            const datapointId = item.name || "";
            const question = item.label?.replace(/&nbsp;/g, " ").trim() || item.type;

            let response = "";
            let dropdownOptions = [];

            if (["checkbox-group", "radio-group", "select"].includes(item.type)) {
                const selected = item.values?.filter(v => v.selected);
                response = selected?.map(v => v.label).join(", ") || "";
                dropdownOptions = item.values?.map(v => v.label).filter(Boolean);
            } else if (item.type === "date") {
                const dateObj = new Date(item.value);
                response = item.value && !isNaN(dateObj) ? item.value : "";
            } else if (item.type === "number") {
                response = item.value !== undefined && item.value !== null ? String(item.value) : "";
            } else {
                response = item.value || "";
            }

            rows.push([datapointId, question, response]);

            if (dropdownOptions.length) {
                dropdownCells.push({
                    row: rows.length + 2, // starts at row 3, header in row 2
                    options: dropdownOptions
                });
            }
        });

        // Create workbook
        const workbook = await XlsxPopulate.fromBlankAsync();

        // === Guidance Sheet ===
        const guidanceSheet = workbook.sheet(0).name("Guidance");
        const guidanceHeader = ["Instruction Sheet"];
        guidanceHeader.forEach((val, colIndex) => {
            guidanceSheet.cell(1, colIndex + 1).value(val);
        });
 

        // === Data Entry Sheet ===
        const dataEntrySheet = workbook.addSheet("Data Entry");
        const dataHeader = ["Datapoint Id", "Question", "Response"];
        dataHeader.forEach((val, colIndex) => {
            dataEntrySheet.cell(2, colIndex + 2).value(val); // B2, C2, D2
        });

        rows.forEach((row, rowIndex) => {
            row.forEach((val, colIndex) => {
                dataEntrySheet.cell(rowIndex + 3, colIndex + 2).value(val); // B3, C3, ...
            });
        });

        // Add dropdowns to Response column (D)
        dropdownCells.forEach(({ row, options }) => {
            dataEntrySheet
                .cell(`D${row}`)
                .dataValidation({
                    type: "list",
                    allowBlank: true,
                    showInputMessage: true,
                    showErrorMessage: true,
                    errorTitle: "Invalid Input",
                    error: "Please select a valid option.",
                    formula1: `"${options.join(",")}"`,
                });
        });

        // Remove default sheet if more than 1
        if (workbook.sheets().length > 2) {
            workbook.deleteSheet("Sheet1");
        }

        const blob = await workbook.outputAsync();
        const url = URL.createObjectURL(new Blob([blob]));
        const a = document.createElement("a");
        a.href = url;
        a.download = data.id+"_"+data.title+"_template.xlsx";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };



    async function UploadFromImport(e, data, setData) {
        const file = e.files[0];
        if (!file) return;
        const workbook = await XlsxPopulate.fromDataAsync(await file.arrayBuffer());


        const sheet = workbook.sheet("Data Entry");
        if (!sheet) {
            alert(" 'Data Entry' sheet not found in uploaded file.");
            return;
        }

        const rows = sheet.usedRange().value();
        const updatedData = [...data.data1];

        function parseValue(value, type = 1, accept = 3) {
            if (
                value === null ||
                value === undefined ||
                (!isFinite(value) && typeof value !== 'string') ||
                Number.isNaN(value)
            ) {
                return { valid: true, value: null };
            }

            let cleanedValue = typeof value === 'string' ? value.replace(/[^0-9.-]/g, '') : value;
            let result = type === 2 ? Number(cleanedValue) : parseFloat(cleanedValue);

            if (Number.isNaN(result) || !isFinite(result)) return { valid: false, value: result };
            if (
                typeof value === 'string' &&
                !/^[0-9]+(?:,[0-9]+)*(?:\.[0-9]+)?$/.test(value)
            )
                return { valid: false, value: result };

            if (type === 2 && !Number.isInteger(result)) return { valid: false, value: result };
            if (accept === 3 && result < 0) return { valid: false, value: result };

            let valid = accept === 1 ? true : accept === 2 ? result > 0 : true;
            return { valid, value: result };
        }

        function validateAndConvertDate(input, format = 'dd/MM/yyyy') {
            if (input === null || input === undefined || String(input).trim() === '') {
                return {
                    valid: true,
                    utcDate: null,
                };
            }

            const numeric = Number(input);
            if (!isNaN(numeric) && numeric > 0) {
                const excelEpoch = DateTime.fromObject({ year: 1899, month: 12, day: 30 });
                const utcDate = excelEpoch.plus({ days: numeric }).toUTC();
                return {
                    valid: true,
                    utcDate: utcDate.toISO(),
                };
            }

            const parsed = DateTime.fromFormat(input.trim(), format, { zone: 'utc' });
            if (parsed.isValid) {
                return {
                    valid: true,
                    utcDate: parsed.toUTC().toISO(),
                };
            }

            return {
                valid: false,
                utcDate: null,
            };
        }

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const datapointId = row[0];
            const response = row[2];
            if (!datapointId) continue;

            const item = updatedData.find((q) => q.name === datapointId);
            if (!item) continue;

            const type = item.type;

            if (["radio-group", "select", "checkbox-group"].includes(type)) {
                const selected = response?.trim();
                const validOption = item.values.find((v) => v.label === selected);
                item.values.forEach((v) => (v.selected = false));
                if (validOption) validOption.selected = true;
                if (type === "select" && validOption) {
                    item.value = validOption.value;
                }
            } else if (type === "number") {
                const parsed = parseValue(response, 1, 3);
                item.value = parsed.valid ? parsed.value : null;
            } else if (type === "date") {
                const result = validateAndConvertDate(response, "d/M/yyyy");
                item.value = result.valid ? result.utcDate : null;
            } else if (["text", "textarea"].includes(type)) {
                item.value = response?.toString().trim() || null;
            }
        }

        setData({ ...data, data1: updatedData });
e.options.clear()
        console.log(" Imported Data Entry values:", e);
    }

    const checkHardcoded = () => {
        if (hardcoded.dcf.includes(id) || hardcoded.dcf2.includes(id)) {
            return true
        } else {
            return false
        }

    }
    return (
        <div className="grid" style={{ margin: 10 }}>
            <div className="col-12" >
                {(admin_data.id !== undefined && data.length !== 0) ?
                    <div >

                        <div className="fs-20 fw-7 clr-gray-900">
                            <h4><span className="mr-2">{'DCF ' + id}</span> {data.title}</h4>
                        </div>


                        {data?.comments?.trim().length !== 0 &&
                            <div className="p-card fw-5 fs-16" style={{ padding: 10, marginBottom: 20 }}>
                                <span style={{ fontWeight: 'bold' }}>Note :&nbsp;</span> <div dangerouslySetInnerHTML={{ __html: data?.comments || null }} />
                            </div>}

                        {data?.data1?.length !== 0 && !checkHardcoded() ?
                            <div>
                                <div className="bg-white" style={{ borderRadius: 4 }}>
                                    <div style={{ display: 'flex', justifyContent: 'flex-end', padding: 24, borderBottom: '1px solid #E0E0E0' }} >
                                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                                            <div className="flex fs-16" style={{ flexDirection: 'row' }}><span className="clr-gray-3 fw-4">Version :&nbsp;</span> <span className="clr-gray-900 fw-7">{moment(data.updated).local().format('DD MMM YYYY, hh:mm A')}</span>  </div>
                                        </div>

                                    </div>
                                    <div style={{ padding: 24 }}>
                                        <div className="col-12" style={{ display: "flex", gap: "12px", alignItems: "center" }}>
                                            <Button
                                                label="Export"
                                                icon="pi pi-download"
                                                className="navy-button"
                                                style={{
                                                    backgroundColor: "#004a7c",
                                                    border: "none",
                                                    color: "#fff",
                                                    fontWeight: "bold",
                                                    padding: "0.75rem 1.25rem",
                                                    fontSize: "14px",
                                                    borderRadius: "6px",
                                                    display: "flex",
                                                    alignItems: "center",
                                                    gap: "8px"
                                                }}
                                                onClick={() => UploadToExport(data.data1)}
                                            />

                                            

                                                <FileUpload
                                                    chooseOptions={{
                                                        label: "Import",
                                                        icon: "pi pi-file-excel",
                                                        className: "navy-button",
                                                    }}
                                                    mode="basic"
                                                    name="demo[]"
                                                    auto
                                                    customUpload
                                                    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                                                    className="mr-2"
                                                    uploadHandler={(e) => {
                                                        UploadFromImport(e, data, setData);
                                                    }}
                                                />



                                        </div>


                                        {data.data1.map((item, index) => {

                                            return renderItems(item, index)
                                        })

                                        }
                                    </div>



                                </div>
                                <div style={{ justifyContent: 'flex-end', display: 'flex', marginTop: 20 }}>
                                    <Button label='Close' className="ml-4" onClick={() => { window.close() }} text ></Button>


                                </div>
                            </div>
                            :
                            (id === '10' || id === '305') ?
                                <div>
                                    <Fugitive_ standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                    </div>
                                </div>
                                : (id === '11' || id === '304') ?
                                    <div>
                                        <Stationary_Combustion_ standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                        </div>
                                    </div>
                                    :
                                    (id === '36') ?
                                        <div>
                                            <Business_Travel_ standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                            </div>
                                        </div>
                                        :
                                        (id === '15') ?
                                            <div>
                                                <Mobile_Combustion_ standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                </div>
                                            </div>

                                            :
                                            (id === '16') ?
                                                <div>
                                                    <Purchase_Goods_Services_ standard={12} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                        <Button label='Close' onClick={() => { window.close() }}></Button>
                                                    </div>
                                                </div>
                                                : id === '188' ?
                                                    <div>
                                                        <BP8EQ2 data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                        </div>
                                                    </div>
                                                    : id === '195' ?
                                                        <div>
                                                            <BP2LQ3 data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                            </div>
                                                        </div>
                                                        : id === '196' ?
                                                            <div>
                                                                <BP2LQ5 data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                </div>
                                                            </div>
                                                            : id === '245' ?
                                                                <div>
                                                                    <GR2LQ1 data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                    </div>
                                                                </div>
                                                                : id === '246' ?
                                                                    <div>
                                                                        <GR2LQ2 data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                        </div>
                                                                    </div>
                                                                    : id === '247' ?
                                                                        <div>
                                                                            <GR2LQ3 data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                            </div>
                                                                        </div>
                                                                        : id === '254' ?
                                                                            <div>
                                                                                <HotelStay standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                </div>
                                                                            </div>
                                                                            : id === '257' ?
                                                                                <div>
                                                                                    <Electricity standard={25} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                    </div>
                                                                                </div>
                                                                                : id === '262' ?
                                                                                    <div>
                                                                                        <Employee_Category data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                        </div>
                                                                                    </div> : id === '263' ?
                                                                                        <div>
                                                                                            <Employee_Demographics_263 data={data.data1} isValidResponse={(e) => { console.log(e) }} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                            </div>
                                                                                        </div> : id === '275' ?
                                                                                            <div>
                                                                                                <Employee_Category_Diversity_STT data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                </div>
                                                                                            </div> : id === '277' ?
                                                                                                <div>
                                                                                                    <Total_No_of_New_Employee_STT data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                    </div>
                                                                                                </div> : id === '278' ?
                                                                                                    <div>
                                                                                                        <Total_No_of_Employee_Left_STT data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                        </div>
                                                                                                    </div>
                                                                                                    : id === '282' ?
                                                                                                        <div>
                                                                                                            <Capital_Goods allowedCategories={[]} standard={12} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                            </div>
                                                                                                        </div>
                                                                                                        : id === '283' ?
                                                                                                            <div>
                                                                                                                <Scope3_Investments data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                </div>
                                                                                                            </div> :
                                                                                                            id === '284' ?
                                                                                                                <div>
                                                                                                                    <Total_No_of_Employee_Hire_TurnOver_STT data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                    </div>
                                                                                                                </div> : id === '285' ?
                                                                                                                    <div>
                                                                                                                        <Water_Withdrawl_STT data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                        </div>
                                                                                                                    </div> : id === '286' ?
                                                                                                                        <div>
                                                                                                                            <Water_Disposal_STT data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                            </div>
                                                                                                                        </div> : id === '287' ?
                                                                                                                            <div>
                                                                                                                                <Electricity_STT standard={26} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                </div>
                                                                                                                            </div> : id === '292' ?
                                                                                                                                <div>
                                                                                                                                    <Upstream_Trans_Dist standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                    </div>
                                                                                                                                </div> : id === '293' ?
                                                                                                                                    <div>
                                                                                                                                        <Downstream_Trans_Dist standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                        </div>
                                                                                                                                    </div> : id === '294' ?
                                                                                                                                        <div>
                                                                                                                                            <ParentalLeave_STT standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                            </div>
                                                                                                                                        </div> : id === '295' ?
                                                                                                                                            <div>
                                                                                                                                                <Employee_Training_Hours_STT standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                </div>
                                                                                                                                            </div> : id === '296' ?
                                                                                                                                                <div>
                                                                                                                                                    <Performance_Career_Development_STT standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                    </div>
                                                                                                                                                </div> : id === '297' ?
                                                                                                                                                    <div>
                                                                                                                                                        <Hazardous_Waste_Disposal_STT standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                        </div>
                                                                                                                                                    </div> : id === '298' ?
                                                                                                                                                        <div>
                                                                                                                                                            <Proportion_Spending_Local_Suppliers_STT standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                            </div>
                                                                                                                                                        </div> : id === '299' ?
                                                                                                                                                            <div>
                                                                                                                                                                <Social_Impact_Programmes standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                </div>
                                                                                                                                                            </div> :
                                                                                                                                                            id === '300' ?
                                                                                                                                                                <div>
                                                                                                                                                                    <Business_Travel_Air isValidResponse={(e) => { console.log(e) }} standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                    </div>
                                                                                                                                                                </div> :
                                                                                                                                                                id === '301' ?
                                                                                                                                                                    <div>
                                                                                                                                                                        <Business_Travel_Land isValidResponse={(e) => { console.log(e) }} standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                        </div>
                                                                                                                                                                    </div> :
                                                                                                                                                                    id === '302' ?
                                                                                                                                                                        <div>
                                                                                                                                                                            <Business_Travel_Rail isValidResponse={(e) => { console.log(e) }} standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                            </div>
                                                                                                                                                                        </div> :
                                                                                                                                                                        id === '307' ?
                                                                                                                                                                            <div>
                                                                                                                                                                                <NonHazardous_Waste_Disposal_STT isValidResponse={(e) => { console.log(e) }} standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                </div>
                                                                                                                                                                            </div> :
                                                                                                                                                                            id === '310' ?
                                                                                                                                                                                <div>
                                                                                                                                                                                    <Stationary_Combustion_Rotary standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                    </div>
                                                                                                                                                                                </div> :
                                                                                                                                                                                id === '311' ?
                                                                                                                                                                                    <div>
                                                                                                                                                                                        <Mobile_Combustion_Rotary standard={1} data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                        </div>
                                                                                                                                                                                    </div> :
                                                                                                                                                                                    id === '316' ?
                                                                                                                                                                                        <div>
                                                                                                                                                                                            <Hazardous_NonHazardous_Rotary data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                            </div>
                                                                                                                                                                                        </div> : id === '355' ?
                                                                                                                                                                                            <div>
                                                                                                                                                                                                <VehicleInformation data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>
                                                                                                                                                                                            </div> : id === '356' ?
                                                                                                                                                                                                <div>
                                                                                                                                                                                                    <VehicleSold data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>
                                                                                                                                                                                                </div> : id === '357' ?
                                                                                                                                                                                                    <div>
                                                                                                                                                                                                        <DistanceTravelledVehicle data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>
                                                                                                                                                                                                    </div> : id === '358' ?
                                                                                                                                                                                                        <div>
                                                                                                                                                                                                            <SoldProduct data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>
                                                                                                                                                                                                        </div> : id === '359' ?
                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                <Employee_Diversity_TVS data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                </div>
                                                                                                                                                                                                            </div> : id === '382' ?
                                                                                                                                                                                                                <div>
                                                                                                                                                                                                                    <Emissions_Due_Downstream_TransportationAndDistribution_SpendBased data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                </div> : id === '383' ?
                                                                                                                                                                                                                    <div>
                                                                                                                                                                                                                        <HazardousWaste_TVS_ data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                    </div> : id === '384' ?
                                                                                                                                                                                                                        <div>
                                                                                                                                                                                                                            <New_Employee_Hires_TVS data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                        </div> : id === '385' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <New_Employee_Turnover_TVS data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div> : id === '387' ?
                                                                                                                                                                                                                                <div>
                                                                                                                                                                                                                                    <Employee_Commutte_TVS data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                    <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                        <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                </div> : id === '388' ?
                                                                                                                                                                                                                                    <div>
                                                                                                                                                                                                                                        <Upstream_Leased_Assets_Electricity data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                        <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                            <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                    </div> : id === '389' ?
                                                                                                                                                                                                                                        <div>
                                                                                                                                                                                                                                            <Upstream_Leased_Assets_Stationary data={data.data1} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                            <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                                <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                        </div> :   id === '378' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <Employees_TQC data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div>  : id === '367' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <Blue_Collar_Manufacturing_employees data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div>  : id === '381' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <OHC_Training data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div>  : id === '368' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <Leadership_B3 data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div>  : id === '365' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <White_Collar_Manufacturing_Employees data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div> :  id === '376' ?
                                                                                                                                                                                                                            <div>
                                                                                                                                                                                                                                <Employees_Data_Privacy data={data.data1} edit={1} isValidResponse={(e) => { console.log(e) }} setData={() => { forceUpdate() }} getData={(e) => { data.data1 = e; }} />
                                                                                                                                                                                                                                <div style={{ justifyContent: 'space-between', display: 'flex', marginTop: 20 }}>
                                                                                                                                                                                                                                    <Button label='Close' onClick={() => { window.close() }}></Button>

                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                            </div> :
                                                                                                                                                                                                                                        'Contact admin'
                        }




                    </div>
                    :
                    <div className="col-12 card">Form not found</div>
                    // <div className="col-12 card">You have no rights to access this page </div>

                }
            </div>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(DCFInputEntryPreview, comparisonFn);
