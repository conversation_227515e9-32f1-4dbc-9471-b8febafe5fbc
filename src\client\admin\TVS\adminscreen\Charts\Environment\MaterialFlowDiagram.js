import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
import { sankey as d3Sankey, sankeyLinkHorizontal } from "d3-sankey";

const MaterialFlowDiagram = () => {
  const svgRef = useRef();

  useEffect(() => {
    const width = 800;
    const height = 400;

    // Data for Sankey diagram
    const data = {
      nodes: [
        { id: "Input" },
        { id: "Processing" },
        { id: "Waste" },
        { id: "Output" },
        { id: "Loss" },
      ],
      links: [
        { source: "Input", target: "Processing", value: 150 },
        { source: "Input", target: "Waste", value: 50 },
        { source: "Processing", target: "Output", value: 120 },
        { source: "Processing", target: "Loss", value: 30 },
      ],
    };

    // Remove existing SVG content
    d3.select(svgRef.current).selectAll("*").remove();

    // Create the SVG container
    const svg = d3
      .select(svgRef.current)
      .attr("width", width)
      .attr("height", height);

    // Create the Sankey generator
    const sankey = d3Sankey()
      .nodeId((d) => d.id)
      .nodeWidth(10) // Reduce the node width for thinner bars
      .nodePadding(20) // Increase padding for better spacing between nodes
      .extent([
        [0, 0],
        [width, height],
      ]);

    // Compute node and link positions
    const { nodes, links } = sankey({
      nodes: data.nodes.map((d) => ({ ...d })),
      links: data.links.map((d) => ({ ...d })),
    });

    // Render links (flows)
    svg
      .append("g")
      .selectAll("path")
      .data(links)
      .join("path")
      .attr("d", sankeyLinkHorizontal())
      .attr("stroke", (d, i) => d3.schemeCategory10[i % 10]) // Color coding for links
      .attr("fill", "none")
      .attr("stroke-width", (d) => Math.max(1, d.width))
      .attr("opacity", 0.8)
      .append("title")
      .text((d) => `${d.source.id} → ${d.target.id}: ${d.value}`);

    // Add flow labels on links
    svg
      .append("g")
      .selectAll("text")
      .data(links)
      .join("text")
      .attr("x", (d) => (d.source.x1 + d.target.x0) / 2)
      .attr(
        "y",
        (d) => (d.source.y0 + d.source.y1 + d.target.y0 + d.target.y1) / 4
      )
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("fill", "#000")
      .text((d) => d.value);

    // Render nodes
    svg
      .append("g")
      .selectAll("rect")
      .data(nodes)
      .join("rect")
      .attr("x", (d) => d.x0)
      .attr("y", (d) => d.y0)
      .attr("width", (d) => d.x1 - d.x0)
      .attr("height", (d) => d.y1 - d.y0)
      .attr("fill", (d, i) => d3.schemeCategory10[i % 10]) // Color coding for nodes
      .append("title")
      .text((d) => `${d.id}: ${d.value || "N/A"}`);

    // Add node labels
    svg
      .append("g")
      .selectAll("text")
      .data(nodes)
      .join("text")
      .attr("x", (d) => d.x0 - 10)
      .attr("y", (d) => (d.y0 + d.y1) / 2)
      .attr("text-anchor", "end")
      .attr("font-size", "14px")
      .attr("fill", "#000")
      .text((d) => d.id);

    // Add output labels for nodes on the right
    svg
      .append("g")
      .selectAll(".output-label")
      .data(nodes)
      .join("text")
      .filter((d) => d.x1 === width)
      .attr("x", (d) => d.x1 + 10)
      .attr("y", (d) => (d.y0 + d.y1) / 2)
      .attr("text-anchor", "start")
      .attr("font-size", "14px")
      .attr("fill", "#000")
      .text((d) => d.id);
  }, []);

  return (
    <>
      <h3 style={{ fontSize: "18px" }}>Material Flow Diagram</h3>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <svg ref={svgRef} />
      </div>
    </>
  );
};

export default MaterialFlowDiagram;
