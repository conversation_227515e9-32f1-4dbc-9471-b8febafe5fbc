// SupplierReport.js
import React, { useState, forwardRef, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import {
  RadarChart, PolarGrid, PolarAngleAxis, Radar,
  CartesianGrid, ResponsiveContainer, BarChart, Bar,
  XAxis, YAxis, <PERSON>ltip, Legend
} from 'recharts';
import { Card, Table } from "react-bootstrap";
import { useReactToPrint } from 'react-to-print';
import { DateTime } from 'luxon';
import { groupArrayByKeys } from '../../../../components/BGHF/helper';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
// import SubGraph1Demo from './reportGraph/SubGraph1Demo';
// import SubGraph3Demo from './reportGraph/SubGraph3Demo';
// import SubGraph4Demo from './reportGraph/SubGraph4Demo';
// import SubGraph5Demo from './reportGraph/SubGraph5Demo';
import ReactToPrint from 'react-to-print';
import { Button } from 'primereact/button';
import ReportSection from '../SupplierScreen/reportSection';
import './EnvironmentReport.css'
// import ESGGScores from '../adminscreen/DealersDashboard/ESGGScores';
import ESGScores from './DealerChat/ESGScores';
import ESG_Sales_Service from './DealerChat/ESG_Sales_Service';
import EnvironmentChart from './DealerChat/SalesServiceEnv';
import SocialChart from './DealerChat/SalesServiceSoc';
import GovernanceChart from './DealerChat/SalesServiceGov';
import GeneralChart from './DealerChat/SalesServiceGen';

// Import Tab components for Area Office
import ESGScoresTab from '../adminscreen/DealersDashboard/TabGraphs/ESGScoresTab';
import EnvironmentTab from '../adminscreen/DealersDashboard/TabGraphs/EnvironmentTab';
import SocialTab from '../adminscreen/DealersDashboard/TabGraphs/SocialTab';
import GovernanceTab from '../adminscreen/DealersDashboard/TabGraphs/GovernanceTab';


const DealerCompletedReport = forwardRef((props, ref) => {



  const [actions, setActions] = useState([])


  const contentRef = useRef(null)
  const [data, setData] = useState(props.report)
  const [chart, setChart] = useState([])
  const categoryOptions = [
    { label: 'Good Practices', id: 1 },
    { label: 'Opportunity of Improvement', id: 2 },
    { label: 'Non-compliance', id: 3 },
  ];

  const nonComplianceOptions = [
    { label: 'Regulatory (Major)', id: 1 },
    { label: 'Regulatory (Minor)', id: 2 },
    { label: 'Minor', id: 3 },
  ];
  const [report, setReport] = useState({})
  useEffect(() => {
    if (contentRef.current) {
      console.log("Div element:", contentRef.current);
    }
  }, []);

  const stripHTML = (html) => {
    if (!html) return "Unnamed Section"; // Default name if label is empty
    const doc = new DOMParser().parseFromString(html, "text/html");
    return doc.body.textContent || "Unnamed Section";
  };


  useEffect(() => {
    // let loc = JSON.parse(JSON.stringify(data))

    APIServices.get(API.DealerAuditorSubmission_Edit_Response(props?.report?.dealerAuditorChecklistSubmission?.id)).then(res => {
      if (res.data.category === "Area Office") {
        console.log(res.data, ' Area Office Data ')
        const formatted = flattenAreaOfficeData(res.data);
        console.log(formatted, ' FOrmatted Data ')
        setChart(formatted);
      } else if (res.data.category === "Authorized Parts Stockist (APS)") {
        const formatted = flattenAPSData(res.data);
        setChart(formatted);
      } else {
        setChart(res.data);
      }
    })
    APIServices.get(API.DealerAuditorSubmission_Edit(props?.report?.dealerAuditorChecklistSubmission?.id)).then(res => {

      // setReport(res.data)





      const groupedData = JSON.parse(res.data?.response).reduce((acc, item) => {
        if (!item.criteria) return acc; // Skip items without a valid criteria

        const criteriaKey = item.criteria; // Group by criteria
        const cleanLabel = stripHTML(item.label); // Remove HTML from section label

        if (!acc[criteriaKey]) {
          acc[criteriaKey] = {};
        }

        if (!acc[criteriaKey][cleanLabel]) {
          acc[criteriaKey][cleanLabel] = {};
        }

        // If checklist-group, group questions inside it and include `section`
        if (item.type === "checklist-group" && item.questions) {
          item.questions.forEach((question) => {
            const questionLabel = stripHTML(question.label);

            if (!acc[criteriaKey][cleanLabel][questionLabel]) {
              acc[criteriaKey][cleanLabel][questionLabel] = {
                section: item.section || "No Section", // Include section key from checklist-group
                questions: [],
              };
            }

            // Add question data including section
            acc[criteriaKey][cleanLabel][questionLabel].questions.push({
              ...question,
              section: item.section || "No Section", // Ensure each question carries its section
            });
          });
        }

        return acc;
      }, {});
      setActions(JSON.parse(res.data?.response))




    })

  }, [data])

  function flattenData(data) {
    if (data.category === "Area Office") return flattenAreaOfficeData(data);
    if (data.category === "Authorized Parts Stockist (APS)") return flattenAPSData(data);
    return data;
  }

  function flattenAPSData(data) {
    if (data.category !== "Authorized Parts Stockist (APS)") return data;

    const environment = data.sales_criteria?.environment?.subCriteria || [];
    const social = data.sales_criteria?.social?.subCriteria || [];
    const governance = data.sales_criteria?.governance?.subCriteria || [];

    const getScore = (arr, name) =>
      arr.find((item) => item.name === name)?.score || 0;

    return [{
      entity_id: 1, // Change as needed
      wk_no: 34, // Or generate dynamically
      category: data.category,
      date_of_calibration:
        new Date(data.date_of_calibration).getTime() / 86400000 + 25569,
      zone: data.zone,
      area_office: data.area_office,
      assessor: "", // Add from source if available
      city: data.city,
      branch_code: data.branch_code,
      dealer_name: data.dealer_name,
      environment: getScore(environment, "statutory_compliance"),
      water_management: getScore(environment, "water_management"),
      waste_management: getScore(environment, "waste_management"),
      energy_management: getScore(environment, "energy_management"),
      road_safety: getScore(social, "road_safety"),
      electrical_safety: getScore(social, "electrical_safety"),
      fire_safety: getScore(social, "fire_safety"),
      "5s_and_house_keeping": getScore(social, "5S"),
      personnel_safety: getScore(social, "personnel_safety"),
      governance_framework: getScore(governance, "governance_framework"),
      total_score: data.total_score,
      grade: data.grade,
      environment_avg: data.sales_criteria?.environment?.summary_score || 0,
      social_avg: data.sales_criteria?.social?.summary_score || 0,
      gove_avg: data.sales_criteria?.governance?.summary_score || 0,
    }];
  }

  function flattenAreaOfficeData(data) {
    if (data.category !== "Area Office" && data.category !== "Authorized Parts Stockist (APS)") return data;

    const environment = data.sales_criteria?.environment?.subCriteria || [];
    const social = data.sales_criteria?.social?.subCriteria || [];
    const governance = data.sales_criteria?.governance?.subCriteria || [];

    

    const getScore = (arr, name) =>
      arr.find((item) => item.name === name)?.score || 0;

    return [{
      entity_id: 1, // Static or generated
      wk_no: 33, // You can set this dynamically if needed
      category: data.category,
      date_of_calibration: new Date(data.date_of_calibration).getTime() / 86400000 + 25569, // Convert to Excel-style date number
      zone: data.zone,
      assessors: "", // If available in source, insert it here
      city: data.city,
      branch_code: data.branch_code,
      area_office: data.area_office,
      environment: getScore(environment, "statutory_compliance"),
      water_management: getScore(environment, "water_management"),
      waste_management: getScore(environment, "waste_management"),
      energy_management: getScore(environment, "energy_management"),
      road_safety: getScore(social, "road_safety"),
      electrical_safety: getScore(social, "electrical_safety"),
      fire_safety: getScore(social, "fire_safety"),
      "5s_and_house_keeping": getScore(social, "5S"),
      personnel_safety: getScore(social, "personnel_safety"),
      governance_framework: getScore(governance, "governance_framework"),
      total_score: data.total_score,
      grade: data.grade,
      environment_avg: data.sales_criteria?.environment?.summary_score || 0,
      social_avg: data.sales_criteria?.social?.summary_score || 0,
      gove_avg: data.sales_criteria?.governance?.summary_score || 0,
    }];
  }


  const getActionId = (item) => {
    if (item.categoryOfFinding === 1) {
      return 'GP' + item.id
    } else if (item.categoryOfFinding === 2) {
      return 'OFI' + item.id
    } else if (item.categoryOfFinding === 3) {
      if (item.nonComplianceType === 1) {
        return 'NCR' + item.id + ' (Major)'
      } else if (item.nonComplianceType === 2) {
        return 'NCR' + item.id + ' (Minor)'
      } else if (item.nonComplianceType === 3) {
        return 'NC' + item.id + ' (Major)'
      }
    }
  }
  const getMSIIDByAssignment = (item) => {

    return 'MSI-' + item?.vendor?.code + '-' + DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')
  }

  const getCalibrationTeamMember = () => {
    if (data?.dealerAuditorChecklistSubmission) {
      let findId = data?.dealerAuditorChecklistSubmission?.modified_by || data?.dealerAuditorChecklistSubmission?.created_by || null;
      return assessorList.find(i => i.id == findId)?.information?.empname || 'Not Assigned';
    } else {
      return 'Not Assigned';
    }
  }
  const getDate = (date, format) => {
    if (!date) {
      return 'Not Set'
    }
    if (typeof date === 'string') {
      return DateTime.fromISO(date, { zone: 'utc' }).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
    } else if (DateTime.isDateTime(date)) {
      return date.toFormat(format ? format : 'dd-MM-yyyy')
    } else {
      return DateTime.fromJSDate(date).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
    }

  };
  const categoryList = [{ name: 'Forging & Machining', value: 1 }, { name: 'Casting & Machining', value: 2 }, { name: 'Pressing & Fabrication', value: 3 }, { name: 'Proprietary Mechanical', value: 4 }, { name: 'Proprietary Electrical', value: 5 }, { name: 'Plastics, Rubber, Painting and Stickers', value: 6 }, { name: 'EV/3W/2W', value: 7 }, { name: 'BW', value: 8 }, { name: 'Accessories', value: 9 }]

  const DealerZone = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]


  const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]


  const admin_data = useSelector(state => state.user.admindetail);
  const userList = useSelector(state => state.userlist.userList);
  const tvsExtUserList = useSelector(state => state.userlist.tvsExtUserList);

  // Combine user lists to create assessor list
  const assessorList = [...(tvsExtUserList || []), ...(userList || [])];

  const reactToPrintFn = useReactToPrint({ contentRef });


  const overallScore = data?.dealerAuditorChecklistSubmission?.score
    ? JSON.parse(data.dealerAuditorChecklistSubmission.score).overallScore
    : null;

  const getMSIRankImage = () => {
    if (overallScore == null) return "NA";
    if (overallScore >= 85) return require('../../../../assets/images/report/valuechain/platinum_rating.png').default;
    if (overallScore > 70) return require('../../../../assets/images/report/valuechain/gold_rating.png').default;
    if (overallScore > 55) return require('../../../../assets/images/report/valuechain/silver_rating.png').default;
    return "Not Met";
  };

  const rankImage = getMSIRankImage();
  return (
    <>
      <div className='col-12 flex justify-content-end'>
        <Button label='Print' icon='pi pi-print' onClick={() => { reactToPrintFn() }} l></Button>

      </div>
      <div ref={contentRef} style={{ width: "100%" }}>
        {/* PAGE 1 */}

        <div className="page text-white min-h-screen flex-col items-center justify-center mb-5" style={{ background: '#00634F' }}>
          <header style={{ marginBottom: "40px", textAlign: "right" }} className='p-4 bg-white'>
            <img
              src={"https://api.eisqr.com/docs/1738227606762tvsm_logo.jpg"} // Replace with the actual path if needed
              alt="TVS Logo"
              style={{ maxWidth: 150 }}
            />
          </header>
          <div className="flex flex-col flex-1">
            <div className="p-10 mt-12" style={{ marginTop: 250 }}>
              <div className="mb-8">
                <img
                  // Update path as needed
                  src={require('../../../../assets/images/test.png').default}
                  alt="TVS Logo"
                  style={{ maxWidth: 250, marginLeft: '-40px' }}
                />
              </div>
              <div className="p-5">
                <h1 className="text-4xl font-bold mb-2 ">MY SUSTAINABILITY INDEX
                  FOR DEALERS:</h1>
                <p className="text-xl font-medium mb-4 font-bold">On-site MSI Calibration Report</p>
                <p className="text-md">{DateTime.fromISO(data?.created_on, { zone: 'utc' }).toLocal().toFormat('LLLL dd,yyyy')} </p>
              </div>
            </div>
          </div>
        </div>

        {/* PAGE 2 */}
        <div className="page min-h-screen p-8 mb-5" style={{ background: "#FFFFFF" }}>
          <header style={{ marginBottom: "40px", textAlign: "right" }}>
            <img src={"https://api.eisqr.com/docs/1738227606762tvsm_logo.jpg"} alt="Logo" style={{ maxWidth: 150 }} />
          </header>

          <h1 className="text-2xl font-bold mb-4">
            Calibration ID:
            <span className="text-gray-500">
              {getMSIIDByAssignment(data)}
            </span>
          </h1>

          <div className='row'>
            <div className='col-12 ps-4' >
              <p><strong>Dealer name:</strong> {data?.vendor?.dealerName}</p>
              <p><strong>Dealer ID:</strong> {data?.vendor?.code}</p>
              <p><strong>Calibration date:</strong>{getDate(data?.auditStartDate)}</p>

              <p><strong>Dealer category:</strong> {DealerZone.find(x => x.value === data?.vendor?.dealerCategory)?.name}</p>

              {/* <p><strong>Number of critical non-compliance:</strong> {data?.supplierActions?.filter(x => x.categoryOfFinding === 3)?.length || 0} </p> */}
            </div>

            <div className='col-12 ps-4' >
              <p><strong>Dealer Zone:</strong> {zonalOfficeList.find(x => x.value === data?.vendor?.dealerZone)?.name} {data.vendor.dealerZone}</p>
              <p><strong>Dealer location:</strong> {data?.vendor?.dealerLocation}</p>
              <p><strong>Calibration Team Member:</strong> {getCalibrationTeamMember()}</p>

              <p><strong>MSI Score:</strong> {data?.dealerAuditorChecklistSubmission?.score
                ? (typeof data.dealerAuditorChecklistSubmission.score === "string"
                  ? JSON.parse(data.dealerAuditorChecklistSubmission.score).overallScore
                  : data.dealerAuditorChecklistSubmission.score.overallScore)
                : "N/A"}</p>

              {/* <p ><strong>MSI Rank:</strong> <div style={{ width: 50, display: 'inline-table' }}> {data?.auditorAssignmentSubmission?.auditorMSIScore != null ? report?.auditorAssignmentSubmission?.auditorMSIScore > 85 ? <img width={'100%'} src={require('../../../../assets/images/report/valuechain/platinum_rating.png').default} /> : report?.auditorAssignmentSubmission?.auditorMSIScore > 70 ? <img width={'100%'} src={require('../../../../assets/images/report/valuechain/gold_rating.png').default} /> : report?.auditorAssignmentSubmission?.auditorMSIScore > 55 ? <img width={'100%'} src={require('../../../../assets/images/report/valuechain/silver_rating.png').default} /> : report?.auditorAssignmentSubmission?.auditorMSIScore > 40 ? <img width={'100%'} src={require('../../../../assets/images/report/valuechain/bronze_rating.png').default} /> : 'Needs Improvement' : "NA"} </div> </p> */}

              <p>
                <strong>MSI Rank:</strong>
                {typeof rankImage === "string" && !rankImage.includes('static') ? (
                  // <img style={{ width: 150 }} src={rankImage} alt="MSI Rank" />
                  <span> {rankImage} </span>
                ) : (
                  <span style={{ width: 50, display: 'inline-block' }}>
                    <img width="100%" src={rankImage} alt="MSI Rank" />
                  </span>
                )}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-8">

            {/* <SubGraph1Demo supplyData={report?.actualResponse || []} /> */}


          </div>
        </div>

        {/* PAGE 3 */}
        <div className="page bg-white mb-5" style={{ fontFamily: "Arial, sans-serif", padding: "40px" }}>
          <header style={{ marginBottom: "40px", textAlign: "right" }}>
            <img src={"https://api.eisqr.com/docs/1738227606762tvsm_logo.jpg"} alt="Logo" style={{ maxWidth: 150 }} />
          </header>
          {chart.length !== 0 && (
            (chart[0]?.category === "Area Office" || chart[0]?.category === "Authorized Parts Stockist (APS)") ? (
              <ESGScoresTab data={chart} />
            ) : (
              <>
                <ESGScores data={chart} />
                <ESG_Sales_Service data={chart} />
              </>
            )
          )}
        </div>

        <div className="page bg-white mb-5" style={{ fontFamily: "Arial, sans-serif", padding: "40px" }}>
          <header style={{ marginBottom: "40px", textAlign: "right" }}>
            <img src={"https://api.eisqr.com/docs/1738227606762tvsm_logo.jpg"} alt="Logo" style={{ maxWidth: 150 }} />
          </header>
          {chart.length !== 0 && (
            (chart[0]?.category === "Area Office" || chart[0]?.category === "Authorized Parts Stockist (APS)") ? (
              <>
                <EnvironmentTab data={chart} />
                <SocialTab data={chart} />
              </>
            ) : (
              <>
                <EnvironmentChart data={chart} />
                <SocialChart data={chart} />
              </>
            )
          )}
        </div>

        <div className="page bg-white mb-5" style={{ fontFamily: "Arial, sans-serif", padding: "40px" }}>
          <header style={{ marginBottom: "40px", textAlign: "right" }}>
            <img src={"https://api.eisqr.com/docs/1738227606762tvsm_logo.jpg"} alt="Logo" style={{ maxWidth: 150 }} />
          </header>
          {chart.length !== 0 && (
            (chart[0]?.category === "Area Office" || chart[0]?.category === "Authorized Parts Stockist (APS)") ? (
              <>
                <GovernanceTab data={chart} />

              </>
            ) : (
              <>
                <GovernanceChart data={chart} />
                <GeneralChart data={chart} />
              </>
            )
          )}
        </div>





        {/* PAGE 5 */}









        <div
          className="page bg-white mb-5 environment-report"
          style={{ padding: 40, lineHeight: 1.6 }}
        >
          {(() => {
            let isGreenHeader = false;
            let greenIndex = 0;
            let lastCriteria = ""; // To track last displayed criteria

            const criteriaColors = {
              environment: "#41AA95",
              social: "#FF9D7C",
              governance: "#5B8FF7",
              general: "#4F4F4F",
            };

            return actions.map((item, index) => {
              if (item.type === "header") {
                isGreenHeader = true;
                greenIndex++;
                lastCriteria = ""; // Reset criteria when header appears

                return (
                  <section key={index} className="mb-4">
                    <header>
                      <h3> Dealer (<span dangerouslySetInnerHTML={{ __html: item.label }} />)  MSI Calibration </h3>
                    </header>
                  </section>
                );
              }

              if (item.type === "checklist-group") {
                const sectionTitle = item.label || "Untitled Section";

                const headerStyle = {
                  backgroundColor:
                    criteriaColors[item.criteria.toLowerCase()] || "gray",
                  color: "white",
                  padding: "10px",
                  borderRadius: "5px",
                };

                // Store current criteria before returning
                const currentCriteria = item.criteria;

                // Toggle background flag
                isGreenHeader = !isGreenHeader;

                const sectionContent = (<>
                  {currentCriteria !== lastCriteria && (
                    <h2>{currentCriteria.toUpperCase()}</h2>
                  )}

                  <section key={index} className="report-section">
                    {/* Display criteria only if it's different */}


                    <header className="section-header d-flex align-items-center">
                      <h3 className="mb-0 me-2" dangerouslySetInnerHTML={{ __html: sectionTitle }} />
                      <span>({item.score})</span>
                    </header>


                    <table className="report-table">
                      <thead>
                        <tr>
                          <th>CHECK POINT</th>
                          <th>DEPT</th>
                          <th>OBSERVATION EVIDENCE</th>
                          <th>REQUIRED ACTION</th>
                          <th>RESPONSIBLE PERSON</th>
                          <th>DUE DATE</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(item.questions || []).map((question, qIndex) => {
                          const actions = question.actions || {};
                          const hasActions =
                            actions.actionToBeTaken ||
                            actions.personResponsible ||
                            actions.dueDate;
                          if (!hasActions) return null;

                          const dueDate = actions.dueDate
                            ? new Date(actions.dueDate).toLocaleDateString("en-GB")
                            : "";

                          return (
                            <tr key={qIndex}>
                              <td>{question.label}</td>
                              <td>{item.section === 1 ? "Sales" : "Service"}</td>
                              <td>{question.remarks || ""}</td>
                              <td>{actions.actionToBeTaken || ""}</td>
                              <td>{actions.personResponsible || ""}</td>
                              <td>{dueDate}</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </section>
                </>);


                lastCriteria = currentCriteria;

                return sectionContent;
              }

              return null;
            });
          })()}
        </div>




      </div>
    </>
  );
});

export default DealerCompletedReport;

