import React, { useEffect, useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import SectionBox from './SectionBox';
import APIServices from '../../../service/APIService';
import { API } from '../../../constants/api_url';
import { useSelector } from 'react-redux';

const initialFormData = (form, userResponse) => {
  return JSON.parse(form.data1).map((question) => {
    const responses = Object.entries(userResponse).map(([user, answers]) => {
      console.log(answers, user)
      let rawValue = answers[question.name];
      let displayValue = rawValue;
      let comment = answers[`${question.name}_comments`];
      if ((question.type === "radio-group" || question.type === "select") && question.values) {
        const matched = question.values.find(v => v.value === rawValue);
        displayValue = matched ? matched.label : rawValue;
      }

      if (question.type === "checkbox-group" && Array.isArray(rawValue)) {

        displayValue = rawValue
          .map(val => {
            const matched = question.values.find(v => v.value === val);
            return matched ? matched.label : null;
          })
          .filter(Boolean);
        console.log(displayValue, question.values, form)
      }

      if (rawValue !== undefined) {
        return {
          user, entity: answers.entity,
          answer: displayValue,
          comment
        };
      }

      return null;
    }).filter(Boolean);

    return { ...question, response: responses };
  });
};

const ConsolidateDialog = ({ visible, onHide, assignmentId, sectionId, rawSiteList }) => {
  const [formData, setFormData] = useState([]);
  const [form, setForm] = useState({});
  const [userResponse, setUserResponse] = useState({});
  const [header, setHeader] = useState({});
  const [loading, setLoading] = useState(true);

  const login_data = useSelector((state) => state.user.userdetail);
  const admin_data = useSelector((state) => state.user.admindetail);
  const userList = useSelector(state => state.userlist.userList);
  const tvsExtUserList = useSelector(state => state.userlist.tvsExtUserList);
  const userList_ = [...userList, ...tvsExtUserList];

  useEffect(() => {
    if (visible && assignmentId && sectionId) {
      setLoading(true);
      loadData();
    }
  }, [visible, assignmentId, sectionId]);
  const getCoverageText = (rowData, rawsitelist) => {
    let text = "";

    if (rowData.level === 0) {
      text = "Corporate";
    } else if (rowData.level === 1) {
      let country_index = rawsitelist.findIndex(
        (i) => i.id === rowData.locationId
      );
      if (country_index !== -1) {
        text = rawsitelist[country_index].name;
      }
    } else if (rowData.level === 2) {
      let city_index = rawsitelist
        .flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )
        .findIndex((i) => {
          return i.city_id === rowData.locationId;
        });
      if (city_index !== -1) {
        text = rawsitelist.flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )[city_index].city_name;
      }
    } else if (rowData.level === 3) {
      let site_index = rawsitelist
        .flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )
        .findIndex((i) => {
          return i.site_id === rowData.locationId;
        });
      if (site_index !== -1) {
        text = rawsitelist.flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )[site_index].site_name;
      }
    }
    return text;
  };
  const loadData = async () => {
    try {
      const res = await APIServices.post(API.GetAssignedQualitative_consolidator, {
        userProfileId: admin_data.id,
        userId: 0
      });
      const userResonse = res?.data?.reporter
        ?.filter(x => x.qSectionId === sectionId)
        .map(x => {
          if (x?.response && typeof x.response === 'object') {
            const entity = getCoverageText(x, rawSiteList);
            return Object.fromEntries(
              Object.entries(x.response).map(([key, value]) => [
                key,
                { ...value, entity }
              ])
            );
          } else {
            return null; // return null instead of {} so we can filter it out
          }
        })
        .filter(Boolean) // remove nulls
        .reduce((a, b) => ({ ...a, ...b }), {})
      // const userResonse = res?.data?.reporter
      //   ?.filter(x => x.qSectionId === sectionId)
      //   .map(x => x?.response ? {...x.response, entity:getCoverageText(x, rawSiteList)} : {})
      //   .reduce((a, b) => { return { ...a, ...b } }, {});

      const response = await APIServices.get(API.GetQualitativeConsolidate(assignmentId) +
        `?filter=${encodeURIComponent(JSON.stringify({
          include: [
            { relation: 'qTopic' },
            { relation: 'qCategory' },
            { relation: 'qSection', scope: { include: [{ relation: 'srf' }] } }
          ]
        }))}`);

      let srfForm = response?.data?.qSection?.srf || {};

      setHeader({
        category: response?.data?.qCategory?.name || '',
        topic: response?.data?.qTopic?.name || '',
        section: response?.data?.qSection?.name || ''
      });

      if (srfForm && srfForm.data1) {
        const loc = JSON.parse(JSON.stringify(srfForm));
        setForm(loc);
        setUserResponse(userResonse);

        setFormData(initialFormData(loc, {
          ...userResonse,
          ...(response?.data?.response || {})
        }));
      }

      setLoading(false);
    } catch (error) {
      console.error("Error loading consolidation data:", error);
      setLoading(false);
    }
  };

  const handleUpdateResponse = (updatedResponses) => {

  };

  return (
    <Dialog
      header="Consolidate Responses"
      visible={visible}
      style={{ width: '80vw', maxHeight: '90vh' }}
      onHide={onHide}
      maximizable
      modal
    >
      {loading ? (
        <div className="p-4 text-center">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
          <p>Loading consolidation data...</p>
        </div>
      ) : (
        <div className="p-3" style={{ overflowY: 'auto', maxHeight: 'calc(90vh - 120px)' }}>
          <div className='col-12 grid mb-3'>
            <div className='col-4'>Category: <strong>{header.category}</strong></div>
            <div className='col-4'>Topic: <strong>{header.topic}</strong></div>
            <div className='col-4'>Section: <strong>{header.section}</strong></div>
          </div>
          <h4 className="mb-3">Form #{form?.id} : {form?.title}</h4>
          {formData.map((item, index) => (
            <SectionBox
              key={index}
              data={item}
              onUpdateResponse={handleUpdateResponse}
              userlist={userList_}
            />
          ))}
          <div className="flex justify-content-end mt-4">
            <Button label="Close" onClick={onHide} className="p-button-secondary" />
          </div>
        </div>
      )}
    </Dialog>
  );
};

export default ConsolidateDialog;
