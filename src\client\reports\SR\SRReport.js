import { useEffect, useRef, useState } from "react";
import "./sr_report.css";
import { But<PERSON> } from "primereact/button";
import { ScrollPanel } from "primereact/scrollpanel";

import { saveAs } from "file-saver";

import GRI2 from "./components/GRI2";
import GRI3 from "./components/GRI3";

import { principles } from "./components/principles";
import Governance from "./components/Governance";
import Environment from "./components/Environment";
import Social from "./components/Social";
import APIServices from "../../../service/APIService";
import { API } from "../../../constants/api_url";
import { useSelector } from "react-redux";

function SRReport() {
  const [loadingType, setLoadingType] = useState(null);
  const [selected, setSelected] = useState(principles[0]);
  const sectionRefs = useRef([]);
  const [qualitativeData, setQualitaiveData] = useState({})
   const admin_data = useSelector((state) => state.user.admindetail);
    const login_data = useSelector((state) => state.user.userdetail);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: Array.from({ length: 11 }, (_, i) => i * 0.1),
    };
APIServices.post(API.Ql_Consolidated_Report(admin_data.id),{
 "ids":["SDP184_3","SDP184_17","SDP184_20","SDP143_15","SDP143_42","SDP143_45","SDP143_51","SDP143_53","SDP128_3","SDP128_3","SDP128_31","SDP204_13","SDP204_14","SDP204_15","SDP185_1","SDP204_16","SDP204_17","SDP204_18","SDP173_26","SDP173_26","SDP173_41","SDP173_41"]
})
    const observer = new IntersectionObserver((entries) => {
      const visible = entries.filter((e) => e.isIntersecting);
      if (visible.length) {
        const top = visible.sort(
          (a, b) => b.intersectionRatio - a.intersectionRatio
        )[0];
        const title = top.target.getAttribute("data-title");
        if (title) setSelected(title);
      }
    }, options);

    sectionRefs.current.forEach((ref) => ref && observer.observe(ref));

    return () => {
      sectionRefs.current.forEach((ref) => ref && observer.unobserve(ref));
    };
  }, []);

  const getQualitativeReportData = (sdp) => {
  const sdpArray = Array.isArray(sdp) ? sdp : [sdp];
const results = sdpArray.flatMap((key) => {
    const value = qualitativeData[key]?.value;
    if (!value) return [];

    return Array.isArray(value) ? value : [value];
  });

  return results.length > 0 ? results.join(', ') : '-';
};
const getQualitativeReportReferenceData = (sdpArray) => {
  return (sdpArray || []).flatMap(sdp => [
    ...(qualitativeData[sdp]?.framework_mandatory || []),
    ...(qualitativeData[sdp]?.framework_mandatory_if_material || []),
    ...(qualitativeData[sdp]?.framework_good_to_have || []),
    ...(qualitativeData[sdp]?.framework_not_required || []),
  ]);
};
console.log(getQualitativeReportData ("SDP184_3"));
  const generateTocList = (items) => {
    return `<ol>${items
      .map((item) => {
        const childrenList = item.children
          ? `<ul>${item.children
            .map(
              (child) =>
                `<li style="list-style: none; font-size: 0.9rem;">${child.title}</li>`
            )
            .join("")}</ul>`
          : "";
        return `<li><strong>${item.title}</strong>${childrenList}</li>`;
      })
      .join("")}</ol>`;
  };

  const exportWord = () => {
    setLoadingType("word");
    setTimeout(() => {
      try {
        const element = document.getElementById("report-content");
        const html = element?.outerHTML;
        const css = `
        <style>
          table { width:100%; border-collapse:collapse; }
          th, td { border:1px solid #333; padding:6px; }
        </style>`;
        const fullDoc = `<!DOCTYPE html><html><head><meta charset='utf-8'/>${css}</head><body>${html}</body></html>`;
        const blob = new Blob([fullDoc], {
          type: "application/msword;charset=utf-8",
        });
        saveAs(blob, "GRI_Report.doc");
      } catch (e) {
        console.error(e);
        alert("Word export failed");
      } finally {
        setLoadingType(null);
      }
    }, 100);
  };

  const exportPDF = async () => {
    setLoadingType("pdf");

    try {
      const content = document.getElementById("report-content");
      if (!content) throw new Error("Report content not found");

      const html = `
        <html>
          <head>
            <style>
              body { font-family: Arial; padding: 40px; }
              .page-break { page-break-before: always; }
              table { width: 100%; border-collapse: collapse; margin-top: 10px; }
              th, td { border: 1px solid #333; padding: 6px; text-align: left; }
              h2 { color: #2c3e50; }
            </style>
          </head>
          <body>
            ${content.innerHTML}
          </body>
        </html>
      `;

      const response = await fetch(
       API.DownloadSRReport('pdf'),
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ html }),
        }
      );

      if (!response.ok) throw new Error("PDF generation failed");

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = "GRI_Report.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error("PDF export failed", err);
      alert("PDF export failed");
    } finally {
      setLoadingType(null);
    }
  };

  return (
    <div className="brsr-report-container">
      <div className="report-sidebar">
        <h2 className="report-sidebar-title">GRI Report Index</h2>
        <ScrollPanel className="report-sidebar-scroll">
          <ul className="principles-list">
            {principles.map((item) => (
              <li key={item.title}>
                <div
                  className={`principle-item ${selected === item.title ? "selected" : ""
                    }`}
                  onClick={() => {
                    setSelected(item.title);
                    const idx = principles.indexOf(item.title);
                    if (idx !== -1) {
                      sectionRefs.current[idx]?.scrollIntoView({
                        behavior: "smooth",
                      });
                    }
                  }}
                >
                  {item.title}
                </div>
                {item.children && (
                  <ul style={{ marginLeft: "-1.3rem" }}>
                    {item.children.map((sub) => (
                      <li style={{ listStyle: "none" }} key={sub.title}>
                        <div
                          className="principle-item"
                          style={{ fontSize: "0.8rem" }}
                          onClick={() => {
                            const el = document.getElementById(sub.targetId);
                            if (el) el.scrollIntoView({ behavior: "smooth" });
                          }}
                        >
                          {sub.title}
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </ScrollPanel>
      </div>

      <div id="report-content" className="main-content scroll-sections">
        <div id="gri2">
          <GRI2 getQualitativeReportData={getQualitativeReportData} />
        </div>
        <div id="gri3">
          <GRI3 />
        </div>
        <div id="governance">
          <Governance getQualitativeReportData={getQualitativeReportData} />
        </div>
        <div id="Environment">
          <Environment />
        </div>
        <div id="Social">
          <Social />
        </div>

        <div className="export-buttons"   style={{ marginTop: "2rem" }}>
          <Button
          
            label={loadingType === "word" ? "Exporting Word..." : "Export Word"}
            icon="pi pi-file-word"
            onClick={exportWord}
            loading={loadingType === "word"}
            severity="info"
          />
          <Button
            label={loadingType === "pdf" ? "Exporting PDF..." : "Export PDF"}
            icon="pi pi-file-pdf"
            onClick={exportPDF}
            loading={loadingType === "pdf"}
            severity="danger"
          />
        </div>
      </div>
    </div>
  );
}

export default SRReport;
