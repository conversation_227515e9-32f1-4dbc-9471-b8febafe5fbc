import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { MultiSelect } from 'primereact/multiselect';
import { Dialog } from 'primereact/dialog';

import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import moment from 'moment';
import DealerSubmissionView from './DealerSubmissionView';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';

const DealersSelfAssessmentTable = ({ data, dealerList, assessorList, globalFilter }) => {
    const [databk, setDatabk] = useState([]);
    const [datas, setDatas] = useState([]);
    const [search, setSearch] = useState('');
    const [dateFilter, setDateFilter] = useState({ start: null, end: null });
    const [submissionDialog, setSubmissionDialog] = useState(false);
    const [submissionData, setSubmissionData] = useState(null);
    const [dealerInfo, setDealerInfo] = useState(null);
    const [filteredDataCount, setFilteredDataCount] = useState(0);
    const [currentFilteredData, setCurrentFilteredData] = useState([]);
    const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);
    const dataTableRef = useRef(null);

    // Add state for pagination
    const [rows, setRows] = useState(10);
    const [first, setFirst] = useState(0);

    // Add filter state management for DataTable
    const [tableFilters, setTableFilters] = useState({
        dealerName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        selfAssessmentGrade: { matchMode: 'in', value: null },
        zone: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null }
    });

    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter;
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.dealerName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }

        // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            filteredData = filteredData.filter(rowData => {
                // Since rowData is now a DealerSelfSubmission record, get reporting period directly
                if (rowData.reporting_period?.[0]) {
                    const reportingPeriod = rowData.reporting_period[0];
                    let submissionDate;

                    // Handle both formats: "May-2025" and "06-2025"
                    if (reportingPeriod.includes('-')) {
                        const parts = reportingPeriod.split('-');
                        if (parts.length === 2) {
                            const [monthPart] = parts;

                            // If monthPart is numeric (like "06"), parse as MM-YYYY
                            if (/^\d{2}$/.test(monthPart)) {
                                submissionDate = moment(reportingPeriod, 'MM-YYYY').toDate();
                            } else {
                                // If monthPart is text (like "May"), parse as MMM-YYYY
                                submissionDate = moment(reportingPeriod, 'MMM-YYYY').toDate();
                            }
                        }
                    }

                    // Fallback: try both formats
                    if (!submissionDate) {
                        const momentDate = moment(reportingPeriod, ['MM-YYYY', 'MMM-YYYY'], true);
                        if (momentDate.isValid()) {
                            submissionDate = momentDate.toDate();
                        }
                    }

                    if (submissionDate) {
                        const startDate = moment(dateFilter.start).startOf('day').toDate();
                        const endDate = moment(dateFilter.end).endOf('day').toDate();
                        return submissionDate >= startDate && submissionDate <= endDate;
                    }
                }
                return false;
            });
        }

        // Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Add grade property based on self-assessment score
        setDatas(indexedData.map(x => {
            const selfAssessmentScore = getSelfAssessmentScore(x);
            return {
                ...x,
                selfAssessmentGrade: getRatingName(selfAssessmentScore)
            };
        }));
    };

    // Modified useEffect to only run when data actually changes, not on every render
    useEffect(() => {
        if (JSON.stringify(databk) !== JSON.stringify(data)) {
            setDatabk(data);
            applyFilters(data);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    // Separate useEffect for date filter changes
    useEffect(() => {
        if (databk.length > 0) {
            applyFilters(databk);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    // Initialize filtered count when data changes
    useEffect(() => {
        // Set initial count when data is loaded or when search/date filters change
        // This ensures the count is always up to date
        setFilteredDataCount(datas.length);
        console.log('Data changed, setting filtered count to:', datas.length);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [datas.length]);

    useEffect(() => {
        const fetchSubmissions = async () => {
            try {
                const res = await APIServices.get(API.DealerSelfSubmission);
                setDealerSelfSubmissions(res?.data || []);
            } catch (error) {
                console.error('Error fetching DealerSelfSubmission:', error);
            }
        };

        fetchSubmissions();
    }, []);

    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]
       const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]
    const searchFn = (e) => {
        let val = e.target.value;
        setSearch(val);
        applyFilters(databk, val);
    };

    const calibrationIdBodyTemplate = (rowData) => {
        return (
            <span>
                {'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
            </span>
        );
    };

    const nameTemplate = (rowData) => {
        return <div>{rowData?.vendor?.dealerName || 'NA'}</div>;
    };

    const locationTemplate = (rowData) => {
        return <div>{rowData?.vendor?.dealerLocation || 'NA'}</div>;
    };

    const zoneTemplate = (rowData) => {
        return <div>{zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA'}</div>;
    };

    const categoryTemplate = (rowData) => {
        return <div>{dealerType.find(x => x.value === rowData?.vendor?.dealerCategory)?.name || 'NA'}</div>;
    };

    const selfAssessmentMonthTemplate = (rowData) => {
        // Since rowData is now a DealerSelfSubmission record, get reporting period directly
        if (rowData.reporting_period?.[0]) {
            const reportingPeriod = rowData.reporting_period[0];

            // Handle both formats: "May-2025" and "06-2025"
            if (reportingPeriod.includes('-')) {
                // Check if it's month name format (May-2025) or numeric format (06-2025)
                const parts = reportingPeriod.split('-');
                if (parts.length === 2) {
                    const [monthPart] = parts;

                    // If monthPart is numeric (like "06"), parse as MM-YYYY
                    if (/^\d{2}$/.test(monthPart)) {
                        return moment(reportingPeriod, 'MM-YYYY').format('MMMM YYYY');
                    } else {
                        // If monthPart is text (like "May"), parse as MMM-YYYY
                        return moment(reportingPeriod, 'MMM-YYYY').format('MMMM YYYY');
                    }
                }
            }

            // Fallback: try both formats
            const momentDate = moment(reportingPeriod, ['MM-YYYY', 'MMM-YYYY'], true);
            if (momentDate.isValid()) {
                return momentDate.format('MMMM YYYY');
            }
        }
        return 'NA';
    };

    const selfAssessmentScoreTemplate = (rowData) => {
        return getSelfAssessmentScore(rowData);
    };

    const getSelfAssessmentScore = (rowData) => {
        // Since rowData is now a DealerSelfSubmission record, get score directly
        try {
            const scoreObj = JSON.parse(rowData.score || '{}');
            return scoreObj.overallScore ?? 'NA';
        } catch (e) {
            return 'NA';
        }
    };

    const selfAssessmentSubmitDateTemplate = (rowData) => {
        // Since rowData is now a DealerSelfSubmission record, get date directly
        if (rowData.created_on) {
            const submitDate = DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy');
            rowData.latestSubmission = submitDate;
            return submitDate;
        }
        rowData.latestSubmission = '';
        return '';
    };
    const getLastSubmissionDate = (rowData) => {
        // Since rowData is now a DealerSelfSubmission record, get date directly
        if (rowData.created_on) {
            return DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy');
        }
        return '';
    };
    const getRatingName = (score) => {
        if (!score || score === '-' || score === 'NA') return 'NA';
        score = parseFloat(score);
        if (score >= 85) return 'Platinum';
        if (score > 70) return 'Gold';
        if (score > 55) return 'Silver';
        return 'Not Met';
    };

    const ratingTemplate = (rowData) => {
        const score = getSelfAssessmentScore(rowData);
        if (score === 'NA') return 'NA';

        const scoreValue = parseFloat(score);
        return (
            <div style={{ width: scoreValue > 55 ? 50 : 80 }}>
                {scoreValue >= 85 ?
                    <img width={'100%'} alt="Platinum Rating" src={require('../../../../assets/images/report/valuechain/platinum_rating.png').default} /> :
                    scoreValue > 70 ?
                        <img width={'100%'} alt="Gold Rating" src={require('../../../../assets/images/report/valuechain/gold_rating.png').default} /> :
                        scoreValue > 55 ?
                            <img width={'100%'} alt="Silver Rating" src={require('../../../../assets/images/report/valuechain/silver_rating.png').default} /> :
                            scoreValue > 40 ?
                                "Bronze" :
                                "Needs Improvement"}
            </div>
        );
    };

    const sortSelfAssessmentScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for ascending
                if (scoreB === 'NA') return -1;

                return parseFloat(scoreA) - parseFloat(scoreB);
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for descending too
                if (scoreB === 'NA') return -1;

                return parseFloat(scoreB) - parseFloat(scoreA);
            });
        }
    };

    const sortSelfAssessmentMonth = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = selfAssessmentMonthTemplate(a);
                const monthB = selfAssessmentMonthTemplate(b);

                if (monthA === 'NA' && monthB === 'NA') return 0;
                if (monthA === 'NA') return 1;
                if (monthB === 'NA') return -1;

                const dateA = moment(monthA, 'MMMM YYYY');
                const dateB = moment(monthB, 'MMMM YYYY');
                return dateA - dateB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = selfAssessmentMonthTemplate(a);
                const monthB = selfAssessmentMonthTemplate(b);

                if (monthA === 'NA' && monthB === 'NA') return 0;
                if (monthA === 'NA') return 1;
                if (monthB === 'NA') return -1;

                const dateA = moment(monthA, 'MMMM YYYY');
                const dateB = moment(monthB, 'MMMM YYYY');
                return dateB - dateA;
            });
        }
    };
    const sortSelfAssessmentSubmitDate = (e) => {
        console.log(e.data)
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB ) return 0;
                if (!monthA ) return 1;
                if (!monthB ) return -1;

                const dateA = DateTime.fromFormat(monthA, 'dd-MM-yyyy')
                const dateB = DateTime.fromFormat(monthB, 'dd-MM-yyyy')
                console.log(dateA.isValid, dateB.isValid);
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB ) return 0;
                if (!monthA ) return 1;
                if (!monthB ) return -1;


                const dateA = DateTime.fromFormat(monthA, 'dd-MM-yyyy')
                const dateB = DateTime.fromFormat(monthB, 'dd-MM-yyyy')
                console.log(monthA,monthB);
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    }
    const sortIndexColumn = (e) => {
        const { data, order } = e;

        // Create a new array with the current data and add an index property
        const indexedData = data.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Sort based on the index
        if (order === 1) { // ascending
            return indexedData.sort((a, b) => a.tableIndex - b.tableIndex);
        } else { // descending
            return indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
        }
    };

    // Process self-assessment submissions directly (data now contains DealerSelfSubmission records)
    const dealersWithSelfAssessments = datas.map((submission, index) => ({
        ...submission,
        // Add properties for compatibility with existing templates
        dealerId: submission.dealerId,
        submissionId: submission.id,
        currentSubmission: submission,
        // Add vendor info directly from submission
        vendor: submission.vendor || {},
        tableIndex: index + 1
    }));

    // Memoize the enhanced data to prevent unnecessary re-computations
    const enhancedData = useMemo(() => {
        return dealersWithSelfAssessments.map((x, index) => ({
            ...x,
            tableIndex: index + 1,
            latestSubmission: getLastSubmissionDate(x),
            selfAssessmentGrade: getRatingName(getSelfAssessmentScore(x)),
            uniqueKey: `${x.dealerId}-${x.submissionId || index}` // Unique key for each row
        }));
    }, [dealersWithSelfAssessments, dealerSelfSubmissions]);

    // Manual filter calculation function as a fallback
    const calculateFilteredCount = useCallback(() => {
        if (!enhancedData || enhancedData.length === 0) return 0;

        let filteredData = [...enhancedData];

        // Apply table filters
        Object.entries(tableFilters).forEach(([field, filter]) => {
            if (filter.value && filter.value.length > 0) {
                filteredData = filteredData.filter(item => {
                    const fieldValue = item[field];
                    if (fieldValue == null) return false;
                    return filter.value.includes(fieldValue);
                });
            }
        });

        // Apply global filter if present
        if (globalFilter && globalFilter.trim()) {
            const globalFilterLower = globalFilter.toLowerCase();
            filteredData = filteredData.filter(item => {
                return Object.values(item).some(value =>
                    value && value.toString().toLowerCase().includes(globalFilterLower)
                );
            });
        }

        return filteredData.length;
    }, [enhancedData, tableFilters, globalFilter]);

    // Initialize filtered count when enhanced data changes
    useEffect(() => {
        // Only update if no filters are currently applied
        const hasActiveFilters = Object.values(tableFilters).some(filter =>
            filter.value && filter.value.length > 0
        );

        if (!hasActiveFilters && !globalFilter) {
            setFilteredDataCount(enhancedData.length);
            console.log('Enhanced data changed, updating count to:', enhancedData.length);
        }
    }, [enhancedData.length, tableFilters, globalFilter]);

    // Backup mechanism to ensure count accuracy
    useEffect(() => {
        const updateCountFromCalculation = () => {
            const calculatedCount = calculateFilteredCount();

            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Manual calculation: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        };

        // Check periodically for any missed updates
        const interval = setInterval(updateCountFromCalculation, 3000);

        return () => clearInterval(interval);
    }, [calculateFilteredCount, filteredDataCount]);

    // Also trigger manual calculation when filters change
    useEffect(() => {
        const calculatedCount = calculateFilteredCount();
        console.log('🔢 Filters changed, calculated count:', calculatedCount);

        // Small delay to allow DataTable to process first
        const timeout = setTimeout(() => {
            if (calculatedCount !== filteredDataCount) {
                console.log('🔄 Filter change: Updating count to:', calculatedCount);
                setFilteredDataCount(calculatedCount);
            }
        }, 500);

        return () => clearTimeout(timeout);
    }, [tableFilters, globalFilter, calculateFilteredCount]);
const RowFilterTemplate = (options, obj) => {
        // Use the same data structure as the DataTable to ensure all fields are available
        const dataWithAllFields = dealersWithSelfAssessments.map(x => ({
            ...x,
            latestSubmission: getLastSubmissionDate(x),
            selfAssessmentGrade: getRatingName(getSelfAssessmentScore(x))
        }));

        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(dataWithAllFields.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };

    // Use useCallback to prevent unnecessary re-renders when opening dialogs
    const handleViewSubmission = useCallback((rowData, matchedSubmission) => {
        try {
            const parsed = JSON.parse(matchedSubmission?.response || '{}');

            // Prepare dealer info for export
            const dealerInfo = {
                calibrationId: 'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy'),
                dealerName: rowData?.vendor?.dealerName || 'NA',
                location: rowData?.vendor?.dealerLocation || 'NA',
                zone: zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA',
                category: dealerType.find(x => x.value === rowData?.vendor?.dealerCategory)?.name || 'NA',
                selfAssessmentMonth: selfAssessmentMonthTemplate(rowData),
                selfAssessmentScore: selfAssessmentScoreTemplate(rowData),
                msiRating: getRatingName(selfAssessmentScoreTemplate(rowData)),
                selfAssessmentSubmitDate: selfAssessmentSubmitDateTemplate(rowData)
            };

            setSubmissionData(parsed);
            setDealerInfo(dealerInfo);
            setSubmissionDialog(true);
        } catch (err) {
            console.error('Invalid JSON in response', err);
            alert('Invalid submission data');
        }
    }, [dealerType, zonalOfficeList, selfAssessmentScoreTemplate]);

    const exportExcel = () => {
        if (!dealersWithSelfAssessments || dealersWithSelfAssessments.length === 0) {
            alert('No data to export.');
            return;
        }

        const exportData = dealersWithSelfAssessments.map((item, index) => ({
            'S.No': index + 1,
            'Calibration ID': item.vendor?.code ? `MSI-${item.vendor.code}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Dealer Name': item.vendor?.dealerName || 'NA',
            'Location': item.vendor?.dealerLocation || 'NA',
            'Zone': zonalOfficeList.find(x => x.value === item.vendor?.dealerZone)?.name || 'NA',
            'Category': dealerType.find(x => x.value === item.vendor?.dealerCategory)?.name || 'NA',
            'Self-assessment Month': selfAssessmentMonthTemplate(item),
            'Self-assessment Score': selfAssessmentScoreTemplate(item),
            'MSI Rating': getRatingName(selfAssessmentScoreTemplate(item)),
            'Self-assessment Submit Date': selfAssessmentSubmitDateTemplate(item)
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'All Dealer Self Assessments');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `All_Dealer_Self_Assessments_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };



    return (
        <>
            <div className="col-12 flex justify-content-between align-items-center mb-3" >
                <div className="col-6 flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Self-assessment Month From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            view="month"
                            yearNavigator
                            yearRange="2020:2030"
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            view="month"
                            yearNavigator
                            yearRange="2020:2030"
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>
                <div className='col-5'>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText value={search} style={{ width: '100%' }} onChange={searchFn} placeholder="Search Code/Name" />
                    </span>
                </div>
            </div>
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4>All MSI Self-assessments ({filteredDataCount})</h4>
                <button
                    className="btn btn-sm btn-success"
                    onClick={exportExcel}
                >
                    Download Excel
                </button>
            </div>

            <DataTable
                ref={dataTableRef}
                value={enhancedData}
                paginator
                rows={rows}
                first={first}
                rowsPerPageOptions={[10, 25, 50, 100,150,200]}
                scrollable
                scrollHeight="500px"
                filters={tableFilters}
                filterDisplay="menu"
                onPage={(e) => {
                    console.log('📄 Page changed:', e);
                    setFirst(e.first);
                    setRows(e.rows);
                    // Ensure count remains accurate when navigating pages
                    const currentCount = calculateFilteredCount();
                    if (currentCount !== filteredDataCount) {
                        console.log('🔄 Page change: Updating count to:', currentCount);
                        setFilteredDataCount(currentCount);
                    }
                }}
                onFilter={(e) => {
                    console.log('🔍 DataTable onFilter triggered');
                    console.log('Event object:', e);

                    // Create a copy of the filters object
                    const cleanedFilters = { ...e.filters };

                    if (cleanedFilters.hasOwnProperty('null')) {
                        delete cleanedFilters['null'];
                    }

                    setTableFilters(cleanedFilters);

                    // Update filtered data count based on the actual filtered results
                    // e.filteredValue contains ALL filtered records across all pages
                    let filteredCount;

                    if (e.filteredValue && Array.isArray(e.filteredValue)) {
                        filteredCount = e.filteredValue.length;
                        console.log('✅ Using e.filteredValue.length:', filteredCount);
                    } else {
                        // Fallback to manual calculation
                        filteredCount = calculateFilteredCount();
                        console.log('⚠️ Fallback to manual calculation:', filteredCount);
                    }

                    console.log('🔢 Filter applied:', cleanedFilters);
                    console.log('🔢 Final filtered count (total across all pages):', filteredCount);
                    console.log('🔢 Enhanced data length:', enhancedData.length);

                    // Update the count and filtered data immediately
                    setFilteredDataCount(filteredCount);
                    setCurrentFilteredData(e.filteredValue || enhancedData);
                }}
                globalFilter={globalFilter}
                className="mt-2 h-500"
            >
                <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={sortIndexColumn} />
                <Column sortable field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "msiId")
                    } />
                <Column sortable field="dealerName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "dealerName")
                    } />
                <Column sortable field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "location")
                    } />
                <Column sortable field="zone" header="Zone" body={zoneTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "zone")
                    } />
                <Column sortable field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "cat")
                    } />
                <Column sortable field="selfAssessmentMonth" header="Self-assessment Month" body={selfAssessmentMonthTemplate} sortFunction={sortSelfAssessmentMonth} />
                <Column sortable field="selfAssessmentScore" header="MSI Self-assessment Score" body={selfAssessmentScoreTemplate} sortFunction={sortSelfAssessmentScore} />
                <Column field="selfAssessmentGrade" filter showFilterMatchModes={false}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "selfAssessmentGrade")
                    } header="MSI Rating" body={ratingTemplate} />
                <Column sortable field="latestSubmission" header="Self-assessment Submit Date" body={selfAssessmentSubmitDateTemplate} sortFunction={sortSelfAssessmentSubmitDate} />
                <Column
                    header="View Submissions"
                    body={(rowData) => {
                        // Since rowData is now a DealerSelfSubmission record, use it directly
                        return (
                            <button
                                className="btn btn-sm btn-secondary"
                                onClick={() => handleViewSubmission(rowData, rowData)}
                            >
                                View
                            </button>
                        );
                    }}
                />
            </DataTable>

            <Dialog
                visible={submissionDialog}
                onHide={() => {
                    setSubmissionDialog(false);
                    setDealerInfo(null); // Clear dealer info when dialog is closed
                }}
                style={{ width: '80vw' }}
                className="custom-dialog"
            >
                {submissionData ? (
                    <DealerSubmissionView excelData={submissionData} dealerInfo={dealerInfo} />
                ) : (
                    <p>No submission data available.</p>
                )}
            </Dialog>
        </>
    );
};

export default DealersSelfAssessmentTable;
