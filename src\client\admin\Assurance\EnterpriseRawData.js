import React, { useEffect, useState, useRef } from 'react'
import APIServices from '../../../service/APIService'
import { API } from '../../../constants/api_url'
import { red } from '@mui/material/colors';
import { MultiSelect } from 'primereact/multiselect';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { useSelector } from 'react-redux';
import { filterDataByTierAndLocationByLevel, getFiscalYearsFromStartDate } from '../../../components/BGHF/helper';
import {
    Tab,
    Tabs,
    Box,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
} from "@mui/material";
import { Dropdown } from 'primereact/dropdown';
import { Badge } from 'primereact/badge';
import { Calendar } from 'primereact/calendar';
import XlsxPopulate from 'xlsx-populate';


export const EnterpriseRawData = ({ admin, user }) => {
    const [yearOption, setYearOption] = useState([]);
    const [assFramework, setAssFramework] = useState([])
    const [rawdata, setRawData] = useState([])
    const [rawdatabk, setRawDataBk] = useState([])
    const [load, setLoad] = useState(true)
    const [rawsitelist, setRawsitelist] = useState([]);
    const [filter, setFilter] = useState({
        year: 0,
        section: 0,
        source: 1,
        form: 0,
        framework: [],
        country: 0,
        city: null,
        site: null,
        fromDate: null,
        toDate: null
    })
    const [locList, setLocList] = useState({ country: [], city: [], location: [] })
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);
    const [assignedDcf, setAssignedDcf] = useState([]);
    const [assignedSap, setAssignedSap] = useState([])
    const dt = useRef(null);
    const rawDcfDataColumns = [
        // { field: "sno", header: "SNo" },
        { field: "title", header: "Quantitative Data Point" },
        { field: "value", header: "Quantitative Value" },
        { field: "unitOfMeasure", header: "Unit of Measure" },
        { field: "periodFrom", header: "Reporting Period (From)" },
        { field: "periodTo", header: "Reporting Period (To)" },
        { field: "entity", header: "Reporting Entity" },
        { field: "status", header: "Current Status" },
        { field: "reporter", header: "Reporter" },
        { field: "reportedDate", header: "Reported Date" },
        { field: "reporterComments", header: "Reporter Comments" },
        { field: "reviewer", header: "Reviewer" },
        { field: "reviewedDate", header: "Reviewed Date" },
        { field: "reviewerComments", header: "Reviewer Comments" },
        { field: "approver", header: "Approver" },
        { field: "dateOfApproval", header: "Date of Approval" },
        { field: "approverComments", header: "Approver Comments" },
    ];

    const rawSapDataColumns = [
        // { field: "sno", header: "SNo" },
        { field: "dataPoint", header: "Quantitative Data Point" },
        { field: "value", header: "Quantitative Value" },
        { field: "unitOfMeasure", header: "Unit of Measure" },
        { field: "periodFrom", header: "Reporting Date (From)" },
        { field: "periodTo", header: "Reporting Date (To)" },
        { field: "entity", header: "Reporting Entity" },
        { field: "syncDate", header: "Sync Date" },
    ];
    useEffect(() => {

        renderData()

    }, [])
    useEffect(() => {
        console.log(rawdata)
    }, [rawdata])

    // Log yearOption whenever it changes
    useEffect(() => {
        console.log("yearOption updated:", yearOption);
    }, [yearOption])
    // Store the current DataTable filters
    const [tableFilters, setTableFilters] = useState({
        title: { value: null, matchMode: "in" },
        dataPoint: { value: null, matchMode: "in" },
        entity: { value: null, matchMode: "in" },
        periodFrom: { value: null, matchMode: "in" },
        reportingPeriodFrom: { value: null, matchMode: "in" },
        status: { value: null, matchMode: "in" },
        reporter: { value: null, matchMode: "in" },
        reviewer: { value: null, matchMode: "in" },
        approver: { value: null, matchMode: "in" },
        unitOfMeasure: { value: null, matchMode: "in" }
        // Note: Do not add a null key here
    });

    // Function to update rawData without disturbing DataTable filters
    const updateDataWithFilters = (newData) => {
        // Simply update the data without trying to manipulate filters
        // The DataTable will maintain its own filter state
        setRawData(newData);

        // Update the table filters options based on the new data
        updateFilterOptions(newData);
    };

    // Function to update filter options based on current data
    const updateFilterOptions = (data) => {
        // This will be called whenever the data changes to update available filter options
        if (!data || !Array.isArray(data) || data.length === 0) return;

        // Get unique values for each filterable field
        const filterableFields = [
            "title", "dataPoint", "entity", "periodFrom",
            "reportingPeriodFrom", "status", "reporter",
            "reviewer", "approver", "unitOfMeasure"
        ];

        // Create a map of field -> unique values
        const uniqueValuesMap = {};

        filterableFields.forEach(field => {
            const uniqueValues = Array.from(
                new Set(data.map(item => item[field]))
            ).filter(item => item !== null && item !== undefined && item !== "");

            uniqueValuesMap[field] = uniqueValues;
        });

        // Store this for use in filter templates
        tempSelectionsMap.current.uniqueValuesMap = uniqueValuesMap;
    };

    // Function to preserve table filters when data changes
    const preserveTableFilters = (newData, currentFilters) => {
        // Create a new filters object
        const newFilters = {};

        // For each field in the current filters
        Object.entries(currentFilters).forEach(([field, filter]) => {
            // Skip the null field if it exists
            if (field === 'null') return;
            // If the filter has a value
            if (filter && filter.value && Array.isArray(filter.value) && filter.value.length > 0) {
                // Get unique values for this field from the new data
                const availableValues = Array.from(
                    new Set(newData.map(item => item[field]))
                ).filter(item => item !== null && item !== undefined && item !== "");

                // Find values that exist in both the current filter and the new data
                const validValues = filter.value.filter(value =>
                    availableValues.includes(value)
                );

                // If there are valid values, preserve the filter
                if (validValues.length > 0) {
                    newFilters[field] = {
                        value: validValues,
                        matchMode: filter.matchMode
                    };
                } else {
                    // Otherwise, reset the filter
                    newFilters[field] = { value: null, matchMode: "in" };
                }
            } else {
                // If the filter doesn't have a value, keep it as is
                newFilters[field] = { ...filter };
            }
        });

        return newFilters;
    };

    const updateDataByFilter = async (obj, value, obj2) => {
        let loc = { ...filter, [obj]: value }
        try {
            setLoad(true)

            // Save current table filters to preserve them if possible
            const currentTableFilters = { ...tableFilters };

            if (obj === 'year') {
                // Reset date filters when year changes
                loc.fromDate = null;
                loc.toDate = null;

                const year = yearOption.find(x => x.name === loc.year)
                console.log("Selected year object:", year);

                // Use the original startMonth and endMonth from the year object
                // This ensures we're sending the correct format to the API
                const startMonth = year?.startMonth || "Jan-2023";
                const endMonth = year?.endMonth || "Dec-2023";

                console.log("Using month range:", startMonth, endMonth);

                const promise2 = await APIServices.post(API.EnterpriseRawData_UP(admin.id), {
                    userId: user.id,
                    framework: loc.framework,
                    year: { startMonth, endMonth }
                })

                const filteredData = filterDataByTierAndLocationByLevel(promise2.data, rawsitelist, loc.country, loc.city, loc.site)
                console.log(promise2.data)

                // Update data with filter preservation
                const newData = filteredData?.filter(x => (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0));

                // Preserve table filters that are still valid with the new data
                const preservedFilters = preserveTableFilters(newData, currentTableFilters);
                console.log(preservedFilters)
                setTableFilters(preservedFilters);

                updateDataWithFilters(newData);

                setRawDataBk(filteredData)
                setLoad(false)
            } else if (obj === 'framework') {
                const year = yearOption.find(x => x.name === loc.year)
                console.log("Selected year object (framework):", year);

                // Use the original startMonth and endMonth from the year object
                // This ensures we're sending the correct format to the API
                const startMonth = year?.startMonth || "Jan-2023";
                const endMonth = year?.endMonth || "Dec-2023";

                console.log("Using month range (framework):", startMonth, endMonth);

                const promise2 = await APIServices.post(API.EnterpriseRawData_UP(admin.id), {
                    userId: user.id,
                    framework: loc.framework,
                    year: { startMonth, endMonth }
                })
                const promise3 = await APIServices.get(
                    API.SapCollection_UP(admin.id)
                );
                const promise4 = await APIServices.get(
                    API.DCF_Title_Only
                );
                const promise5 = await APIServices.post(
                    API.GetAssignedIndicator_UP(admin.id)
                );
                const assignedDcfids = promise5.data?.flatMap(x => x?.dcfIds || []).filter((a, b, c) => c.indexOf(a) === b)
                const assignedSapids = promise5.data?.flatMap(x => x?.sapIds || []).filter((a, b, c) => c.indexOf(a) === b)
                console.log(promise4.data, promise3.data)
                setAssignedDcf([...promise4.data.filter(x => assignedDcfids.includes(x.id)).map(i => ({ ...i, title: i.id + ' :' + i.title, formCategory: 1 })), ...promise3.data.filter(x => assignedSapids.includes(x.id)).map(i => ({ ...i, id: i.sapId, title: i.sapId + ' :' + i.title, formCategory: 2 }))])
                const filteredData = filterDataByTierAndLocationByLevel(promise2.data, rawsitelist, loc.country, loc.city, loc.site)

                // Update data with filter preservation
                const newData = filteredData?.filter(x => (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0));

                // Preserve table filters that are still valid with the new data
                const preservedFilters = preserveTableFilters(newData, currentTableFilters);
                setTableFilters(preservedFilters);

                updateDataWithFilters(newData);

                setRawDataBk(filteredData)
                setLoad(false)
            } else if (obj === 'location') {
                const locFilter = getLocationFilterValue(obj2, value, loc)
                loc = { ...loc, ...locFilter }
                console.log(loc)
                const year = yearOption.find(x => x.name === loc.year)
                console.log("Selected year object:", year);

                // Use the original startMonth and endMonth from the year object
                // This ensures we're sending the correct format to the API
                const startMonth = year?.startMonth || "Jan-2023";
                const endMonth = year?.endMonth || "Dec-2023";
                // Update data with filter preservation
                // const newData = filterDataByTierAndLocationByLevel(
                //     rawdatabk.filter(x => (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0)),
                //     rawsitelist,
                //     loc.country,
                //     loc.city,
                //     loc.site
                // );
                const promise2 = await APIServices.post(API.EnterpriseRawData_UP(admin.id), {
                    userId: user.id,
                    framework: loc.framework,
                    year: { startMonth, endMonth }
                })

                const filteredData = filterDataByTierAndLocationByLevel(promise2.data, rawsitelist, loc.country, loc.city, loc.site)
                console.log(promise2.data)

                // Update data with filter preservation
                const newData = filteredData?.filter(x => (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0));

                // Preserve table filters that are still valid with the new data
                const preservedFilters = preserveTableFilters(newData, currentTableFilters);
                setTableFilters(preservedFilters);

                updateDataWithFilters(newData);
                setRawDataBk(filteredData)
                setLoad(false)
            } else if (obj === 'fromDate' || obj === 'toDate') {
                console.log(`Date filter changed: ${obj} = ${value}`);
                console.log("Current filter state:", loc);

                try {
                    // Handle date filter changes
                    if (obj === 'fromDate') {
                        // If fromDate is later than toDate, reset toDate
                        if (loc.toDate !== null && loc.fromDate > loc.toDate) {
                            console.log(`Resetting toDate because fromDate (${loc.fromDate}) > toDate (${loc.toDate})`);
                            loc.toDate = null;
                        }
                    }

                    // Only apply date filters if both fromDate and toDate are selected
                    if (loc.fromDate !== null && loc.toDate !== null) {
                        console.log(`Applying date filter: fromDate=${loc.fromDate}, toDate=${loc.toDate}`);

                        // Get the selected year to include in the API call
                        const selectedYear = yearOption.find(y => y.name === loc.year);
                        console.log("Selected year for date filter:", selectedYear);

                        // Create a custom date range for the API call
                        // We need to include the selected year in the API call

                        // Get the original startMonth and endMonth strings from the selected year
                        const yearStartMonth = selectedYear?.startMonth || "Jan-2023";
                        const yearEndMonth = selectedYear?.endMonth || "Dec-2023";

                        // Extract the year part from the month strings (e.g., "2023" from "Apr-2023")
                        const yearMatch = yearStartMonth.match(/-(\d+)/);
                        const yearValue = yearMatch ? yearMatch[1] : new Date().getFullYear().toString();

                        // Create month names for the selected dates
                        const monthNames = [
                            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
                        ];

                        // Format the month strings in the same format as the original (e.g., "Apr-2023")
                        const fromMonth = loc.fromDate.getMonth();
                        const toMonth = loc.toDate.getMonth();

                        const formattedStartMonth = `${monthNames[fromMonth]}-${yearValue}`;
                        const formattedEndMonth = `${monthNames[toMonth]}-${yearValue}`;

                        console.log("Formatted month range:", formattedStartMonth, formattedEndMonth);

                        const apiParams = {
                            userId: user.id,
                            framework: loc.framework,
                            year: {
                                startMonth: formattedStartMonth,
                                endMonth: formattedEndMonth
                            }
                        };

                        console.log("API parameters:", apiParams);

                        // Make the API call
                        const promise2 = await APIServices.post(API.EnterpriseRawData_UP(admin.id), apiParams);
                        console.log("API response:", promise2);

                        if (!promise2 || !promise2.data) {
                            console.error("Invalid API response:", promise2);
                            setLoad(false);
                            return;
                        }

                        const filteredData = filterDataByTierAndLocationByLevel(promise2.data, rawsitelist, loc.country, loc.city, loc.site);
                        // console.log("Filtered data count:", filteredData.length);

                        // Update data with filter preservation
                        const newData = filteredData?.filter(x => (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0));
                        console.log("New data count after source/form filter:", newData.length);

                        // Preserve table filters that are still valid with the new data
                        const preservedFilters = preserveTableFilters(newData, currentTableFilters);
                        setTableFilters(preservedFilters);

                        updateDataWithFilters(newData);
                        setRawDataBk(filteredData);
                        setLoad(false);
                    } else {
                        // If not both dates are selected, just update the filter state
                        console.log("Not applying filter yet - need both fromDate and toDate");
                        setLoad(false);
                    }
                } catch (error) {
                    console.error("Error in date filter handling:", error);
                    setLoad(false);
                }
            } else {
                // Update data with filter preservation
                const newData = rawdatabk.filter(x => (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0));

                // Preserve table filters that are still valid with the new data
                const preservedFilters = preserveTableFilters(newData, currentTableFilters);
                setTableFilters(preservedFilters);

                updateDataWithFilters(newData);

                setLoad(false)
            }
            console.log(loc)
            // Update filter state
            setFilter(loc)

            // Update table filter options based on the new data
            if (dt.current) {
                // Force DataTable to refresh its filters
                dt.current.filter(null, null, 'equals');
            }
        } catch (error) {
            console.error('Error in updateDataByFilter:', error);
            setLoad(false);
        }
    }
    const getLocationFilterValue = (obj, val, filter) => {

        let item = { ...filter, [obj]: val }
        let country_list = [{ name: 'All Countries', id: 0 }]
        let city_list = [{ name: 'All Regions', id: 0 }]
        let location_list = [{ name: 'All Business Unit', id: 0 }]
        rawsitelist.forEach((country) => {
            country_list.push({ name: country.name, id: country.id })
            if (country.id === item.country || item.country === 0) {
                if (country.locationTwos) {
                    country.locationTwos.forEach((city) => {
                        city_list.push({ name: city.name, id: city.id })
                        if (city.id === item.city || item.city === 0) {
                            if (city.locationThrees) {
                                city.locationThrees.forEach((site) => {
                                    location_list.push({ name: site.name, id: site.id })

                                })
                            }
                        }
                    })

                }

            }

        })
        if (obj === 'country') {
            item.city = val === 0 ? null : 0

            item.site = null
        }
        else if (obj === 'city') {

            item.site = val === 0 ? null : 0
        }

        setLocList((prev) => ({ ...prev, 'country': country_list, 'city': city_list, 'location': location_list }))
        return { country: item.country, city: item.city, site: item.site }
    }
    const renderData = async () => {
        let yrOptions = getFiscalYearsFromStartDate(
            admin.information.startdate, fymonth
        );
        console.log("Year options from getFiscalYearsFromStartDate:", yrOptions);
        setYearOption(yrOptions)
        try {

            if (yrOptions.length) {

                let uriStringLoc = {
                    include: [
                        {
                            relation: "locationTwos",
                            scope: { include: [{ relation: "locationThrees" }] },
                        },
                    ],
                };
                const promise0 = APIServices.get(API.Report_Name_Twos)
                const promise1 = APIServices.post(
                    API.LocationByRoles(admin.id), { userId: user.id, roles: [2] }
                );
                const promise2 = APIServices.post(API.EnterpriseRawData_UP(admin.id), { userId: user.id, year: { startMonth: yrOptions.slice(-1)?.[0]?.startMonth, endMonth: yrOptions.slice(-1)?.[0]?.endMonth } })
                const promise3 = APIServices.get(
                    API.SapCollection_UP(admin.id)
                );
                const promise4 = APIServices.get(
                    API.DCF_Title_Only
                );
                const promise5 = APIServices.post(
                    API.GetAssignedIndicator_UP(admin.id)
                );
                Promise.all([promise0, promise1, promise2, promise3, promise4, promise5]).then((values) => {
                    console.log(values[2].data)
                    let allframework = values[0].data.filter((i) => { return admin.information.report.includes(i.id) })
                    setFilter((prev) => ({ ...prev, year: yrOptions.slice(-1)[0].name, framework: allframework.map(x => x.title) }))
                    const shapedSite = values[1]?.data
                    setRawsitelist(shapedSite)
                    setLocList({ country: [{ name: 'All Countries', id: 0 }, ...shapedSite.map(location => ({ name: location.name, id: location.id }))] });

                    setAssFramework(allframework)
                    const assignedDcfids = values[5].data?.flatMap(x => x?.dcfIds || []).filter((a, b, c) => c.indexOf(a) === b)
                    const assignedSapids = values[5].data?.flatMap(x => x?.sapIds || []).filter((a, b, c) => c.indexOf(a) === b)

                    setAssignedDcf([...values[4].data.filter(x => assignedDcfids.includes(x.id)).map(i => ({ ...i, title: i.id + ' :' + i.title, formCategory: 1 })), ...values[3].data.filter(x => assignedSapids.includes(x.id)).map(i => ({ ...i, id: i.sapId, title: i.sapId + ' :' + i.title, formCategory: 2 }))])
                    console.log(assignedDcfids, values[5].data)
                    setAssignedSap(values[3].data.map(i => ({ ...i, id: i.sapId, title: i.sapId + ' :' + i.title, formCategory: 2 })))
                    // Update data with filter preservation
                    updateDataWithFilters(values[2].data);
                    setRawDataBk(values[2].data);
                }).then(() => {
                    setLoad(false);
                })

            }
        } catch {

        }
    }
    const bodyTemplate = (rowData) => {
        if (
            rowData.status === "Approved" ||
            rowData.status === "Pending Approval" ||
            rowData?.status === "Pending Review" ||
            rowData?.status === "Draft" ||
            rowData?.status === "Pending Submission"
        ) {
            return (
                <Badge
                    style={{
                        minWidth: '120px',
                        width: 'auto',
                        textAlign: 'center',
                        whiteSpace: 'nowrap'
                    }}
                    value={rowData.status}
                    severity={
                        rowData.status === "Approved"
                            ? "success"
                            : rowData.status === "Pending Approval"
                                ? "info"
                                : rowData?.status === "Pending Review"
                                    ? "info"
                                    : rowData?.status === "Pending Submission"
                                        ? "warning"
                                        : rowData?.status === "Draft"
                                            ? "secondary"
                                            : null
                    }
                />
            );
        } else {
            console.log(rowData)
        }

        return null;
    };
    // Handle filter changes and update the tableFilters state
    const handleFilterChange = (fieldName, filterApplyCallback) => (selectedValues) => {
        const safeValues = Array.isArray(selectedValues) ? selectedValues : [];

        // Update our tableFilters state first
        setTableFilters(prev => {
            const newFilters = {
                ...prev,
                [fieldName]: { value: safeValues, matchMode: "in" }
            };

            // Then apply the filter using the callback
            // This ensures the filter is applied with the correct values
            if (filterApplyCallback) {
                setTimeout(() => {
                    filterApplyCallback(safeValues);
                }, 0);
            }

            return newFilters;
        });
    };

    // Create a map to store temporary selections for each field
    const tempSelectionsMap = useRef({});

    const filterTemplate = (fieldName) => {
        return (options) => {
            // Get unique options from the current data state
            // First try to get from our cached uniqueValuesMap
            let uniqueOptions = [];

            if (tempSelectionsMap.current.uniqueValuesMap &&
                tempSelectionsMap.current.uniqueValuesMap[fieldName]) {
                uniqueOptions = tempSelectionsMap.current.uniqueValuesMap[fieldName];
            } else {
                // Fallback to calculating from current data
                uniqueOptions = Array.from(
                    new Set(rawdata?.filter(x => x.formCategory === filter.source).map((item) => item[fieldName]))
                ).filter((item) => item !== null && item !== undefined && item !== "");
            }

            // Convert to the format expected by MultiSelect
            const allOptions = uniqueOptions.map(option => ({
                name: option,
                id: option,
            }));

            // Check if we need to filter options based on other selected filters
            // This makes filters interdependent
            const filteredOptions = filterOptionsByOtherSelections(allOptions, fieldName);

            return (
                <MultiSelect
                    panelClassName="hidefilter"
                    value={options.value}
                    options={filteredOptions}
                    optionLabel="name"
                    optionValue="id"
                    filter
                    placeholder="Any"
                    className="p-column-filter"
                    maxSelectedLabels={1}
                    style={{ minWidth: "14rem" }}
                    onChange={(e) => {
                        // Store the current selection in the map
                        tempSelectionsMap.current[fieldName] = e.value;

                        // Update the value in the component (this doesn't apply the filter yet)
                        // This ensures the checkboxes show as checked
                        options.filterCallback(e.value, false);
                    }}
                    showApplyButton={true}
                    showClearButton={true}
                    onApply={() => {
                        // Apply the filter only when the Apply button is clicked
                        const currentSelection = tempSelectionsMap.current[fieldName] || [];

                        // Apply the filter
                        handleFilterChange(fieldName, options.filterApplyCallback)(currentSelection);

                        // Update dropdown filters to reflect table filters
                        updateDropdownFiltersFromTableFilters();
                    }}
                />
            );
        };
    };

    // Function to filter options based on other selected filters
    const filterOptionsByOtherSelections = (options, currentField) => {
        // If no table filters are active, return all options
        const hasActiveFilters = Object.entries(tableFilters).some(
            ([field, filter]) => field !== currentField &&
                filter.value &&
                Array.isArray(filter.value) &&
                filter.value.length > 0
        );

        if (!hasActiveFilters) return options;

        // Get the current filtered data from the DataTable
        let filteredData = [];

        try {
            if (dt.current && dt.current.getVirtualScroller) {
                filteredData = dt.current.getVirtualScroller().props.items || [];
            }
        } catch (err) {
            console.error('Error getting filtered data for options:', err);
            // Fallback to all data
            filteredData = rawdata.filter(x => x.formCategory === filter.source);
        }

        // Get unique values for the current field from the filtered data
        const filteredValues = Array.from(
            new Set(filteredData.map(item => item[currentField]))
        ).filter(item => item !== null && item !== undefined && item !== "");

        // Return only options that exist in the filtered data
        return options.filter(option => filteredValues.includes(option.id));
    };

    // Function to get available data sources based on current data
    const getAvailableDataSources = () => {
        // Default options
        const allOptions = [
            { label: "DCF", value: 1 },
            { label: "SAP", value: 2 }
        ];

        // If we have no data yet, return all options
        if (!rawdatabk || rawdatabk.length === 0) {
            return allOptions;
        }

        // Get unique form categories from the current data
        const availableCategories = Array.from(
            new Set(rawdatabk.map(item => item.formCategory))
        ).filter(Boolean);

        // Filter options to only include sources that have data
        return allOptions.filter(option =>
            availableCategories.includes(option.value)
        );
    };

    // Helper function to parse month strings like "Apr-2023" to numeric values
    const parseMonthString = (monthStr, defaultValue) => {
        if (!monthStr) return defaultValue;

        try {
            // Parse the month string (e.g., "Apr-2023")
            const monthMatch = monthStr.match(/([A-Za-z]+)-\d+/);
            if (monthMatch && monthMatch[1]) {
                const monthName = monthMatch[1];
                const monthMap = {
                    "Jan": 1, "Feb": 2, "Mar": 3, "Apr": 4, "May": 5, "Jun": 6,
                    "Jul": 7, "Aug": 8, "Sep": 9, "Oct": 10, "Nov": 11, "Dec": 12
                };
                return monthMap[monthName] || defaultValue;
            }
        } catch (error) {
            console.error("Error parsing month string:", error);
        }

        return defaultValue;
    };

    // Function to get date constraints for Calendar components based on selected year
    const getDateConstraints = () => {
        console.log("Getting date constraints for Calendar");
        console.log("Current filter state:", filter);
        console.log("Year options:", yearOption);

        // Default constraints (full year)
        let minDate = null;
        let maxDate = null;

        // If no year is selected, return null constraints
        if (!filter.year || filter.year === 0) {
            console.log("No year selected, returning null constraints");
            return { minDate, maxDate };
        }

        // Find the selected year object
        const selectedYear = yearOption.find(y => y.name === filter.year);
        console.log("Selected year object:", selectedYear);

        if (!selectedYear) {
            console.log("Year not found:", filter.year);
            return { minDate, maxDate }; // Return null constraints if year not found
        }

        // Parse the startMonth and endMonth strings (e.g., "Apr-2023")
        try {
            // Extract month and year from startMonth
            const startMonthMatch = selectedYear.startMonth?.match(/([A-Za-z]+)-(\d+)/);
            if (startMonthMatch && startMonthMatch[1] && startMonthMatch[2]) {
                const monthName = startMonthMatch[1];
                const year = parseInt(startMonthMatch[2]);

                // Convert month name to month index (0-11)
                const monthMap = {
                    "Jan": 0, "Feb": 1, "Mar": 2, "Apr": 3, "May": 4, "Jun": 5,
                    "Jul": 6, "Aug": 7, "Sep": 8, "Oct": 9, "Nov": 10, "Dec": 11
                };

                const monthIndex = monthMap[monthName] || 0;

                // Create Date object for the first day of the start month
                minDate = new Date(year, monthIndex, 1);
                console.log("Min date:", minDate);
            }

            // Extract month and year from endMonth
            const endMonthMatch = selectedYear.endMonth?.match(/([A-Za-z]+)-(\d+)/);
            if (endMonthMatch && endMonthMatch[1] && endMonthMatch[2]) {
                const monthName = endMonthMatch[1];
                const year = parseInt(endMonthMatch[2]);

                // Convert month name to month index (0-11)
                const monthMap = {
                    "Jan": 0, "Feb": 1, "Mar": 2, "Apr": 3, "May": 4, "Jun": 5,
                    "Jul": 6, "Aug": 7, "Sep": 8, "Oct": 9, "Nov": 10, "Dec": 11
                };

                const monthIndex = monthMap[monthName] || 11;

                // Create Date object for the last day of the end month
                const lastDay = new Date(year, monthIndex + 1, 0).getDate();
                maxDate = new Date(year, monthIndex, lastDay);
                console.log("Max date:", maxDate);
            }
        } catch (error) {
            console.error("Error parsing date constraints:", error);
        }

        return { minDate, maxDate };
    };

    // Function to get available form options based on current data
    const getAvailableFormOptions = () => {
        // Start with all assigned DCFs for the current source
        const allOptions = assignedDcf.filter(x => x.formCategory === filter.source);

        // If we have no data yet, return all options
        if (!rawdatabk || rawdatabk.length === 0) {
            return allOptions;
        }

        // Get unique form IDs from the current data
        const availableFormIds = Array.from(
            new Set(rawdatabk
                .filter(x => x.formCategory === filter.source)
                .map(item => item.formId))
        ).filter(Boolean);

        // Filter options to only include forms that have data
        return allOptions.filter(option =>
            availableFormIds.includes(option.id) || option.id === 0
        );
    };

    // Function to update dropdown filters based on table filters
    const updateDropdownFiltersFromTableFilters = () => {
        // Get the current filtered data
        let filteredData = [];

        try {
            if (dt.current && dt.current.getVirtualScroller) {
                filteredData = dt.current.getVirtualScroller().props.items || [];
            } else {
                // Fallback to all data
                filteredData = rawdata.filter(x => x.formCategory === filter.source);
            }
        } catch (err) {
            console.error('Error getting filtered data for dropdown update:', err);
            return;
        }

        // Update location filters based on filtered data
        if (filteredData.length > 0) {
            // Get unique countries, cities, and sites from filtered data
            const countries = Array.from(new Set(filteredData.map(item => {
                // Extract country ID from entity field if possible
                const entity = item.entity || '';
                // This is a simplification - you'll need to adapt based on your actual data structure
                const match = entity.match(/Country: (\d+)/);
                return match ? parseInt(match[1]) : null;
            }))).filter(Boolean);

            // If we have country information in the filtered data, update the dropdown options
            if (countries.length > 0) {
                // This is just an example - you'll need to adapt to your actual data structure
                console.log('Filtered countries:', countries);
                // You would update your location dropdowns here based on the filtered data
            }
        }
    };

    // Function to export data to Excel
    const exportExcel = (data, columns) => {
        if (!data || data.length === 0) {
            alert("No data to export");
            return;
        }

        // Generate Excel file name based on current filters
        const fileName = `Enterprise_Data_${filter.source === 1 ? 'DCF' : 'SAP'}_${filter.year}_${new Date().toISOString().slice(0, 10)}.xlsx`;

        // Export data using xlsx-populate
        XlsxPopulate.fromBlankAsync()
            .then(workbook => {
                // Get the sheet
                const sheet = workbook.sheet(0);

                // Style the header row
                const headerStyle = {
                    bold: true,
                    fill: { type: 'solid', color: '4472C4' },
                    fontColor: 'FFFFFF'
                };

                // Add headers
                if (Array.isArray(columns)) {
                    for (let colIndex = 0; colIndex < columns.length; colIndex++) {
                        const col = columns[colIndex];
                        if (col) {
                            try {
                                const cell = sheet.cell(1, colIndex + 1);
                                cell.value(col.header || `Column ${colIndex + 1}`);

                                // Apply header style
                                cell.style({
                                    bold: headerStyle.bold,
                                    fill: headerStyle.fill,
                                    fontColor: headerStyle.fontColor
                                });
                            } catch (e) {
                                console.error(`Error setting header for column ${colIndex}:`, e);
                            }
                        }
                    }
                } else {
                    console.error('Columns is not an array:', columns);
                    throw new Error('Invalid columns format');
                }

                // Add data
                if (Array.isArray(data)) {
                    // Safely iterate through data
                    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
                        const row = data[rowIndex];
                        if (row) {
                            // Safely iterate through columns
                            if (Array.isArray(columns)) {
                                for (let colIndex = 0; colIndex < columns.length; colIndex++) {
                                    const col = columns[colIndex];
                                    if (col && typeof col.field === 'string') {
                                        // Get cell value safely
                                        let cellValue = '';
                                        try {
                                            cellValue = row[col.field] !== undefined && row[col.field] !== null
                                                ? row[col.field]
                                                : '';
                                        } catch (e) {
                                            console.error(`Error accessing field ${col.field} in row ${rowIndex}:`, e);
                                        }

                                        // Set cell value safely
                                        try {
                                            sheet.cell(rowIndex + 2, colIndex + 1).value(cellValue);
                                        } catch (e) {
                                            console.error(`Error setting cell value at (${rowIndex + 2}, ${colIndex + 1}):`, e);
                                        }
                                    }
                                }
                            } else {
                                console.error('Columns is not an array:', columns);
                            }
                        }
                    }
                } else {
                    console.error('Data is not an array:', data);
                    throw new Error('Invalid data format');
                }

                // Auto-size columns
                if (Array.isArray(columns)) {
                    for (let colIndex = 0; colIndex < columns.length; colIndex++) {
                        try {
                            const col = sheet.column(colIndex + 1);
                            if (col) {
                                col.width(20); // Set a reasonable default width
                            }
                        } catch (e) {
                            console.error(`Error setting width for column ${colIndex}:`, e);
                        }
                    }
                }

                // Return the workbook as a blob
                return workbook.outputAsync();
            })
            .then(blob => {
                // Create a download link
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            })
            .catch(err => {
                console.error('Error exporting data:', err);
                alert('Error exporting data. Please try again.');
            });
    };
    const renderDataTable = (data, columns) => (
        <>
            <div className="d-flex justify-content-end mb-2">
                <Button
                    disabled={data?.length === 0}
                    onClick={() => {
                        console.log('Export button clicked');

                        // Get the filtered data from the DataTable
                        let filteredData = [...data]; // Default to all data

                        if (dt.current) {
                            try {
                                // Try to get the filtered data directly from the DataTable
                                if (dt.current.getVirtualScroller &&
                                    dt.current.getVirtualScroller() &&
                                    dt.current.getVirtualScroller().props &&
                                    Array.isArray(dt.current.getVirtualScroller().props.items)) {

                                    filteredData = dt.current.getVirtualScroller().props.items;
                                    console.log('Got filtered data from virtual scroller:', filteredData.length);
                                }
                            } catch (err) {
                                console.error('Error getting filtered data:', err);

                                // Fallback: manually apply filters
                                try {
                                    // Check if we have any active filters
                                    const hasActiveFilters = Object.values(tableFilters).some(
                                        filter => filter.value && Array.isArray(filter.value) && filter.value.length > 0
                                    );

                                    if (hasActiveFilters) {
                                        console.log('Applying filters manually using tableFilters');

                                        // Filter the data manually based on tableFilters
                                        filteredData = data.filter(row => {
                                            // Check each filter
                                            for (const [field, filter] of Object.entries(tableFilters)) {
                                                if (filter && filter.value && Array.isArray(filter.value) && filter.value.length > 0) {
                                                    // Skip if the row doesn't have this field
                                                    if (!row[field]) return false;

                                                    // For "in" match mode, check if the value is in the filter values
                                                    if (filter.matchMode === "in") {
                                                        if (!filter.value.includes(row[field])) {
                                                            return false;
                                                        }
                                                    }
                                                }
                                            }
                                            return true;
                                        });

                                        console.log('Filtered data length after manual filtering:', filteredData.length);
                                    }
                                } catch (filterErr) {
                                    console.error('Error applying filters manually:', filterErr);
                                }
                            }
                        }

                        // Make sure we have valid data
                        if (!Array.isArray(filteredData) || filteredData.length === 0) {
                            alert('No data to export');
                            return;
                        }

                        // Export the data
                        try {
                            console.log('Exporting data with columns:', columns);
                            exportExcel(filteredData, columns);
                        } catch (err) {
                            console.error('Error during export:', err);
                            alert('Error exporting data. Please try again.');
                        }
                    }}
                    label="Export Report"
                    icon="pi pi-download"
                    className="p-button-primary mr-3"
                />

            </div>
            <DataTable
                ref={dt}
                value={data}
                paginator
                rows={10}
                scrollable
                className="custom-datatable"
                removableSort
                rowsPerPageOptions={[10, 20, 50, 200, 300, 500, 1000]}
                loading={load}
                filters={tableFilters}
                onFilter={(e) => {
                    // Create a copy of the filters object
                    const cleanedFilters = { ...e.filters };

                    // Remove the null key if it exists
                    if (cleanedFilters.hasOwnProperty('null')) {
                        delete cleanedFilters['null'];
                    }

                    // Update our tableFilters state with the cleaned filters
                    setTableFilters(cleanedFilters);
                    console.log('Filter applied (cleaned):', cleanedFilters);

                    // Update dropdown filters based on table filters
                    updateDropdownFiltersFromTableFilters();
                }}
            >
                {columns.map((col, index) => {
                    if (
                        col.field === "title" ||
                        col.field === "label" ||
                        col.field === "dataPoint" ||
                        col.field === "entity" ||
                        col.field === "periodFrom" ||
                        col.field === "reportingPeriodFrom" ||
                        col.field === "status" ||
                        col.field === "reporter" ||
                        col.field === "reviewer" ||
                        col.field === "approver" ||
                        col.field === "unitOfMeasure" ||
                        col.field === "efkey"
                    ) {
                        return (
                            <Column
                                key={index}
                                field={col.field}
                                header={col.header}
                                filter
                                filterElement={filterTemplate(col.field)}
                                showFilterMatchModes={false}
                                showFilterMenuOptions={false}
                                filterField={col.field} // Ensure we use the correct field name for filtering


                                body={col.field === "status" ? bodyTemplate : (rowData) => { return <span className={col.field === 'title' && 'fw-6 cur-pointer clr-navy fs-14 text-underline'} onClick={() => { if (col.field === 'title') { window.open(window.origin + '/data_input_status/' + rowData.dcfId + '/' + rowData.submitId) } }}  >{rowData[col.field]} </span> }}
                                sortable={
                                    col.field === "periodFrom" ||
                                    col.field === "reportingPeriodFrom"
                                }
                            // showApplyButton={false}
                            // showAddButton={false}
                            // showClearButton={false}
                            />
                        );
                    }
                    return (
                        <Column
                            key={index}
                            field={col.field}
                            header={col.header}
                            sortable={col.field === "value"}
                        />
                    );
                })}
            </DataTable>
        </>
    );

    return (
        <div>
            {/* First row: Year, Month filters, and Entity filters */}
            <Box display="flex" gap={2} padding={2} alignItems="flex-start" flexWrap="wrap">
                <div style={{ minWidth: '15%', display: 'flex', flexDirection: 'column' }}>
                    <label htmlFor="reporting-period-dropdown" style={{ marginBottom: '4px', fontSize: '0.875rem' }}>
                        Reporting Year
                    </label>
                    <Dropdown
                        id="reporting-period-dropdown"
                        value={filter.year}
                        disabled={load}
                        options={[...yearOption]}
                        optionValue="name"
                        optionLabel="label"
                        onChange={(e) => updateDataByFilter('year', e.value)}
                        placeholder="Select Reporting Year"
                    />
                </div>

                <div style={{ minWidth: '12%', display: 'flex', flexDirection: 'column' }}>
                    <label htmlFor="from-date-calendar" style={{ marginBottom: '4px', fontSize: '0.875rem' }}>
                        From Month
                    </label>
                    <Calendar
                        id="from-date-calendar"
                        value={filter.fromDate}
                        onChange={(e) => {
                            console.log("From Date selected:", e.value);
                            // Set loading state before updating filter
                            setLoad(true);
                            // Use setTimeout to ensure UI updates before filter processing
                            setTimeout(() => {
                                updateDataByFilter('fromDate', e.value);
                            }, 100);
                        }}
                        disabled={load || !filter.year || filter.year === 0}
                        dateFormat="MM/yy"
                        view="month"
                        minDate={getDateConstraints().minDate}
                        maxDate={getDateConstraints().maxDate}
                        placeholder="Select From Month"
                        showIcon
                        readOnlyInput
                    />
                </div>

                <div style={{ minWidth: '12%', display: 'flex', flexDirection: 'column' }}>
                    <label htmlFor="to-date-calendar" style={{ marginBottom: '4px', fontSize: '0.875rem' }}>
                        To Month
                    </label>
                    <Calendar
                        id="to-date-calendar"
                        value={filter.toDate}
                        onChange={(e) => {
                            console.log("To Date selected:", e.value);
                            // Set loading state before updating filter
                            setLoad(true);
                            // Use setTimeout to ensure UI updates before filter processing
                            setTimeout(() => {
                                updateDataByFilter('toDate', e.value);
                            }, 100);
                        }}
                        disabled={load || !filter.fromDate}
                        dateFormat="MM/yy"
                        view="month"
                        minDate={filter.fromDate || getDateConstraints().minDate}
                        maxDate={getDateConstraints().maxDate}
                        placeholder="Select To Month"
                        showIcon
                        readOnlyInput
                    />
                </div>

                <div style={{ minWidth: '40%', display: 'flex', flexDirection: 'column' }}>
                    <div style={{ display: 'flex', gap: '10px' }}>
                        <div style={{ display: 'flex', flexDirection: 'column', minWidth: '150px' }}>
                            <label style={{ marginBottom: '4px', fontSize: '0.875rem' }}>Country</label>
                            <Dropdown
                                disabled={load}
                                value={filter.country}
                                options={locList.country}
                                optionLabel="name"
                                optionValue="id"
                                onChange={(e) => { updateDataByFilter('location', e.value, 'country') }}
                                placeholder="Select Country"
                            />
                        </div>

                        {filter.country !== 0 && (
                            <div style={{ display: 'flex', flexDirection: 'column', minWidth: '150px' }}>
                                <label style={{ marginBottom: '4px', fontSize: '0.875rem' }}>Region</label>
                                <Dropdown
                                    disabled={load}
                                    value={filter.city}
                                    options={locList.city}
                                    optionLabel="name"
                                    optionValue="id"
                                    onChange={(e) => { updateDataByFilter('location', e.value, 'city') }}
                                    placeholder="Select Region"
                                />
                            </div>
                        )}

                        {filter.country !== 0 && filter.city !== 0 && (
                            <div style={{ display: 'flex', flexDirection: 'column', minWidth: '180px' }}>
                                <label style={{ marginBottom: '4px', fontSize: '0.875rem' }}>Business Unit</label>
                                <Dropdown
                                    disabled={load}
                                    value={filter.site}
                                    options={locList.location}
                                    optionLabel="name"
                                    optionValue="id"
                                    onChange={(e) => { updateDataByFilter('location', e.value, 'site') }}
                                    placeholder="Select Business Unit"
                                />
                            </div>
                        )}
                    </div>
                </div>
            </Box>

            {/* Second row: Framework, Data Source, and ID filters */}
            <Box display="flex" gap={2} padding={2} alignItems="flex-start" flexWrap="wrap" sx={{ mt: -2 }}>
                <div style={{ minWidth: '20%', display: 'flex', flexDirection: 'column' }}>
                    <label htmlFor="category-dropdown" style={{ marginBottom: '4px', fontSize: '0.875rem' }}>
                        Framework
                    </label>
                    <MultiSelect
                        disabled={filter.source === 2 || load}
                        display="chip"
                        style={{ width: 300 }}
                        value={filter.framework}
                        onChange={(e) => updateDataByFilter('framework', e.value)}
                        options={assFramework}
                        optionLabel="title"
                        optionValue="title"
                        filter={true}
                        placeholder="Select"
                        panelClassName={'hidefilter'}
                    />
                </div>

                <div style={{ minWidth: '15%', display: 'flex', flexDirection: 'column' }}>
                    <label htmlFor="datasource-dropdown" style={{ marginBottom: '4px', fontSize: '0.875rem' }}>
                        Data Source
                    </label>
                    <Dropdown
                        id="data-source"
                        disabled={load}
                        value={filter.source}
                        filter
                        options={getAvailableDataSources()}
                        onChange={(e) => updateDataByFilter('source', e.value)}
                        placeholder="Select Data Source"
                    />
                </div>

                <div style={{ minWidth: '40%', display: 'flex', flexDirection: 'column' }}>
                    <label htmlFor="dcf-dropdown" style={{ marginBottom: '4px', fontSize: '0.875rem' }}>
                        Select ID
                    </label>
                    <Dropdown
                        disabled={load}
                        id="dcf-dropdown"
                        value={filter.form}
                        filter
                        optionLabel="title"
                        optionValue="id"
                        options={[
                            { title: "All", id: 0 },
                            ...getAvailableFormOptions(),
                        ]}
                        onChange={(e) => updateDataByFilter('form', e.value)}
                        placeholder="Select Id"
                    />
                </div>
            </Box>

            {renderDataTable(rawdata.filter(x => x.formCategory === filter.source), filter.source === 1 ? rawDcfDataColumns : rawSapDataColumns)}

        </div>
    )
}