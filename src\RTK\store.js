

import userProfile from "./Login/userProfile";
import { configureStore } from "@reduxjs/toolkit";
import { createLogger } from "redux-logger";
import { combineReducers } from 'redux'
import {
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { createReliableStorage } from '../utils/reliableStorage'
import pendingData from "./Background/pendingData";
import userProfileList from "./Background/userProfileList";
import siteList from "./Background/siteList";
import emissionFactor from "./Background/emissionFactor";
import RFDCFLibrary from "./Background/RFDCFLibrary";
// Use reliable storage that handles quota errors gracefully
let persistStorage;
try {
  persistStorage = createReliableStorage();
} catch (error) {
  console.warn('Reliable storage creation failed, using default storage:', error);
  persistStorage = storage;
}

const persistConfig = {
  key: 'root',
  version: 1,
  storage: persistStorage,
  // Only persist essential user data, exclude large API responses
  whitelist: ['user'], // Only persist user profile data
  // This prevents large data from being stored in localStorage
  debug: process.env.NODE_ENV === 'development',
}
const rootReducer = combineReducers({
  user: userProfile, userlist: userProfileList, sitelist: siteList,library: RFDCFLibrary, emissionfactor: emissionFactor, pendingdata: pendingData

})

// Create persisted reducer with error handling
let persistedReducer;
try {
  persistedReducer = persistReducer(persistConfig, rootReducer);
} catch (error) {
  console.error('Failed to create persisted reducer, using regular reducer:', error);
  persistedReducer = rootReducer;
}

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
    },
  }).concat(createLogger()),
  devTools: process.env.NODE_ENV === 'development'
})


export default store