import React from "react";

const PrincipleThree = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          PRINCIPLE 3- BUSINESSES SHOULD RESPECT AND PROMOTE THE WELL-BEING OF
          ALL EMPLOYEES, INCLUDING THOSE IN THEIR VALUE CHAINS
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. <br></br> a. Details of measures for the well-being of employees:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th colSpan={13} style={{ textAlign: "center" }}>
                % Of employees covered by
              </th>
            </tr>
            <tr>
              <th rowSpan={2}>Category</th>
              <th rowSpan={2}>Total (A)</th>
              <th colSpan={2}>Health insurance</th>
              <th colSpan={2}>Accident insurance</th>
              <th colSpan={2}>Maternity benefits</th>
              <th colSpan={2}>Paternity benefits</th>
              <th colSpan={2}>Daycare facilities</th>
            </tr>
            <tr>
              <th>No. (B)</th>
              <th>% (B/A)</th>
              <th>No. (C)</th>
              <th>% (C/A)</th>
              <th>No. (D)</th>
              <th>% (D/A)</th>
              <th>No. (E)</th>
              <th>% (E/A)</th>
              <th>No. (F)</th>
              <th>% (F/A)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={13} style={{ fontWeight: "bold" }}>
                PERMANENT EMPLOYEES
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td colSpan={13} style={{ fontWeight: "bold" }}>
                OTHER THAN PERMANENT EMPLOYEES
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td colSpan={12}>Not Applicable</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          b. Details of measures for the well-being of workers:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th colSpan={13} style={{ textAlign: "center" }}>
                % Of employees covered by
              </th>
            </tr>
            <tr>
              <th rowSpan={2}>Category</th>
              <th rowSpan={2}>Total (A)</th>
              <th colSpan={2}>Health insurance</th>
              <th colSpan={2}>Accident insurance</th>
              <th colSpan={2}>Maternity benefits</th>
              <th colSpan={2}>Paternity benefits</th>
              <th colSpan={2}>Daycare facilities</th>
            </tr>
            <tr>
              <th>No. (B)</th>
              <th>% (B/A)</th>
              <th>No. (C)</th>
              <th>% (C/A)</th>
              <th>No. (D)</th>
              <th>% (D/A)</th>
              <th>No. (E)</th>
              <th>% (E/A)</th>
              <th>No. (F)</th>
              <th>% (F/A)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={13} style={{ fontWeight: "bold" }}>
                PERMANENT EMPLOYEES
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td colSpan={13} style={{ fontWeight: "bold" }}>
                OTHER THAN PERMANENT EMPLOYEES
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          c. Spending on measures towards the well-being of employees and
          workers (including permanent and other than permanent) in the
          following format –
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>&nbsp;</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Spent towards Wellbeing which include Insurance premium
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Details of retirement benefits for the current and previous
          financial year.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }} rowSpan={2}>
                Benefits
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2024-25
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2023-24
              </th>
            </tr>
            <tr>
              <th>No. of employees covered (as a % of total employees)</th>
              <th>No. of workers covered (as a % of total workers)</th>
              <th>Deducted &amp; deposited with the authority (Yes/No/N.A.)</th>
              <th>No. of employees covered (as a % of total employees)</th>
              <th>No. of workers covered (as a % of total workers)</th>
              <th>Deducted and deposited with the authority (Yes/No/N.A.)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>PF</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Gratuity</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>ESI</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Accessibility of workplaces Are the premises/offices of the entity
          accessible to differently-abled employees and workers, as per the
          requirements of the Rights of Persons with Disabilities Act, 2016
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. Does the entity have an equal opportunity policy as per the Rights
          of Persons with Disabilities Act, 2016? If so, provide a web link to
          the policy.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          5. Return to work and retention rates of permanent employees that took
          parental leave.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th colSpan={3} style={{ textAlign: "center" }}>
                Permanent employees
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                Permanent Workers
              </th>
            </tr>
            <tr>
              <th>Gender</th>
              <th>Return to work rate</th>
              <th>Retention rate</th>
              <th>Return to work rate</th>
              <th>Retention rate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "normal" }}>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          6. Is there a mechanism available to receive and redress grievances
          for the following categories of employees and workers? If yes, give
          details of the mechanism in brief.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>&nbsp;</th>
              <th>
                Yes/No (If Yes, then give details of the mechanism in brief)
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>Permanent Employees</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Permanent Workers</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Other than Permanent Workers
              </td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          7. Membership of employees in association(s) or unions recognised by
          the listed entity:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2} style={{ width: "15%" }}>
                Category
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2024-25
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2023-24
              </th>
            </tr>
            <tr>
              <th>Total employees/workers in respective category (A)</th>
              <th>
                No. of employees/workers in respective category, who are part of
                association(s) or Union (B)
              </th>
              <th>% (B/A)</th>
              <th>Total employees/workers in respective category (C)</th>
              <th>
                No. of employees/workers in respective category, who are part of
                association(s) or Union (D)
              </th>
              <th>% (D/C)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>Total Permanent Employees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Total Permanent Workers</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          8. Details of training given to employees and workers:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2}>Category</th>
              <th rowSpan={2}>Total (A)</th>
              <th colSpan={2}>On health &amp; safety/wellness measures</th>
              <th colSpan={2}>On skill upgradation</th>
              <th rowSpan={2}>Total (D)</th>
              <th colSpan={2}>On health and safety measures/wellness</th>
              <th colSpan={2}>On skill upgradation</th>
            </tr>
            <tr>
              <th>No. (B)</th>
              <th>% (B/A)</th>
              <th>No. (C)</th>
              <th>% (C/A)</th>
              <th>No. (E)</th>
              <th>% (E/D)</th>
              <th>No. F</th>
              <th>% (F/D)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={11} style={{ fontWeight: "bold" }}>
                EMPLOYEES
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td colSpan={11} style={{ fontWeight: "bold" }}>
                WORKERS
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          9. Details of performance and career development reviews of employees
          and workers.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2}>Category</th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2024-25
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2023-24
              </th>
            </tr>
            <tr>
              <th>Total (A)</th>
              <th>No. (B)</th>
              <th>% (B/A)</th>
              <th>Total (C)</th>
              <th>No. (D)</th>
              <th>% (D/C)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={7} style={{ fontWeight: "bold" }}>
                EMPLOYEES*
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td colSpan={7} style={{ fontWeight: "bold" }}>
                WORKERS
              </td>
            </tr>
            <tr>
              <td>Male</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Female</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          10. Health and Safety Management System
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          a) Whether an occupational health and safety management system has
          been implemented by the entity? (Yes/ No). If yes, the coverage of
          such system?
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          b) What are the processes used to identify work-related hazards and
          assess risks on a routine and non-routine basis by the entity?
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          c) Whether you have processes for workers to report work-related
          hazards and to remove themselves from such risks. (Yes/No)
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          d) Do the employees/workers of the entity have access to
          non-occupational medical and healthcare services? (Yes/No)
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          11. Details of safety-related incidents:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "40%" }}>Safety Incident/Number</th>
              <th>Category</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }} rowSpan={2}>
                Lost Time Injury Frequency Rate (LTIFR) (per one million
                person-hours worked)
              </td>
              <td>Employees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Workers</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td style={{ fontWeight: "bold" }} rowSpan={2}>
                Total recordable work-related injuries*
              </td>
              <td>Employees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Workers</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td style={{ fontWeight: "bold" }} rowSpan={2}>
                No. of fatalities
              </td>
              <td>Employees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Workers</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td style={{ fontWeight: "bold" }} rowSpan={2}>
                High-consequence work-related injury or ill-health (excluding
                fatalities)
              </td>
              <td>Employees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Workers</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          12. Describe the measures taken by the entity to ensure a safe and
          healthy workplace.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          13. Number of complaints on working conditions and health and safety
          made by employees and workers.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2}>Category</th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2024-25
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2023-24
              </th>
            </tr>
            <tr>
              <th>Filed during the year</th>
              <th>Pending resolution at the end of the year</th>
              <th>Remarks</th>
              <th>Filed during the year</th>
              <th>Pending resolution at the end of year</th>
              <th>Remarks</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Working Conditions</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Health &amp; Safety</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          14. Assessments for the year
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "40%" }}>&nbsp;</th>
              <th style={{ textAlign: "center" }}>
                % Of your plants and offices that were assessed (by entity or
                statutory authorities or third parties)
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Health and safety practices
              </td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Working Conditions</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          15. Provide details of any corrective action taken or underway to
          address safety-related incidents (if any) and on significant risks /
          concerns arising from assessments of health and safety practices and
          working conditions.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "5rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            textAlign: "center",
            color: "blue",
            marginBottom: "1rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Does the entity extend any life insurance or any compensatory
          package in the event of death of (A) Employees (Y/N) (B) Workers
          (Y/N)?
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold", width: "40%" }}>Employees</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Workers</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Provide the measures undertaken by the entity to ensure that
          statutory dues have been deducted and deposited by the value chain
          partners.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Provide the number of employees / workers having suffered high
          consequence work related injury / ill-health / fatalities (as reported
          in Q11 of Essential Indicators above), who have been are rehabilitated
          and placed in suitable employment or whose family members have been
          placed in suitable employment
        </p>

        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2}></th>
              <th colSpan={2} style={{ textAlign: "center" }}>
                Total no. of affected employees/workers
              </th>
              <th colSpan={2} style={{ textAlign: "center" }}>
                No. of employees/workers that are rehabilitated and placed in
                suitable employment or whose family members have been placed in
                suitable employment
              </th>
            </tr>
            <tr>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Employees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Workers</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. Does the entity provide transition assistance programs to
          facilitate continued employability and the management of career
          endings resulting from retirement or termination of employment? (Yes/
          No)
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          5. Details on assessment of value chain partners:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "40%" }}>&nbsp;</th>
              <th>
                % of value chain partners (by value of business done with such
                partners) that were assessed
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>
                Health &amp; Safety practices
              </td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>Working Conditions</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "3rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          6. Provide details of any corrective actions taken or underway to
          address significant risks / concerns arising from assessments of
          health and safety practices and working conditions of value chain
          partners.
        </p>
      </div>
    </div>
  );
};

export default PrincipleThree;
