import React from "react";

const CriticalNonCompliances = ({ count, onClick }) => {
  const containerStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    border: "1px solid black",
    borderRadius: "4px",
    padding: "10px",
    width: '90%',
    backgroundColor: "#f8f9fa",
    fontFamily: "Arial, sans-serif",
    fontSize: "14px",
    fontWeight: "bold",
    cursor: onClick ? "pointer" : "default",
    transition: "background-color 0.2s ease",
  };

  const containerHoverStyle = {
    ...containerStyle,
    backgroundColor: onClick ? "#e9ecef" : "#f8f9fa",
  };

  const circleStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "30px",
    height: "30px",
    borderRadius: "50%",
    backgroundColor: "red",
    color: "white",
    fontWeight: "bold",
    fontSize: "14px",
  };

  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <div
      style={isHovered ? containerHoverStyle : containerStyle}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={onClick ? "Click to view details" : ""}
    >
      <span>No. of critical regulatory non-compliances</span>
      <div style={circleStyle}>{count}</div>
    </div>
  );
};

export default CriticalNonCompliances;
